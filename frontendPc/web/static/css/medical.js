window.onload = function () {
var time = null;
var index = 0;
var pics = document.getElementsByClassName('bannerslide');
var rollBtn = document.getElementById('rollbtn');
var lis = rollBtn.getElementsByTagName('li');

function byId(id) {
return typeof (id) === 'string' ? document.getElementById(id) : id;
}

function slideImg() {
var rollImg = byId('rollimg');
var rollBanner = byId('rollbanner');
rollImg.onmouseover = function () {
stopAutoPlay();
}
rollImg.onmouseout = function () {
startAutoPlay();
}
rollImg.onmouseout();
for (var i = 0; i < pics.length; i++) {
lis[i].id = i;
lis[i].onmouseover = function () {
index = this.id;
changeImg();
}
}
}

function startAutoPlay() {
time = setInterval(function () {
index++;
if (index > pics.length - 1) {
index = 0;
}
changeImg();
}, 3000);
}

function stopAutoPlay() {
if (time) {
clearInterval(time);
}
}

function changeImg() {
for (var i = 0; i < pics.length; i++) {
pics[i].style.display = 'none';
lis[i].className = '';
}
pics[index].style.display = 'block';
lis[index].className = 'btnchange';
}
slideImg();

var tabMenu = document.getElementById('tabbox');
var tabLi = tabMenu.getElementsByTagName('li');
var tabCon = document.getElementById('tab_content');
var conList = tabCon.getElementsByClassName('content_list');
for (var i = 0; i < tabLi.length; i++) {
tabLi[i].index = i;
tabLi[i].onmouseover = function () {
for (var i = 0; i < tabLi.length; i++) {
tabLi[i].className = '';
conList[i].style.display = 'none';
}
this.className = 'tabact';
conList[this.index].style.display = 'block'
}
for (var m = 1; m < tabLi.length; m++) {
tabLi[m].className = '';
conList[m].style.display = 'none';
}
}
}