* {
    margin: 0;
    padding: 0;
    list-style: none;
    text-decoration: none;
    font-family: 'Microsoft YaHei';
    font-weight: normal;
    color: #666666;
    text-align: center;
    font-size: 16px;
}

.double_election,
.conference_header,
.header_banner {
    width: 100%;
    overflow: hidden;
    height: auto;
}

.header_banner img {
    width: 100%;
    height: auto;
}

.conference_nav {
    width: 1200px;
    height: 80px;
    margin: 0 auto;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: center;
    -webkit-align-items: center;
    position: relative;
    bottom: 50px;
}

.conference_nav li {
    flex-grow: 0;
    flex-shrink: 0;
    -webkit-flex-grow: 0;
    -webkit-flex-shrink: 0;
    width: 240px;
    height: 80px;
    line-height: 80px;
    background-color: #0071ba;
}

.conference_nav li:nth-child(2n) {
    background-color: #29a9e0;
}

.conference_li a {
    color: #fff;
    font-size: 20px;
    width: 100%;
    height: 100%;
    display: block;
}

.conference_li_banner1 {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/shuangxuanhui_icon01.png) 6px 3px no-repeat;
    background-size: contain;
}

.conference_li_banner2 {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/shuangxuanhui_icon02.png) 11px 3px no-repeat;
    background-size: contain;
}

.conference_li_banner3 {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/shuangxuanhui_icon03.png) 12px 3px no-repeat;
    background-size: contain;
}

.conference_li_banner4 {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/shuangxuanhui_icon04.png) 14px 3px no-repeat;
    background-size: contain;
}

.conference_li_banner5 {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/shuangxuanhui_icon005.png) 20px 3px no-repeat;
    background-size: contain;
}

.conference_preface_box {
    width: 1200px;
    margin: 0 auto;
}

.conference_time_tit {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon58.png) center center no-repeat;
    width: 600px;
    height: 50px;
    background-size: contain;
    margin: 0 auto 35px auto;
}

.conference_countdown {
    width: 800px;
    height: 130px;
    margin: 0 auto 40px auto;
    line-height: 70px;
    overflow: hidden;
}

.conference_time_text {
    font-size: 46px;
    color: #000;
    font-weight: bold;
    height: 100px;
    line-height: 100px;
    position: relative;
    z-index: 999;
}

.chdw_box {
    margin: 20px auto;
}

.date_text {
    height: 30px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: center;
    -webkit-align-items: center;
    z-index: 999999999;
    position: relative;
}

.date_text span {
    font-size: 16px;
    color: #000;
    margin-right: 120px;
}

.date_text span:nth-child(4) {
    margin-right: 0;
}

.time_box {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: center;
    -webkit-align-items: center;
    position: relative;
    bottom: 135px;
}

.time_box span {
    width: 110px;
    height: 110px;
    display: flex;
    align-items: flex-end;
    justify-content: center;
    display: -webkit-flex;
    -webkit-align-items: flex-end;
    -webkit-justify-content: center;
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon1.png) center center no-repeat;
    background-size: contain;
    margin-right: 30px;
}

.time_box span:nth-child(4) {
    margin-right: 0;
}

.conference_preface {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: center;
    -webkit-align-items: center;
}

.conference_preface_con {
    width: 600px;
    height: 420px;
    display: flex;
    align-items: center;
    display: -webkit-flex;
    -webkit-align-items: center;
    overflow: hidden;
}

.conference_preface_con img {
    width: 100%;
    height: 420px;
}

.conference_preface_text {
    flex-direction: column;
    justify-content: flex-start;
    -webkit-flex-direction: column;
    -webkit-justify-content: flex-start;
    background-color: #f0f9fd;
    width: 520px;
    height: 360px;
    padding: 29px 40px;
}

.conference_preface_text h2 {
    width: 145px;
    height: 40px;
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon07.png) center center no-repeat;
    background-size: contain;
    color: #0071ba;
    margin-right: 455px;
    font-size: 25px;
}

.conference_preface_text p {
    text-indent: 30px;
    line-height: 30px;
    text-align: left;
    margin-top: 60px;
    color: #777979;
}

.conference_eventoverview_box {
    width: 100%;
    height: auto;
}

.tit {
    width: 300px;
    height: 62px;
    color: #0071ba;
    margin: 0 auto;
    font-size: 32px;
    margin: 50px auto 30px auto;
}

.conference_eventoverview_tit {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon03.png) center center no-repeat;
    background-size: contain;
    width: 416px;
    height: 80px;
    line-height: 65px;
}

.conference_eventoverview_con {
    width: 100%;
    height: 640px;
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/shuangxuanhui_banner0001.jpg) center center no-repeat;

    display: flex;
    justify-content: flex-start;
    align-items: center;
    display: -webkit-flex;
    -webkit-justify-content: flex-start;
    -webkit-align-items: center;
}

.conference_eventoverview_text {
    width: 570px;
    margin-left: 14%;
}

.conference_eventoverview_text h3,
.conference_eventoverview_text p {
    color: #fff;
    text-align: left;
}

.conference_eventoverview_text h3 {
    font-size: 20px;
}

.conference_sponsor h3 {
    margin-top: 110px;
}

.conference_eventoverview_text p {
    margin: 10px auto;
    text-indent: 30px;
    line-height: 30px;
}

.conference_sponsor h3 a {
    color: #fff;
}

.conference_sponsor h3 a:hover {
    color: #f80908;
}

.conference_item_box {
    width: 1200px;
    margin: 0 auto;
}

.conference_item_tit {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon04.png) center center no-repeat;
    background-size: contain;
    width: 306px;
    height: 152px;
    line-height: 202px;
    overflow: hidden;
}

.tit_small {
    line-height: 30px;
    font-size: 20px;
}

.conference_item_list {
    width: 800px;
    margin: 50px auto;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: space-between;
    -webkit-align-items: center;
}

.conference_item_list li {
    width: 215px;
    height: 310px;
    border-radius: 50px;
    padding: 10px;
    cursor: pointer;
    overflow: hidden;
}

.list1 {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon008-2.png) center center no-repeat;
    background-size: contain;
}

.list2 {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon008-3.png) center center no-repeat;
    background-size: contain;
}

.list3 {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon008-1.png) center center no-repeat;
    background-size: contain;
}

.list1:hover {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon008-5.png) center center no-repeat;
    background-size: contain;
}

.list2:hover {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon008-6.png) center center no-repeat;
    background-size: contain;
}

.list3:hover {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon008-4.png) center center no-repeat;
    background-size: contain;
}

.conference_item_list li h4 {
    width: 136px;
    height: 75px;
    color: #666;
    font-size: 23px;
    line-height: 108px;
    margin: 55px auto 0 auto;
}

.conference_item_list p {
    font-size: 14px;
    line-height: 25px;
    text-align: left;
}

.conference_propaganda_box {
    width: 100%;
    height: 690px;
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/shuangxuanhui_banner014.png) center center no-repeat;

    overflow: hidden;
}

.conference_propaganda_tit {
    color: #fff;
    margin-top: 85px;
}

.conference_propaganda_box p {
    color: #fff;
    line-height: 30px;
}

.conference_propaganda_smalltit,
.conference_propaganda_list {
    width: 1000px;
    margin: 30px auto;
}

.conference_propaganda_list {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: space-between;
    -webkit-align-items: flex-start;
}

.conference_propaganda_list li {
    width: 199px;
}

.conference_propaganda_list li div {
    width: 101px;
    height: 101px;
    margin: 10px auto;
}

.propaganda_item_logo1 {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon019.png) center center no-repeat;
    background-size: contain;
}

.propaganda_item_logo2 {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon020.png) center center no-repeat;
    background-size: contain;
}

.propaganda_item_logo3 {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon021.png) center center no-repeat;
    background-size: contain;
}

.propaganda_item_logo4 {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon022.png) center center no-repeat;
    background-size: contain;
}

.conference_propaganda_list li p {
    text-align: left;
}

.propaganda_footer {
    width: 450px;
    margin: 0 auto;
}

.propaganda_footer p {
    border: 1px solid #fff;
    border-radius: 25px;
}

.propaganda_footer span {
    display: block;
    width: 50px;
    border-top: 1px solid #fff;
    margin: 22px auto 0 auto;
}

.conference_plan_box {
    width: 1200px;
    margin: 0 auto;
}

.conference_plan_tit {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon05.png) center center no-repeat;
    width: 303px;
    height: 62px;
    background-size: contain;
    overflow: hidden;
    line-height: 50px;
}

.conference_sort {
    width: 1200px;
    margin: 50px auto;
    overflow: hidden;
}

.sort_listbox {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-flex-wrap: wrap;
    -webkit-justify-content: space-between;
    -webkit-align-items: center;
    width: 100%;
    margin: 0 auto;
}

.sort_listbox li {
    width: 590px;
    height: 240px;
    margin-bottom: 30px;
    overflow: hidden;
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001chcc_icon10.png) center center no-repeat;
    display: flex;
    justify-content: center;
    align-items: center;
    display: -webkit-flex;
    -webkit-justify-content: center;
    -webkit-align-items: center;
}

.sort_inf {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: space-between;
    -webkit-align-items: center;
    width: 550px;
}

.logo_img,
.logo_img img {
    width: 200px;
    height: 200px;
}

.sort_text {
    width: 270px;
    margin-right: 45px;
}

.sort_text h2 {
    width: 270px;
    height: 40px;
    line-height: 40px;
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001chcc_icon15.png) center center no-repeat;
    color: #39a1ee;
    font-size: 24px;
}

.sort_text h3 {
    font-size: 27px;
    color: #fff;
    margin: 15px auto;
}

.sort_time {
    width: 270px;
    height: 90px;
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001chcc_icon11.png) center center no-repeat;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: column;
    -webkit-justify-content: center;
    -webkit-align-items: center;
}

.sort_time p{
    font-size: 22px;
    color: #fff;
}

.sort_time p:nth-child(1){
    margin-top: 20px;
}

.conference_object_tit {
    width: 358px;
    height: 70px;
    overflow: hidden;
    line-height: 52px;
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon06.png) center center no-repeat;
    background-size: contain;
    margin: 50px auto 30px auto;
    font-size: 32px;
    color: #0071ba;
}

.conference_company_box {

    background-color: #f0f9fd;
}

.conference_company {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: center;
    -webkit-align-items: center;
    margin: 0px auto;
}

.conference_company img {
    width: 600px;
    height: 390px;
}

.conference_company_text {
    width: 540px;
}

.text_right {
    padding-left: 60px;
    display: flex;
    align-items: flex-start;
    flex-direction: column;
    display: -webkit-flex;
    -webkit-align-items: flex-start;
    -webkit-flex-direction: column;
}

.text_left {
    padding-right: 60px;
    display: flex;
    align-items: flex-end;
    flex-direction: column;
    display: -webkit-flex;
    -webkit-align-items: flex-end;
    -webkit-flex-direction: column;
}

.bottom_border {
    width: 50px;
    display: block;
    border-top: 1px solid #666666;
    margin-top: 12px;
    margin-bottom: 20px;
}

.conference_company_text p {
    width: 330px;
    text-align: left;
    line-height: 30px;
}

.conference_company_text a {
    display: block;
    width: 100px;
    height: 30px;
    line-height: 30px;
    border: 1px solid #0071bc;
    border-radius: 25px;
    margin-top: 20px;
    color: #0071bc;
}

.position {
    position: absolute;
    left: 0;
    right: 0;
    height: 1132px;
    overflow: hidden;
}

.position.position_page {
    position: absolute;
    left: 0;
    right: 0;
    height: 653px;
    overflow: hidden;
}

.conference_mode_box {
    width: 860px;
    height: 844px;
    overflow: hidden;
    margin: 70px auto;
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/shuangxuanhui_banner00010.png) center center no-repeat;
    background-size: contain;
    padding: 0 70px;
    z-index: 9999;
    position: relative;
}





.conference_mode_box2 {
    width: 860px;
    height: 445px;
    overflow: hidden;
    margin: 30px auto;
    /* background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/shuangxuanhui_banner000010.png) center center no-repeat; */
    background-size: contain;
    padding: 0 70px;
    z-index: 9999;
    position: relative;
    background-color: #f0f9fd;
}


.conference_mode_tit {
    margin-top: 40px;
}

.conference_mode_company,
.conference_mode_person {
    text-align: left;
    font-size: 22px;
}

.conference_mode_person {
    margin-top: 20px;
}

.conference_mode_box p {
    text-align: left;
    line-height: 30px;
}

.conference_mode_box2 p {
    text-align: left;
    line-height: 30px;
}

.name_box {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: space-between;
}

.sign.up {
    font-weight: bold;
    color: #0071ba;
}


.conference_singup a:hover {
    color: #f80908;
}

.conference_singup_mode {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: space-between;
    -webkit-align-items: center;
}

.conference_mode_list {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: space-between;
    -webkit-align-items: center;
    -webkit-flex-wrap: wrap;
    width: 550px;
}

.conference_mode_list li {
    width: 248px;
    height: 28px;
    line-height: 30px;
    border: 1px solid #666;
    margin-top: 20px;
}

.conference_footer {
    width: 100%;
    height: 560px;
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/shuangxuanhui_banner009.jpg) center center no-repeat;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: column;
    -webkit-justify-content: center;
    -webkit-align-items: center;
    position: relative;
    bottom: 413px;
}

.conference_footer p,
.conference_footer p span {
    color: #fff;
    line-height: 50px;
}

.conference_footer p:nth-child(1) {
    margin-top: 340px;
}

.conference_footer p span {
    margin-left: 30px;
}

.conference_footer p span:nth-child(1) {
    margin-left: 0;
}

/* 2002��Ƹ��λ�б�ҳ�� */
.conference_companytab_box {
    width: 1200px;
    height: auto;
    margin: 0 auto;
}

.conference_companytab_tit {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon05.png) center center no-repeat;
    background-size: contain;
    width: 303px;
    height: 62px;
    line-height: 50px;
    margin-top: 0;
}

.conference_nav_box {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: flex-start;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: space-between;
    -webkit-align-items: flex-start;
}

.conference_tab_menu {
    width: 100%;
    height: 90px;
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: flex-end;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: flex-start;
    -webkit-align-items: flex-end;
}

.order_information {
    width: 808px;
    height: 58px;
    border: 1px solid #0071bc;
    line-height: 60px;
    color: #0071bc;
    font-size: 30px;
    margin-top: 5px;
    box-shadow: 0 1px 5px #888888;
}

.conference_tab_menu li {
    width: 111px;
    height: 85px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    display: -webkit-flex;
    -webkit-flex-direction: column;
    margin-left: 10px;
}

.conference_tab_menu li:nth-child(1) {
    margin-left: 0;
}

.date_time {
    width: 109px;
    height: 58px;
    border: 1px solid #0071ba;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: column;
    -webkit-justify-content: center;
    -webkit-align-items: center;
    box-shadow: 0 1px 5px #888888;
}

.date_time p,
.date_time span {
    color: #0071ba;
}

.date_time p {
    font-size: 20px;
}

.date_time span {
    font-size: 14px;
}

.delta {
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 0 20px 20px 20px;
    border-color: transparent transparent transparent transparent;
    margin: 5px auto 0 auto;
}

.act .date_time {
    background: #0071ba;
    height: 60px;
    border: none;
    width: 111px;
}

.act .date_time p,
.act .date_time span {
    color: #fff;
}

.act .date_time .border_top {
    display: block;
    width: 75px;
    border-top: 2px solid #00599f;
    position: relative;
    bottom: 8px;
}

.act .delta {
    border-bottom-color: #0071ba;
}

.tab_c {
    width: 100%;
    height: auto;
    display: none;
}

.conference_explain {
    width: 100%;
    height: 80px;
    line-height: 80px;
    font-size: 32px;
    color: #fff;
    background-color: #0071bc;
    box-shadow: 0 1px 5px #888888;
}

.sign_up_box {
    width: 45%;
    height: 110px;
    margin: 30px auto;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: space-between;
    -webkit-align-items: center;
}

.signup_list {
    font-size: 22px;
    width: 192px;
    height: 45px;
    line-height: 42px;
    background: #0071bc;
    color: #fff;
    box-shadow: 0 1px 5px #888888;
    border-radius: 12px;
    font-style: oblique;
}

.signup_list a {
    color: #fff;
    font-size: 20px;
    font-style: oblique;
}


.signup_list span {
    display: block;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 0 5px 10px;
    border-color: transparent transparent transparent #fff;
    float: right;
    margin: 18px 10px 18px 0;
}

.enroll_company_tit {
    width: 320px;
    height: 80px;
    line-height: 55px;
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon30.png) center center no-repeat;
    background-size: contain;
    margin-bottom: 50px;
}

.region {
    width: 100%;
    height: 47px;
    line-height: 45px;
    background-color: #29abe2;
    margin: 0 auto;
}

.region p {
    color: #fff;
    font-size: 25px;
    position: relative;
    background-color: #0071bc;
    margin: 0 auto;
    width: 250px;
}

.border_arrow {
    position: relative;
    display: block;
    width: 80px;
    border-top: 2px solid #00599f;
    bottom: 2px;
    margin: 0 auto;
}

.enroll_company_table {
    width: 100%;
    height: auto;
    margin-bottom: 50px;
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: flex-start;
    justify-content: flex-start;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-flex-wrap: wrap;
    -webkit-align-items: flex-start;
    -webkit-justify-content: flex-start;
}



.enroll_company_table li {
    width: 205px;
    height: 252px;
    border-radius: 50px;
    border: 1px solid #187ec1;
    padding: 24px 20px;
    margin-top: 32px;
    margin-left: 64px;
    overflow: hidden;

}

.enroll_company_table li:nth-child(4n+1) {
    margin-left: 0;
}



.enroll_list img {
    width: 109px;
    height: 108px;

    margin-top: -10px;
}

.enroll_list h2 {
    font-size: 18px;
    color: #0071bc;
    height: 52px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: column;
    -webkit-justify-content: center;
    -webkit-align-items: center;
}

.enroll_list p {
    font-size: 14px;
    color: #4d4d4d;
    line-height: 30px;
}

.list_button {
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-align-items: center;
    -webkit-justify-content: space-between;
    margin-top: 20px;
}

.enroll_list button {
    display: block;
    border: none;
    background-color: #0071bc;
    color: #fff;
    width: 100px;
    height: 30px;
    line-height: 30px;
    border-radius: 25px;
    font-size: 15px;
    font-style: oblique;
    cursor: pointer;
}

.list_button span {
    display: block;
    width: 0;
    height: 0;
    border-style: solid;
    border-width: 5px 0 5px 10px;
    border-color: transparent transparent transparent #fff;
    float: right;
    margin: 10px 5px 10px 0;
}

.open_win {
    border: none;
    background-color: #0071bc;
    color: #fff;
    width: 100px;
    height: 30px;
    line-height: 30px;
    border-radius: 25px;
    font-size: 15px;
    font-style: oblique;
    cursor: pointer;
}


.arrow {
    display: block;
    width: 80px;
    height: 5px;
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon32.png) center center no-repeat;
    background-size: contain;
    margin: 20px auto 0 auto;
}

.conference_alter {
    display: none;
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    background-color: rgba(11, 11, 11, 0.4);
    z-index: 999999;
}

.conference_wechat_box {
    width: 100%;
    height: 100%;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    background-color: rgba(11, 11, 11, 0.4);
    z-index: 999999;
    display: none;
}

.win_box {
    width: 1198px;
    height: 598px;
    border: 2px solid #0d78bf;
    margin: 100px auto;
    z-index: 999999;
    background-color: #fff;
    overflow-y: scroll;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 100px;
}

.close_win {
    width: 30px;
    height: 30px;
    cursor: pointer;
    left: 50%;
    margin-left: 540px;
    margin-top: 10px;
    position: fixed;

}

.close_win img {
    width: 100%;
    height: 30px;
}

.wechat_box {
    margin-top: 200px;
}

.wechat_img {
    width: 540px;
    height: 375px;
    padding: 10px 30px;
    background-color: #0071ba;
    border-radius: 70px;
    margin: 20px auto;
    box-shadow: 20px 20px 0 #29a9e0;
    overflow: hidden;
}

.wechat_box_logo {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: space-between;
    -webkit-align-items: center;
    width: 85%;
    margin: 0 auto;
}

.wechat_box_logo img {
    width: 250px;
    height: 70px;
}

.wechat_box_logo span {
    display: block;
    width: 100px;
    border-top: 5px dotted #fff;
}

.wechat_text {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: row;
    -webkit-justify-content: space-between;
    -webkit-align-items: center;
    width: 477px;
    margin: 60px auto 0 auto;
    padding-bottom: 50px;
    border-bottom: 5px dotted #fff;
}

.inf_text {
    width: 260px;
}

.inf_text h1,
.inf_text p {
    font-size: 25px;
    line-height: 50px;
    height: 50px;
    font-weight: bold;
    color: #fff;
    width: 215px;
    border: 2px solid #fff;
    padding: 0 20px;
}

.inf_text p {
    font-size: 43px;
    border: none;
    padding: 0;
    width: 259px
}

.wechat_text img {
    width: 150px;
    height: 150px;
}

.close_wechat {
    width: 30px;
    height: 30px;
    border-radius: 70px;
    border: 1px solid #fff;
    margin: 50px auto;
    cursor: pointer;
}

.close_wechat img {
    width: 100%;
    height: 30px;
    margin-top: 0;
}

.article_box {
    width: 80%;
    margin: 50px auto 50px auto;
}

.article_box h1 {
    width: 100%;
    line-height: 30px;
    border-bottom: 1px solid #999999;
    font-size: 20px;
    color: #0071bc;
    font-weight: bold;
}

.article_box h2,
.article_box p,
.article_box h4 {
    text-align: left;
}

.article_box h2,
.article_box h4 {
    color: #0071bc;
    line-height: 30px;
    font-weight: bold;
}

.article_box h2 {
    margin-top: 50px;
}

.article_box h4 {
    margin-top: 30px;
}

.position_page .conference_mode_box {
    background: none;
    background-color: #f9f9f9;
}

.position_page .conference_mode_company,
.position_page .conference_mode_person {
    width: 210px;
    height: 30px;
    margin: 40px auto 0 auto;
    color: #0071ba;
    font-size: 25px;
    text-align: center;
}

.position_page .bottom_border {
    background: url(http://zt.gaoxiaojob.com/zhaopinhui2002/zhaopinhui2001_icon26.png) center center no-repeat;
    border: none;
    height: 2px;
    margin: 12px auto 20px auto;
}

.conference_mode_wechat {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    display: -webkit-flex;
    -webkit-flex-direction: column;
    -webkit-justify-content: center;
    -webkit-align-items: center;
}