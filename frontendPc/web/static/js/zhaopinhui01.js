window.onload = function () {

    //����ʱ
    var timeBox = document.getElementById('date');

    function countDown(times) {
        var today = new Date().getTime();
        if (today >= times) {
            return {
                countdownHtml: '˫ѡ�����ڽ�����',
                flag: true
            }
        }
        var timeDiff = times - today;
        var d = parseInt(timeDiff / 1000 / 60 / 60 / 24, 10);
        var h = parseInt(timeDiff / 1000 / 60 / 60 % 24, 10);
        var M = parseInt(timeDiff / 1000 / 60 % 60, 10);
        var s = parseInt(timeDiff / 1000 % 60, 10);
        var seperator1 = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
        var seperator2 = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
        var seperator3 = "&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;";
        var seperator4 = "";
        d = d < 10 ? "0" + d : d;
        h = h < 10 ? "0" + h : h;
        M = M < 10 ? "0" + M : M;
        s = s < 10 ? "0" + s : s;
        var countdown = d + seperator1 + h + seperator2 + M + seperator3 + s + seperator4
        return {
            countdownHtml: countdown,
            flag: false
        }
    }
    var times = new Date('2020/5/22 9:00').getTime();
    var count = countDown(times);
    timeBox.innerHTML = count.countdownHtml;
    var interval = setInterval(function () {
        var count = countDown(times);
        if (count.flag) {
            clearInterval(interval);
        }
        timeBox.innerHTML = count.countdownHtml;
    }, 1000)



    //��꾭���¼�
    eVent('list1', 'onmouseover');
    eVent('list2', 'onmouseover');
    eVent('list3', 'onmouseover');

    function eVent(id, event) {
        var List = document.getElementById(id);
        List[event] = function () {
            var overh4 = document.getElementById(id).getElementsByTagName('h4')[0];
            var overp = document.getElementById(id).getElementsByTagName('p')[0];
            overh4.style.color = '#fff';
            overp.style.color = '#fff';
        }
    }

    function oneVent(id, onevent) {
        var Listout = document.getElementById(id);
        Listout[onevent] = function () {
            var outh4 = document.getElementById(id).getElementsByTagName('h4')[0];
            var outp = document.getElementById(id).getElementsByTagName('p')[0];
            outh4.style.color = '#666666';
            outp.style.color = '#666';
        }
    }
    oneVent('list1', 'onmouseout');
    oneVent('list2', 'onmouseout');
    oneVent('list3', 'onmouseout');

    //ҳ�������Ч
    var wow = new WOW({
        boxClass: 'wow',
        animateClass: 'animated',
        offset: 0,
        mobile: true,
        live: true
    });
    wow.init();

}