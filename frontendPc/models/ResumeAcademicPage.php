<?php

namespace frontendPc\models;

use common\base\models\BaseDictionary;
use common\base\models\BaseMember;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAcademicPage;
use common\helpers\ArrayHelper;
use common\helpers\IpHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use yii\base\BaseObject;
use yii\base\Exception;

class ResumeAcademicPage extends BaseResumeAcademicPage
{
    /**
     * 获取学术论文列表
     * @param $memberId
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getPageList($memberId): array
    {
        $list = self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->select([
                'id',
                'serial_number as serialNumber',
                'title',
                'position',
                'publish_date as publishDate',
                'record_situation as recordSituation',
                'impact_factor as impactFactor',
                'description',
            ])
            //            ->limit(self::LIMIT_NUM)
            ->orderBy('publish_date desc')
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            $item['positionText'] = BaseDictionary::getPaperRankName($item['position']);
            $item['publishDate']  = TimeHelper::formatToYearMonth($item['publishDate']);
        }

        return $list;
    }

    /**
     * 保存/编辑信息
     * @param $data
     * @throws Exception
     */
    public static function saveInfo($data)
    {
        $title           = $data['title'];
        $serialNumber    = $data['serialNumber'];
        $publishDate     = $data['publishDate'];
        $recordSituation = $data['recordSituation'];
        $position        = $data['position'];
        $impactFactor    = $data['impactFactor'];
        $description     = $data['description'];

        //判断参数
        if (strlen($title) < 1 || strlen($serialNumber) < 1 || strlen($publishDate) < 1 || strlen($recordSituation) < 1 || mb_strlen($description,
                'UTF-8') > 500) {
            throw new Exception('数据出错!');
        }

        //判断是新增还是修改
        if (!empty($data['id'])) {
            //修改
            $model    = self::findOne($data['id']);
            $memberId = $model->member_id;
            if (empty($model) || $model->status != self::STATUS_ACTIVE) {
                throw new Exception('该学术论文记录不存在');
            }
        } else {
            //新增
            $memberId = $data['memberId'];

            $model = new self();

            $model->resume_id = BaseMember::getMainId($memberId);
            $model->member_id = $memberId;
        }

        $model->title            = $title;
        $model->serial_number    = $serialNumber;
        $model->publish_date     = TimeHelper::formatAddDay($publishDate);
        $model->record_situation = $recordSituation;
        $model->position         = $position;
        $model->impact_factor    = $impactFactor;
        $model->description      = $description;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //更新简历完成度表
        ResumeComplete::updateResumeCompleteInfo($memberId);
        //新增操作日志
        $log_data = [
            'content' => '保存简历学术论文信息，memberId：' . $memberId,
        ];
        // 写日志
        BaseMemberActionLog::log($log_data);
    }

    /**
     * 删除论文
     * @param $id
     * @throws Exception
     */
    public static function delPage($id, $memberId)
    {
        $model = self::findOne($id);
        if ($model->member_id != $memberId) {
            throw new Exception('该记录与当前用户信息不匹配');
        }

        $model->status = self::STATUS_DELETE;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //更新简历完成度表
        ResumeComplete::updateResumeCompleteInfo($memberId);

        //新增操作日志
        $data = [
            'content' => '删除学术论文id：' . $id,
        ];
        // 写登录日志
        BaseMemberActionLog::log($data);
    }

}