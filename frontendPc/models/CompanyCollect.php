<?php

namespace frontendPc\models;

use common\base\models\BaseCompanyCollect;
use common\base\models\BaseJobApply;
use common\helpers\StringHelper;
use yii\base\Exception;
use yii\helpers\Url;

class CompanyCollect extends BaseCompanyCollect
{
    /**
     * 获取收藏单位数量
     * @param $memberId
     * @return bool|int|string|null
     */
    public static function getAmount($memberId)
    {
        return intval(self::find()
            ->where(['member_id' => $memberId])
            ->andWhere(['status' => self::STATUS_ACTIVE])
            ->count());
    }

    /**
     * 获取单位收藏列表
     * @param $memberId
     * @param $searchData
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getList($searchData, $needPageInfo = false)
    {
        $query = self::find()
            ->alias('cc')
            ->leftJoin(['c' => Company::tableName()], 'c.id=cc.company_id')
            ->where(['cc.member_id' => $searchData['memberId']])
            ->andWhere(['cc.status' => self::STATUS_ACTIVE]);

        $query->select([
            'cc.id',
            'c.id as companyId',
            'c.full_name as companyName',
            'c.logo_url as companyLogo',
            'c.province_id',
            'c.city_id',
            'c.type',
            'c.label_ids',
            'c.member_id as companyMemberId',
            'cc.add_time',
        ]);
        $count = $query->count();

        $pageSize = $searchData['pageSize'] ?: \Yii::$app->params['defaultPageSize'];

        $pages = self::setPage($count, $searchData['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('cc.update_time desc')
            ->asArray()
            ->all();

        foreach ($list as $k => &$record) {
            // 这里对logo做一个保底设置
            if (empty($record['companyLogo'])) {
                $record['companyLogo'] = \Yii::$app->params['defaultCompanyLogo'];
            }
            //获取地区名
            $areaName           = Area::getAreaName($record['province_id']) . '-' . Area::getAreaName($record['city_id']);
            $record['areaName'] = StringHelper::subtractString($areaName, '-');

            //获取单位标签
            $record['companyLabel'] = [];
            if (!empty($record['label_ids'])) {
                $labelArr = explode(',', $record['label_ids']);
                foreach ($labelArr as $key => $v) {
                    array_push($record['companyLabel'], Dictionary::getCompanyLabelName($v));
                }
            }
            //获取单位类型
            $record['companyType'] = Dictionary::getCompanyTypeName($record['type']);
            //获取单位在招职位数量
            $record['jobAmount'] = Job::getCompanyJobAmount($record['companyId']);
            //获取单位公告数量
            $record['announcementAmount'] = Announcement::getCompanyOnLineAnnouncementAmount($record['companyId']);
            //获取单位简历查看率
            $record['resumeViewRate'] = BaseJobApply::statCompanyViewingRate(['company_member_id' => $record['companyMemberId']]);

            $record['url'] = Url::toRoute([
                'company/detail',
                'id' => $record['companyId'],
            ]);
        }

        if ($needPageInfo) {
            return [
                'list' => $list,
                'page' => [
                    'count' => intval($count),
                    'limit' => intval($pages['limit']),
                    'page'  => intval($searchData['page']),
                ],
            ];
        } else {
            return $list;
        }
    }

    /**
     * 修改收藏状态
     * @param $memberId
     */
    public static function changeStatus($memberId)
    {
        $model = self::findOne(['member_id' => $memberId]);
        if ($model->status == self::STATUS_ACTIVE) {
            $model->status = self::STATUS_DELETE;
        } elseif ($model->status == self::STATUS_DELETE) {
            $model->status = self::STATUS_ACTIVE;
        }
        $model->save();
    }

}