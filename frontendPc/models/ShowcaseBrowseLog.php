<?php

namespace frontendPc\models;

use common\base\models\BaseShowcaseBrowseLog;
use queue\Producer;

class ShowcaseBrowseLog extends BaseShowcaseBrowseLog
{
    /**
     * 新增广告统计数据
     */
    public static function addShowcaseBrowseLog($request, $useragent)
    {
        $data = array_merge($request, [
            'useragent'    => $useragent,
            'company_id'   => $request['company_id'] ?: '0',
            'company_name' => $request['company_name'] ?: '',
        ]);
        if (!$request['number']) {
            // 非法
            return;
        }

        if (!$request['id']) {
            // 非法
            return;
        }

        Producer::showcaseBrowseLog($data);
    }

}
