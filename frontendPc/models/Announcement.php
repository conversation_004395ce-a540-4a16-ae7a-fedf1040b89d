<?php

namespace frontendPc\models;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementEdit;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseCompanyPackageConfig;
use common\base\models\BaseCompanyPackageSystemConfig;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomeColumnDictionaryRelationship;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseJobTemp;
use common\base\models\BaseJobWelfareRelation;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseWelfareLabel;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use common\helpers\UUIDHelper;
use common\libs\Cache;
use common\libs\CompanyAuthority\CompanyAuthorityClassify;
use common\service\column\RecommendJobService;
use common\service\companyAuth\ButtonGroupAuthService;
use Yii;
use yii\base\Exception;
use yii\db\conditions\AndCondition;

class Announcement extends BaseAnnouncement
{
    const DETAIL_CACHE_TIME = 1800;

    public static function getDetailUrl($id)
    {
        return UrlHelper::toRoute([
            '/announcement/detail',
            'id' => $id,
        ]);
    }

    /**
     * 获取栏目页公告数据（一级）模版A+B
     * @param     $keywords
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public static function columnAnnouncementList($keywords): array
    {
        $config = Yii::$app->params['firstLevelColumn']['announcement'];

        $list = [];
        foreach ($config as $k => $item) {
            $limit             = $item['count'];
            $keywords['limit'] = $limit;
            $keywords['key']   = $k;

            switch ($k) {
                case 'right_announcement_classification':
                case 'latest_announcement_tiled':
                    if ($keywords['level'] == 1) {
                        $tapList          = BaseAnnouncement::getHomeSubColumnIdsList($keywords);
                        $data             = [];
                        $announcementList = [];
                        foreach ($tapList as $value) {
                            $keywords['columnId']      = $value['id'];
                            $columnAnnouncementTopList = BaseAnnouncement::getColumnAnnouncementTopList($value['id']);
                            $outIds                    = array_column($columnAnnouncementTopList, 'id');
                            $keywords['outIds']        = $outIds;
                            $keywords['limit']         = $limit - sizeof($columnAnnouncementTopList);
                            $columnAnnouncementList    = BaseAnnouncement::getColumnAnnouncementList($keywords);
                            $announcementList[]        = array_merge($columnAnnouncementTopList,
                                $columnAnnouncementList);
                        }

                        $data['tapList'] = $tapList;
                    } else {
                        $data               = [];
                        $announcementList   = [];
                        $announcementList[] = BaseAnnouncement::getColumnAnnouncementList($keywords);
                    }
                    $data['list'] = $announcementList;
                    break;
                case 'right_hot_announcement':
                    $data = BaseAnnouncement::getRecommendAnnouncementList($keywords);
                    break;
                default:
                    $data = BaseAnnouncement::getColumnAnnouncementList($keywords);
                    break;
            }

            $list[$k] = $data;
        }

        return $list;
    }

    /**
     * 获取栏目公告下职位(一级栏目)
     * 推荐职位、最新职位、热门职位
     * @param     $keywords
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    //    public static function getColumnJob($keywords): array
    //    {
    //        $config   = Yii::$app->params['firstLevelColumn']['job'];
    //        $list     = [];
    //        $partList = [
    //            'recommend',
    //            'newest',
    //            'hot',
    //        ];
    //        $tapList  = BaseAnnouncement::getHomeColumnJobIdsList($keywords);
    //        foreach ($config as $k => $item) {
    //            $keywords['limit'] = $item['count'];
    //            $keywords['key']   = $k;
    //
    //            $data = [];
    //            foreach ($partList as $value) {
    //                $announcementList = [];
    //                if ($value == 'recommend') {
    //                    $keywords1 = array_merge($keywords, [
    //                        'type' => BaseArticleAttribute::COLUMN_RECOMMEND_ATTRIBUTE,
    //                    ]);
    //                    foreach ($tapList as $s) {
    //                        $keywords1['job_category_id'] = $s['job_category_id'];
    //                        $announcementList[]           = BaseAnnouncement::getColumnJobList($keywords1);
    //                    }
    //                }
    //
    //                if ($value == 'hot') {
    //                    $keywords2 = array_merge($keywords, [
    //                        'sort_hot' => 1,
    //                    ]);
    //                    foreach ($tapList as $s) {
    //                        $keywords2['job_category_id'] = $s['job_category_id'];
    //                        $announcementList[]           = BaseAnnouncement::getColumnJobList($keywords2);
    //                    }
    //                }
    //
    //                if ($value == 'newest') {
    //                    foreach ($tapList as $s) {
    //                        $keywords['job_category_id'] = $s['job_category_id'];
    //                        $announcementList[]          = BaseAnnouncement::getColumnJobList($keywords);
    //                    }
    //                }
    //
    //                $data[$value]['list']    = $announcementList;
    //                $data[$value]['tapList'] = $tapList;
    //            }
    //
    //            $list[$k] = $data;
    //        }
    //
    //        return $list;
    //    }
    /**
     * 获取栏目公告下职位(一级栏目)
     * @return array|mixed
     */
    public static function getColumnJob($columnId)
    {
        //这里用作获取省区栏目和普通一级栏目的分发获取职位列表，由于政府事业单位的栏目比较特殊，这里不混在一起操作了
        $service = new RecommendJobService();
        $service->setColumnId($columnId);

        //        $jobList = $service->getOtherColumnJobList();
        $jobList = $service->distribute();

        return $jobList;
    }

    /**
     * 获取公告详情页数据
     * @param $id
     * @param $isDelCache 是否清除缓存重新设置
     * @return array|mixed|\yii\db\ActiveRecord|null
     * @throws \Exception
     */
    public static function getDetailInfo($id, $isDelCache = false)
    {        // 先找缓存里面是否有

        $cacheKey = Cache::PC_ANNOUNCEMENT_DETAIL_KEY . ':' . $id;
        //需要清空缓存重新设置
        if ($isDelCache) {
            Cache::delete($cacheKey);
        }
        $cacheData = Cache::get($cacheKey);
        if ($cacheData) {
            $info = json_decode($cacheData, true);
        } else {
            $info = self::find()
                ->alias('an')
                ->innerJoin(['a' => BaseArticle::tableName()], 'a.id=an.article_id')
                ->innerJoin(['c' => BaseCompany::tableName()], 'an.company_id = c.id')
                ->where(['an.id' => $id])
                ->select([
                    'an.id',
                    'article_id',
                    'is_delete',
                    'is_show',
                    'template_id',
                    'an.title',
                    'an.file_ids',
                    'a.status',
                    'an.add_time',
                    'c.full_name as companyName',
                    'c.id as companyId',
                    'a.refresh_time as refreshTime',
                    'a.content',
                    'a.click',
                    'a.release_time',
                    'a.id as articleId',
                    'an.period_date as periodDate',
                    'c.logo_url as logoUrl',
                    'c.english_name as englishName',
                    'c.type',
                    'c.scale',
                    'c.nature',
                    'c.member_id as companyMemberId',
                    'a.home_column_id',
                    'an.apply_type',
                    'an.delivery_type',
                    'an.delivery_way',
                    'c.industry_id',
                    'c.is_cooperation',
                    'a.seo_keywords',
                    'a.seo_description',
                    'is_cooperation',
                    'establishment_type as establishmentType',
                ])
                ->asArray()
                ->one();

            if (!$info) {
                return [];
            }

            //获取公告在招职位数量
            $info['jobAmount'] = BaseJob::getAnnouncementJobAmount($id);
            //获取公告招聘人
            $info['jobRecruitAmount'] = BaseJob::getAnnouncementJobRecruitAmount($id);
            //获取公告下所有省份
            $info['provinceName'] = self::getAllProvinceName($id);
            $info['provinceList'] = self::getAllProvinceList($id);
            //获取公告下的所有城市
            $info['cityName'] = self::getAllCityName($id);
            $info['cityList'] = self::getAllCityList($id);
            //获取公告下福利
            $info['allWelfareLabel'] = self::getAllWelfareLabel($id);
            if ($info['periodDate'] == TimeHelper::ZERO_TIME) {
                $info['periodDate'] = '详见正文';
            } else {
                $info['periodDate'] = date('Y-m-d', strtotime($info['periodDate']));
            }
            if ($info['refreshTime'] == TimeHelper::ZERO_TIME) {
                $info['fullRefreshTime'] = $info['add_time'];
                $info['refreshTime']     = date('Y-m-d', strtotime($info['add_time']));;
            } else {
                $info['fullRefreshTime'] = $info['refreshTime'];
                $info['refreshTime']     = date('Y-m-d', strtotime($info['refreshTime']));
            }
            //简历查看率
            $info['resumeViewRate'] = BaseJobApply::statCompanyViewingRate(['company_id' => $info['companyId']]);
            //单位最后登录时间
            $info['lastLoginTime'] = BaseMemberActionLog::getCompanyLastLoginDateText($info['companyMemberId']);
            //获取公告下的最低学历要求
            $info['minEducation'] = trim(BaseJob::getAnnouncementJobEducationType($id));
            //获取公告下的专业列表
            $info['majorName'] = self::getAllMajorName($id, 'array');
            //获取公告下所有专业名称-为了不影响底层逻辑  有些位置再用防止影响到 这里特殊替换一下
            $info['majorNameText'] = self::getAllMajorName($id);
            if ($info['majorNameText']) {
                $info['majorNameText'] = str_replace(',', '，', $info['majorNameText']);
            }
            //获取栏目的名称
            $info['columnName'] = HomeColumn::findOneVal(['id' => $info['home_column_id']], 'name');
            //单位logo
            $info['companyLogo'] = Company::getLogoFullUrl($info['logoUrl']);
            //单位行业
            $info['companyTrade'] = Trade::getIndustryName($info['industry_id']);
            //单位规模
            $info['companyScale'] = Dictionary::getCompanyScaleName($info['scale']);
            //单位类型
            $info['companyType'] = Dictionary::getCompanyTypeName($info['type']);
            //获取单位在招职位数量
            $info['companyJobRecruitAmount'] = BaseJob::getCompanyJobAmount($info['companyId']);
            //获取单位在招公告数量
            $info['companyAnnouncementRecruitAmount'] = self::getCompanyOnLineAnnouncementAmount($info['companyId']);
            //获取栏目信息
            $info['columnInfo'] = BaseHomeColumn::getInfoListByAnnouncementId($info['id']);
            //获取报名方式
            ////获取公告职位信息
            $job_list        = BaseJob::find()
                ->select('delivery_way,delivery_type,apply_type,apply_address,is_show')
                ->andWhere(['announcement_id' => $info['id']])
                ->andWhere(['is_show' => BaseJob::IS_SHOW_YES])
                ->andWhere([
                    'status' => [
                        BaseJob::STATUS_OFFLINE,
                        BaseJob::STATUS_ONLINE,
                    ],
                ])
                ->asArray()
                ->all();
            $jobApplyTypeArr = [];
            //处理一下没有跟公告就不拿
            $announcement_bool = false;
            foreach ($job_list as $item) {
                if (!empty($item['delivery_type'])) {
                    if ($item['delivery_type'] == BaseJob::DELIVERY_TYPE_OUTER) {
                        $applyTypeList = explode(',', $item['apply_type']);
                        foreach ($applyTypeList as $type_item) {
                            array_push($jobApplyTypeArr, BaseDictionary::getSignUpName($type_item));
                        }
                    } else {
                        array_push($jobApplyTypeArr, '站内投递');
                    }
                } else {
                    if (!$announcement_bool) {
                        $announcement_bool = true;
                    }
                }
            }
            $announcementApplyTypeArr = [];
            if ($info['delivery_type'] > 0 && $announcement_bool) {
                if ($info['delivery_type'] == BaseAnnouncement::DELIVERY_TYPE_OUTER) {
                    $announcementApplyTypeTxt = BaseJob::getApplyTypeName($info['apply_type']);
                    $announcementApplyTypeArr = explode(',', $announcementApplyTypeTxt);
                } else {
                    $announcementApplyTypeArr = ['站内投递'];
                }
            }
            $applyTypeArr                  = array_merge($announcementApplyTypeArr, $jobApplyTypeArr);
            $applyTypeArr                  = array_unique($applyTypeArr);
            $info['applyTypeText']         = implode(',', $applyTypeArr);
            $info['announcementUrl']       = UrlHelper::createAnnouncementDetailPath($info['id']);
            $info['companyUrl']            = UrlHelper::createCompanyDetailPath($info['companyId']);
            $info['establishmentTypeText'] = '';
            if ($info['establishmentType']) {
                $info['establishmentTypeText'] = self::ESTABLISHMENT_TEXT_LIST[$info['establishmentType']];
            }

            Cache::set($cacheKey, json_encode($info), self::DETAIL_CACHE_TIME);
        }

        return $info;
    }

    /**
     * 获取栏目页公告数据（二级级栏目）
     * @param     $keywords
     * @return array
     * @throws \Exception
     */
    public static function secondColumnAnnouncementList($keywords): array
    {
        $config = Yii::$app->params['secondLevelColumn']['announcement'];

        $list = [];
        foreach ($config as $k => $item) {
            $data['pageSize'] = $item['count'];
            if ($k != "latest_announcement") {
                $limit             = $item['count'];
                $keywords['limit'] = $limit;
                $keywords['key']   = $k;
                $data['list']      = BaseAnnouncement::getSecondColumnAnnouncementList($keywords);
            }

            $list[$k] = $data;
        }

        return $list;
    }

    /**
     * 获取栏目公告下职位(二级栏目)
     * 最新职位
     * @param     $keywords
     * @return array
     * @throws \Exception
     */
    public static function getSecondColumnJob($keywords): array
    {
        $config = Yii::$app->params['secondLevelColumn']['job'];
        $list   = [];

        foreach ($config as $k => $item) {
            $keywords['key']   = $k;
            $keywords['limit'] = $item['count'];

            $data = BaseAnnouncement::changeSecondColumnJobList($keywords);

            $list[$k] = $data;
        }

        return $list;
    }

    public static function getSecondColumnJobByType($keywords, $type, $id): array
    {
        $config = Yii::$app->params['secondLevelColumn']['job'];
        $list   = [];

        switch ($type) {
            case BaseHomeColumnDictionaryRelationship::TYPE_MAJOR:
                $keywords['major_id'] = $id;
                break;
            case BaseHomeColumnDictionaryRelationship::TYPE_AREA:
                break;
        }

        foreach ($config as $k => $item) {
            $keywords['key']   = $k;
            $keywords['limit'] = $item['count'];

            $data = BaseAnnouncement::changeSecondColumnJobList($keywords);

            $list[$k] = $data;
        }

        return $list;
    }

    /**
     * 获取栏目页公告数据（政府与事业单位/年度招聘）
     * @param     $keywords
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public static function governmentColumnAnnouncementList($keywords): array
    {
        $config = Yii::$app->params['governmentColumn']['announcement'];
        $list   = [];

        foreach ($config as $k => $item) {
            $limit             = $item['count'];
            $keywords['limit'] = $limit;
            $keywords['key']   = $k;

            switch ($k) {
                case 'government_latest_announcement':
                    $announcementList = [];
                    $count            = 0;
                    if ($keywords['level'] == 1) {
                        $tapList = BaseAnnouncement::getHomeSubColumnIdsList($keywords);

                        foreach ($tapList as $value) {
                            $keywords['columnId']      = $value['id'];
                            $columnAnnouncementTopList = BaseAnnouncement::getColumnAnnouncementTopList($value['id']);
                            $outIds                    = array_column($columnAnnouncementTopList, 'id');
                            $keywords['outIds']        = $outIds;
                            $keywords['limit']         = $limit - sizeof($columnAnnouncementTopList);
                            $columnAnnouncementList    = BaseAnnouncement::getColumnAnnouncementList($keywords);
                            $temp                      = array_merge($columnAnnouncementTopList,
                                $columnAnnouncementList);
                            $announcementList[]        = $temp;
                            $count                     += sizeof($temp);
                        }

                        $data['tapList'] = $tapList;
                    } else {
                        $data               = [];
                        $temp               = BaseAnnouncement::getGovernmentColumnAnnouncementList($keywords);
                        $announcementList[] = $temp;
                        $count              += sizeof($temp);
                    }

                    $data['list']  = $announcementList;
                    $data['count'] = $count;
                    break;
                case 'right_hot_announcement':
                    $data = BaseAnnouncement::getRecommendAnnouncementList($keywords);
                    break;
                default:
                    $data = BaseAnnouncement::getGovernmentColumnAnnouncementList($keywords);
                    break;
            }

            $list[$k] = $data;
        }

        return $list;
    }

    //    /**
    //     * 获取栏目公告下职位(政府与事业单位/专题)
    //     * 最新职位
    //     * @param     $keywords
    //     * @return array
    //     * @throws Exception
    //     * @throws \Exception
    //     */
    //    public static function getGovernmentColumnJob1($keywords): array
    //    {
    //        $config   = Yii::$app->params['governmentColumn']['job'];
    //        $list     = [];
    //        $partList = [
    //            'recommend',
    //            'newest',
    //            'hot',
    //        ];
    //
    //        foreach ($config as $k => $item) {
    //            $keywords['limit'] = $item['count'];
    //            $keywords['key']   = $k;
    //            $keywords['page']  = $keywords['page'] ?: 1;
    //            $data              = [];
    //            foreach ($partList as $key => $value) {
    //                $announcementList = [];
    //                if ($value == 'recommend') {
    //                    $keywords1        = array_merge($keywords, [
    //                        'type' => BaseArticleAttribute::COLUMN_RECOMMEND_ATTRIBUTE,
    //                    ]);
    //                    $announcementList = BaseAnnouncement::getGovernmentColumnJobList($keywords1);
    //                }
    //
    //                if ($value == 'hot') {
    //                    $keywords2        = array_merge($keywords, [
    //                        'sort_hot' => 1,
    //                    ]);
    //                    $announcementList = BaseAnnouncement::getGovernmentColumnJobList($keywords2);
    //                }
    //
    //                if ($value == 'newest') {
    //                    $announcementList = BaseAnnouncement::getGovernmentColumnJobList($keywords);
    //                }
    //
    //                $data[$key] = $announcementList;
    //            }
    //            $list[$k] = $data;
    //        }
    //
    //        return $list;
    //    }

    /**
     * 获取栏目公告下职位(政府与事业单位/专题)
     * 最新职位
     * @param     $keywords
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public static function getGovernmentColumnJob(): array
    {
        $config   = Yii::$app->params['governmentColumn']['job'];
        $list     = [];
        $partList = [
            'newest',
            'hot',
        ];
        foreach ($config as $k => $item) {
            $data = [];

            $service  = new RecommendJobService();
            $listInfo = $service->getGovernmentJobList();
            foreach ($partList as $key => $value) {
                $jobList = [];

                if ($value == 'hot') {
                    $jobList = $listInfo['hotList'];
                }

                if ($value == 'newest') {
                    $jobList = $listInfo['newList'];
                }

                $data[$key] = $jobList;
            }
            $list[$k] = $data;
        }

        return $list;
    }

    /**
     * 获取栏目公告下职位(省区栏目)
     * 推荐职位、最新职位、热门职位
     * @param     $keywords
     * @return array
     * @throws Exception
     */
    //    public static function getProvinceColumnJob($keywords): array
    //    {
    //        $config   = Yii::$app->params['provinceColumn']['job'];
    //        $list     = [];
    //        $partList = [
    //            'recommend',
    //            'newest',
    //            'hot',
    //        ];
    //
    //        foreach ($config as $k => $item) {
    //            $limit             = $item['count'];
    //            $keywords['limit'] = $limit;
    //            $keywords['key']   = $k;
    //
    //            $data    = [];
    //            $tapList = BaseAnnouncement::getHomeColumnJobIdsList($keywords);
    //            foreach ($partList as $value) {
    //                $announcementList = [];
    //                if ($value == 'recommend') {
    //                    $keywords1 = array_merge($keywords, [
    //                        'type' => BaseArticleAttribute::COLUMN_RECOMMEND_ATTRIBUTE,
    //                    ]);
    //                    foreach ($tapList as $s) {
    //                        $keywords1['job_category_id'] = $s['job_category_id'];
    //                        $announcementList[]           = BaseAnnouncement::getColumnJobList($keywords1);
    //                    }
    //                }
    //
    //                if ($value == 'hot') {
    //                    $keywords2 = array_merge($keywords, [
    //                        'sort_hot' => 1,
    //                    ]);
    //                    foreach ($tapList as $s) {
    //                        $keywords2['job_category_id'] = $s['job_category_id'];
    //                        $announcementList[]           = BaseAnnouncement::getColumnJobList($keywords2);
    //                    }
    //                }
    //
    //                if ($value == 'newest') {
    //                    foreach ($tapList as $s) {
    //                        $keywords['job_category_id'] = $s['job_category_id'];
    //                        $announcementList[]          = BaseAnnouncement::getColumnJobList($keywords);
    //                    }
    //                }
    //                $data[$value]['list']    = $announcementList;
    //                $data[$value]['tapList'] = $tapList;
    //            }
    //
    //            $list[$k] = $data;
    //        }
    //
    //        return $list;
    //    }

    /**
     * 获取省区栏目职位模块
     * @param $columnId
     * @return array|mixed
     */
    public static function getProvinceColumnJob($columnId)
    {
        $service = new RecommendJobService();
        $service->setColumnId($columnId);
        $jobList = $service->getAreaJobList();

        return $jobList;
    }

    //    /**
    //     * 获取省区栏目职位模块
    //     * @param $columnId
    //     * @return array|mixed
    //     */
    //    public static function getProvinceColumnPageJob($params)
    //    {
    //        if($params[''])
    //
    //        $cacheKey  = Cache::PC_COLUMN_GOVERNMENT_SELECT_JOB_PAGE_KEY.'_' . $params['page'];;
    //        $cacheData = Cache::get($cacheKey);
    //        if (!$cacheData) {
    //            return json_decode($cacheKey, true);
    //        } else {
    //            $service = new RecommendJobService();
    //            $service->setColumnId($columnId);
    //            $jobList = $service->getAreaJobList();
    //
    //            return $jobList;
    //        }
    //    }
    /**
     * 获取省区栏目职位模块
     * @param $columnId
     * @return array|mixed
     */
    public static function getProvinceColumnPageJob($columnId)
    {
        $cacheKey  = Cache::PC_COLUMN_GOVERNMENT_SELECT_JOB_KEY;
        $cacheData = Cache::get($cacheKey);
        if ($cacheData) {
            return json_decode($cacheKey, true);
        } else {
            $service = new RecommendJobService();
            $service->setColumnId($columnId);
            $jobList = $service->getAreaJobList();

            return $jobList;
        }
    }

    /**
     * 获取栏目页公告数据（省区栏目）
     * @param     $keywords
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public static function provinceColumnAnnouncementList($keywords): array
    {
        $config = Yii::$app->params['provinceColumn']['announcement'];

        $list = [];

        foreach ($config as $k => $item) {
            $limit             = $item['count'];
            $keywords['limit'] = $limit;
            $keywords['key']   = $k;

            switch ($k) {
                case 'right_announcement_classification':
                    if ($keywords['level'] == 1) {
                        $tapList          = BaseAnnouncement::getHomeSubColumnIdsList($keywords);
                        $data             = [];
                        $announcementList = [];
                        foreach ($tapList as $value) {
                            $keywords['columnId']      = $value['id'];
                            $columnAnnouncementTopList = BaseAnnouncement::getColumnAnnouncementTopList($value['id']);
                            $outIds                    = array_column($columnAnnouncementTopList, 'id');
                            $keywords['outIds']        = $outIds;
                            $keywords['limit']         = $limit - sizeof($columnAnnouncementTopList);
                            $columnAnnouncementList    = BaseAnnouncement::getColumnAnnouncementList($keywords);
                            $announcementList[]        = array_merge($columnAnnouncementTopList,
                                $columnAnnouncementList);
                        }

                        $data['tapList'] = $tapList;
                    } else {
                        $data               = [];
                        $announcementList   = [];
                        $announcementList[] = BaseAnnouncement::getColumnAnnouncementList($keywords);
                    }
                    $data['list'] = $announcementList;
                    break;
                case 'right_hot_announcement':
                    $data = BaseAnnouncement::getProvinceRecommendAnnouncementList($keywords);
                    break;
                default:
                    $data = BaseAnnouncement::getProvinceColumnAnnouncementList($keywords);
                    break;
            }

            $list[$k] = $data;
        }

        return $list;
    }

    /**
     * 获取栏目公告下职位(博士后栏目)
     * 推荐职位、最新职位、热门职位
     * @param     $keywords
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public static function getPostDoctorColumnJob($keywords): array
    {
        $config   = Yii::$app->params['postDoctorColumn']['job'];
        $list     = [];
        $partList = [
            'recommend',
            'newest',
            'hot',
        ];

        foreach ($config as $k => $item) {
            $limit             = $item['count'];
            $keywords['limit'] = $limit;
            $keywords['key']   = $k;

            $data    = [];
            $tapList = BaseAnnouncement::getHomeColumnJobIdsList($keywords);
            foreach ($partList as $value) {
                $announcementList = [];
                if ($value == 'recommend') {
                    $keywords1 = array_merge($keywords, [
                        'type' => BaseArticleAttribute::COLUMN_RECOMMEND_ATTRIBUTE,
                    ]);
                    foreach ($tapList as $s) {
                        $keywords1['job_category_id'] = $s['job_category_id'];
                        $announcementList[]           = BaseAnnouncement::getColumnJobList($keywords1);
                    }
                }

                if ($value == 'hot') {
                    $keywords2 = array_merge($keywords, [
                        'sort_hot' => 1,
                    ]);
                    foreach ($tapList as $s) {
                        $keywords2['job_category_id'] = $s['job_category_id'];
                        $announcementList[]           = BaseAnnouncement::getColumnJobList($keywords2);
                    }
                }

                if ($value == 'newest') {
                    foreach ($tapList as $s) {
                        $keywords['job_category_id'] = $s['job_category_id'];
                        $announcementList[]          = BaseAnnouncement::getColumnJobList($keywords);
                    }
                }
                $data[$value]['list']    = $announcementList;
                $data[$value]['tapList'] = $tapList;
            }

            $list[$k] = $data;
        }

        return $list;
    }

    /**
     * 获取栏目页公告数据（博士后栏目）
     * @param     $keywords
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public static function PostDoctorColumnAnnouncementList($keywords): array
    {
        $config = Yii::$app->params['postDoctorColumn']['announcement'];

        $list = [];

        foreach ($config as $k => $item) {
            $limit             = $item['count'];
            $keywords['limit'] = $limit;
            $keywords['key']   = $k;

            switch ($k) {
                case 'right_announcement_classification':
                    $tapList          = BaseAnnouncement::getHomeSubColumnIdsList($keywords);
                    $data             = [];
                    $announcementList = [];
                    foreach ($tapList as $value) {
                        $keywords['columnId']      = $value['id'];
                        $columnAnnouncementTopList = BaseAnnouncement::getColumnAnnouncementTopList($value['id']);
                        $outIds                    = array_column($columnAnnouncementTopList, 'id');
                        $keywords['outIds']        = $outIds;
                        $keywords['limit']         = $limit - sizeof($columnAnnouncementTopList);
                        $columnAnnouncementList    = BaseAnnouncement::getColumnAnnouncementList($keywords);
                        $announcementList[]        = array_merge($columnAnnouncementTopList, $columnAnnouncementList);
                    }

                    $data['tapList'] = $tapList;
                    $data['list']    = $announcementList;
                    break;
                case 'right_hot_announcement':
                    $data = BaseAnnouncement::getRecommendAnnouncementList($keywords);
                    break;
                default:
                    $data = BaseAnnouncement::getPostDoctorColumnAnnouncementList($keywords);
                    break;
            }

            $list[$k] = $data;
        }

        return $list;
    }

    /**
     * 获取公告是否是合作单位发布的
     * @param $id
     * @return mixed
     */
    public static function checkCooperation($id)
    {
        $info = self::find()
            ->alias('a')
            ->leftJoin(['c' => Company::tableName()], 'c.id=a.company_id')
            ->select(['c.is_cooperation'])
            ->where(['a.id' => $id])
            ->asArray()
            ->one();
        if ($info['is_cooperation'] == Company::COOPERATIVE_UNIT_YES) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 把内容格式化成PC详情页面显示
     * @param $content
     */
    public static function formatContent($content)
    {
        preg_match_all('/<h5[^>]*([\s\S]*?)<\/h5>/i', $content, $matches);
        foreach ($matches[0] as $match) {
            // 去掉html
            $txt     = strip_tags($match);
            $h1      = '<h2 class="detail-title"><span>' . $txt . '</span></h2>';
            $content = str_replace($match, $h1, $content);
        }

        return $content;
    }

    // 2.1.1版本做的功能，这里和之前不太一样，h5是需要变成div，并且有一套逻辑
    // 1、通过识别“标题H5”格式的内容，作为模块标题，采用模块标题样式显示；
    //
    // 2、两个“标题H5”之间的内容，作为模块的正文内容展示，显示模块正文样式，如       ；
    //
    // 3、若富文本的头部内容，和第一个“标题H5”格式之间存在内容，则该内容直接显示为模块正文样式，无模块标题，如        ；
    //
    // 4、若两个“标题H5”之间无内容，则直接显示模块标题，模块正文内容显示为空，如         ；
    //
    // 5、富文本的的最后一个“标题H5”格式，至文末区域的内容，视为一整个模块，显示对应的模块标题样式&模块正文样式，
    public static function formatContentV2($content)
    {
        // 获取所有的h5标签
        preg_match_all('/<h5[^>]*([\s\S]*?)<\/h5>/i', $content, $matches);

        // 每两个h5标签之间的内容用一个div=common-wrapper包裹
        foreach ($matches[0] as $match) {
            // 去掉html
            $txt     = strip_tags($match);
            $h1      = '</div></div><div class="common-wrapper"><div class="common-title"><div class="common-title-content"><h2>' . $txt . '</h2></div></div><div class="common-content">';
            $content = str_replace($match, $h1, $content);
        }

        $content = '<div class="common-wrapper"><div class="common-content">' . $content . '</div></div>';

        // 找到第一个common-content里面的内容
        preg_match_all('/<div class="common-content">([\s\S]*?)<\/div>/i', $content, $matches);
        $div1Base = $matches[1][0];

        // 去掉里面全部的html标签(除了img)
        $div1 = strip_tags($div1Base, '<img>');
        // 去掉空格
        // aa($content);

        $div1 = str_replace(' ', '', $div1);
        if (empty($div1)) {
            $replace = '<div class="common-wrapper"><div class="common-content">' . $div1Base . '</div></div>';
            $content = str_replace($replace, '', $content);
        }

        // 再做一个保底，去掉所有没有用的空内容，只留标题
        $content = str_replace('<div class="common-content"></div>', '', $content);

        return $content;
    }

    /**
     * 获取栏目页公告数据（一级）切换
     * @param     $keywords
     * @return array
     * @throws \Exception
     */
    public static function changeColumnAnnouncementList($keywords): array
    {
        $config            = Yii::$app->params['firstLevelColumn']['announcement'][$keywords['key']];
        $keywords['limit'] = $config['count'];

        if ($keywords['key'] == 'right_hot_announcement') {
            return BaseAnnouncement::getRecommendAnnouncementList($keywords);
        } else {
            return BaseAnnouncement::getColumnAnnouncementList($keywords);
        }
    }

    /**
     * 获取栏目页公告数据（政府与事业单位/专题栏目）切换
     * @param     $keywords
     * @return array
     * @throws \Exception
     */
    public static function changGovernmentColumnAnnouncementList($keywords): array
    {
        $config            = Yii::$app->params['governmentColumn']['announcement'][$keywords['key']];
        $keywords['limit'] = $config['count'];

        if ($keywords['key'] == 'right_hot_announcement') {
            return BaseAnnouncement::getRecommendAnnouncementList($keywords);
        } else {
            return BaseAnnouncement::getColumnAnnouncementList($keywords);
        }
    }

    /**
     * 获取栏目页公告数据（省区栏目）切换
     * @param     $keywords
     * @return array
     * @throws \Exception
     */
    public static function changeProvinceColumnAnnouncementList($keywords): array
    {
        $config            = Yii::$app->params['provinceColumn']['announcement'][$keywords['key']];
        $keywords['limit'] = $config['count'];

        if ($keywords['key'] == 'right_hot_announcement') {
            return BaseAnnouncement::getProvinceRecommendAnnouncementList($keywords);
        } else {
            return BaseAnnouncement::getColumnAnnouncementList($keywords);
        }
    }

    /**
     * 获取栏目页公告数据（博士后栏目）切换
     * @param     $keywords
     * @return array
     * @throws \Exception
     */
    public static function changePostDoctorColumnAnnouncementList($keywords): array
    {
        $config            = Yii::$app->params['postDoctorColumn']['announcement'][$keywords['key']];
        $keywords['limit'] = $config['count'];

        if ($keywords['key'] == 'right_hot_announcement') {
            return BaseAnnouncement::getRecommendAnnouncementList($keywords);
        } else {
            return BaseAnnouncement::getColumnAnnouncementList($keywords);
        }
    }

    /**
     * 获取栏目公告下职位(切换)通用
     * 推荐职位、最新职位、热门职位
     * @param     $keywords
     * @return array
     * @throws \Exception
     */
    public static function changeColumnJob($keywords): array
    {
        $keywords['limit'] = 12;

        return BaseAnnouncement::getColumnJobList($keywords);
    }

    /**
     * 公告列表
     * @param $keywords
     * @return array
     * @throws \yii\base\Exception
     */
    public static function getList($keywords): array
    {
        $select = [
            'a.id',
            'a.add_time',
            'a.member_id',
            'a.company_id',
            'a.title',
            'a.audit_status',
            'a.offline_type',
            'a.create_type',
            'a.creator_id',
            'a.period_date',
            'a.offline_type',
            'a.offline_reason',
            'a.offline_time',
            'a.home_sort',
            'a.delivery_way',
            'ar.status',
            'ar.click',
            'ar.release_time',
            'ar.apply_audit_time',
            'ar.delete_time',
            'ar.refresh_time',
            'ar.first_release_time',
            'ar.real_refresh_time',
            //            'j.id as job_id',
            //            'j.is_article',
            //            'j.name',
            //            'j.is_stick',
            //            'j.code',
            //            'j.job_category_id',
            //            'j.education_type',
            //            'j.nature_type',
            //            'j.is_negotiable',
            //            'j.wage_type',
            //            'j.min_wage',
            //            'j.max_wage',
            //            'j.experience_type',
            //            'j.age_type',
            //            'j.title_type',
            //            'j.political_type',
            //            'j.abroad_type',
            //            'j.amount',
            //            'j.department',
            //            'j.district_id',
            //            'j.province_id',
            //            'j.city_id',
            //            'j.welfare_tag',
            //            'j.duty',
            //            'j.requirement',
            //            'j.remark',
            //            'j.announcement_id',
            //            'j.gender_type',
            //            'j.announcement_id',
            'SUM(j.amount) as amountNum',
            'COUNT(j.announcement_id) as jobNum',
        ];

        $memberId = $keywords['member_id'];
        $query    = self::find()
            ->alias('a')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'ar.id=a.article_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.announcement_id=a.id')
            ->select($select)
            ->where([
                'ar.is_show' => BaseArticle::IS_SHOW_YES,
                //'a.member_id' => $memberId,
            ])
            ->andWhere([
                '<>',
                'ar.is_delete',
                BaseArticle::STATUS_ACTIVE,
            ])
            ->groupBy('a.id');

        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField'  => 'a.company_id',
            'memberId'         => $memberId,
            'query'            => $query,
            'returnType'       => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
            'companyAuthority' => 'announcementList',
        ]);

        $companyAuthorityList = [];
        if ($authorityList) {
            $query                = $authorityList['query'];
            $companyAuthorityList = $authorityList['companyAuthorityList'];
        }

        //todo 查询条件
        if ($keywords['column']) {
            switch ($keywords['column']) {
                case 1:
                    $query->andFilterCompare('ar.status', BaseArticle::STATUS_ONLINE);
                    $query->andFilterCompare('ar.first_release_time', TimeHelper::ZERO_TIME, '<>');
                    break;
                case 2:
                    $statusList = [
                        self::STATUS_AUDIT_STAGING,
                        self::STATUS_AUDIT_AWAIT,
                        self::STATUS_AUDIT_REFUSE,
                    ];
                    $query->andFilterCompare('a.audit_status', $statusList, 'in');
                    $query->andFilterCompare('a.create_type', BaseAnnouncement::CREATE_TYPE_COMPANY);
                    $query->andFilterCompare('ar.first_release_time', TimeHelper::ZERO_TIME);
                    break;
                case 3:
                    $query->andFilterCompare('ar.status', BaseArticle::STATUS_OFFLINE);
                    break;
                case 4:
                    $statusList = [
                        self::STATUS_AUDIT_STAGING,
                        self::STATUS_AUDIT_AWAIT,
                        self::STATUS_AUDIT_REFUSE,
                    ];
                    $status     = [
                        BaseArticle::STATUS_ONLINE,
                        BaseArticle::STATUS_OFFLINE,
                    ];
                    $query->andWhere([
                        'or',
                        [
                            'and',
                            [
                                'in',
                                'ar.status',
                                $status,
                            ],
                            [
                                '<>',
                                'ar.first_release_time',
                                TimeHelper::ZERO_TIME,
                            ],
                        ],
                        [
                            'and',
                            [
                                'in',
                                'a.audit_status',
                                $statusList,
                            ],
                            [
                                '=',
                                'a.create_type',
                                BaseAnnouncement::CREATE_TYPE_COMPANY,
                            ],
                        ],
                    ]);
                    break;
                default:
            }
        }

        if ($keywords['name']) {
            if (is_numeric($keywords['name']) && strlen($keywords['name']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['name']);
                $query->andFilterCompare('job_id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('concat(j.name,j.id )', $keywords['name'], 'like');
            }
        }

        if ($keywords['announcement']) {
            if (is_numeric($keywords['announcement']) && strlen($keywords['announcement']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['announcement']);
                $query->andFilterCompare('a.id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('concat(a.title,a.id )', $keywords['announcement'], 'like');
            }
        }

        if ($keywords['release_start_time']) {
            $query->andWhere([
                '>=',
                'ar.release_time',
                TimeHelper::dayToBeginTime($keywords['release_start_time']),
            ]);
        }
        if ($keywords['release_end_time']) {
            $query->andWhere([
                '<=',
                'ar.release_time',
                TimeHelper::dayToEndTime($keywords['release_end_time']),
            ]);
        }

        $query->andFilterCompare('j.status', BaseJob::STATUS_DELETE, '<>');
        $query->andFilterCompare('a.audit_status', $keywords['audit_status']);
        $query->andFilterCompare('a.offline_type', $keywords['offline_type']);

        $orderBy = ' a.add_time desc';
        if ($keywords['sort_release_time']) {
            $sort    = $keywords['sort_release_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' ar.release_time ' . $sort;
        }
        if ($keywords['sort_refresh_time']) {
            $sort    = $keywords['sort_refresh_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' ar.real_refresh_time ' . $sort;
        }
        if ($keywords['sort_period_date']) {
            $sort    = $keywords['sort_period_date'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' a.period_date ' . $sort;
        }
        if ($keywords['sort_home_sort']) {
            $sort    = $keywords['sort_home_sort'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' a.home_sort ' . $sort;
        }

        $count            = $query->count();
        $pageSize         = $keywords['page_size'] ?: Yii::$app->params['default_page_size'];
        $pages            = self::setPage($count, $keywords['page'], $pageSize);
        $announcementList = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        // 日期时间格式数据数组
        $timeArray = [
            'add_time',
            'period_date',
            'refresh_time',
            'release_time',
            'offline_time',
            'delete_time',
            'apply_audit_time',
            'real_refresh_time',
            'first_release_time',
        ];

        // 相关数据数组准备
        $statusList      = BaseArticle::STATUS_LIST;
        $auditStatusList = self::STATUS_AUDIT_LIST;
        $experienceList  = BaseDictionary::getExperienceList();
        $offlineTypeList = self::OFFLINE_TYPE_NAME;

        // 单位权益信息
        $refreshIntervalDay = 0;
        $releaseIntervalDay = 0;
        $refreshAmount      = 0;
        $releaseAmount      = 0;
        $dateTime           = CUR_DATETIME;
        //$companyId            = BaseCompany::findOneVal(['member_id' => $memberId], 'id');
        $companyMemberInfo    = BaseCompanyMemberInfo::findOne(['member_id' => $memberId]);
        $companyPackageConfig = BaseCompanyPackageConfig::getCompanyPackageConfig($companyMemberInfo['company_id']);

        if ($companyPackageConfig) {
            $refreshIntervalDay = $companyPackageConfig['job_refresh_interval_day '];
            $releaseIntervalDay = $companyPackageConfig['job_release_interval_day '];
            if ($companyPackageConfig['companyRole'] == BaseCompanyPackageConfig::COMPANY_ROLE_EXPIRE) {
                $refreshAmount = 0;
                $releaseAmount = 0;
            } else {
                $refreshAmount = $companyPackageConfig['job_refresh_amount'];
                $releaseAmount = $companyPackageConfig['job_release_amount'];
            }
        }

        foreach ($announcementList as $k => $list) {
            if ($list['first_release_time'] != TimeHelper::ZERO_TIME) {
                $announcementList[$k]['announcementUrl'] = UrlHelper::createAnnouncementDetailPath($list['id']);
            }

            $announcementList[$k]['wage'] = BaseJob::formatWage($list['min_wage'], $list['max_wage'],
                $list['wage_type']);
            // 经验要求
            if ($list['experience_type'] == -1) {
                $announcementList[$k]['experience_type_title'] = '不限';
            } elseif ($list['experience_type'] == 0) {
                $announcementList[$k]['experience_type_title'] = '未填';
            } else {
                $announcementList[$k]['experience_type_title'] = $experienceList[$list['experience_type']];
            }
            $announcementList[$k]['status_title']       = $statusList[$list['status']];
            $announcementList[$k]['audit_status_title'] = ($list['audit_status'] == 0) ? '未填' : $auditStatusList[$list['audit_status']];
            $announcementList[$k]['offline_type_title'] = $offlineTypeList[$list['offline_type']];

            // 职位投递统计
            $jobApplyList                                   = BaseJobApply::announcementJobApplyStatistics($list['id']);
            $announcementList[$k]['unread_account']         = $jobApplyList['unreadApplyNum'] ?: 0;
            $announcementList[$k]['wait_interview_account'] = $jobApplyList['jobInterviewNum'] ?: 0;
            $announcementList[$k]['all_apply_account']      = $jobApplyList['allJobApplyNum'] ?: 0;
            $announcementList[$k]['outApplyAccount']        = $jobApplyList['linkApplyNum'] ?: 0;
            $announcementList[$k]['isPlatformOrEmailApply'] = $jobApplyList['isPlatformOrEmailApply'];
            $announcementList[$k]['isLinkApply']            = $jobApplyList['isLinkApply'];

            // 公告下的所有职位
            $jobIdArr = self::getAnnouncementJobIds($list['id']);
            $jobList  = BaseJob::find()
                ->where([
                    'in',
                    'id',
                    $jobIdArr,
                ])
                ->andWhere([
                    'status'  => [
                        BaseJob::STATUS_ONLINE,
                        BaseJob::STATUS_OFFLINE,
                        BaseJob::STATUS_WAIT,
                    ],
                    'is_show' => BaseJob::IS_SHOW_YES,
                ])
                ->asArray()
                ->all();

            //获取地区表缓存
            $cache     = Yii::$app->cache;
            $areaCache = $cache->get(Cache::PC_ALL_AREA_TABLE_KEY);
            if (!$areaCache) {
                $areaCache = BaseArea::setAreaCache();
            }

            $educationArr  = [];
            $cityArr       = [];
            $amountNumList = [];
            $amountNum     = 0;
            $jobNum        = 0;
            foreach ($jobList as $job) {
                $educationArr[]  = $job['education_type'];
                $cityArr[]       = $areaCache[$job['city_id']]['name'] ?: '-';
                $amountNumList[] = $job['amount'];
                $amountNum       = $amountNum + $job['amount'];
                $jobNum++;
            }

            if (in_array('若干', $amountNumList)) {
                $announcementList[$k]['amountNum'] = '若干';
            } else {
                $announcementList[$k]['amountNum'] = $amountNum;
            }

            // 学历要求
            if (count($educationArr) > 1) {
                $educationMin = min($educationArr);
                $education    = BaseDictionary::getEducationName($educationMin);

                $announcementList[$k]['education_type_title'] = !empty($education) ? BaseDictionary::getEducationName($educationMin) . ($educationMin == BaseDictionary::EDUCATION_DOCTOR_ID ? '' : '及以上') : '未填';
            } else {
                $announcementList[$k]['education_type_title'] = !empty($educationArr) ? BaseDictionary::getEducationName($educationArr[0]) . ($educationArr[0] == BaseDictionary::EDUCATION_DOCTOR_ID ? '' : '及以上') : '未填';
            }

            // 城市
            if (count($cityArr) > 1) {
                $announcementList[$k]['city'] = implode('、', array_unique($cityArr));
            } else {
                $announcementList[$k]['city'] = $cityArr[0] ?: '';
            }

            //todo 刷新、发布的限制
            if ($refreshAmount == 0) {
                $announcementList[$k]['is_allow_refresh']       = 2;
                $announcementList[$k]['is_allow_refresh_title'] = "当前公告不可刷新";
            } else {
                if (TimeHelper::reduceDates($dateTime, $list['refresh_time']) < $refreshIntervalDay) {
                    $announcementList[$k]['is_allow_refresh']       = 2;
                    $announcementList[$k]['is_allow_refresh_title'] = "当前公告不可刷新";
                } else {
                    $announcementList[$k]['is_allow_refresh']       = 1;
                    $announcementList[$k]['is_allow_refresh_title'] = "当前公告可刷新";
                }
            }

            if ($releaseAmount == 0) {
                $announcementList[$k]['is_allow_release']       = 2;
                $announcementList[$k]['is_allow_release_title'] = "当前公告不可发布";
            } else {
                if (TimeHelper::reduceDates($dateTime, $list['release_time']) < $releaseIntervalDay) {
                    $announcementList[$k]['is_allow_release']       = 2;
                    $announcementList[$k]['is_allow_release_title'] = "当前公告不可发布";
                } else {
                    $announcementList[$k]['is_allow_release']       = 1;
                    $announcementList[$k]['is_allow_release_title'] = "当前公告可发布";
                }
            }

            // TODO 这里获取审核拒绝
            $announcementList[$k]['opinion'] = '';

            if ($list['audit_status'] == self::STATUS_AUDIT_REFUSE) {
                $handleLog = BaseAnnouncementHandleLog::find()
                    ->where([
                        'announcement_id' => $list['id'],
                        'handler_type'    => BaseAnnouncementHandleLog::HANDLER_TYPE_PLAT,
                        'handle_type'     => BaseAnnouncementHandleLog::HANDLE_TYPE_AUDIT,
                    ])
                    ->limit(1)
                    ->orderBy('id desc')
                    ->one();

                if ($handleLog) {
                    $handleAfter                     = json_decode($handleLog['handle_after'], true);
                    $announcementList[$k]['opinion'] = $handleAfter['opinion'] ?: '';
                }
            }
            // 有效日期
            $announcementList[$k]['periodAttention'] = "";
            if (strtotime($list['period_date']) < 1) {
                if (strtotime($list['release_time']) < 1) {
                    $releaseTime = CUR_DATETIME;
                } else {
                    $releaseTime = $list['release_time'];
                }

                // 没有填截止日期，默认给发布时间后的365天
                $periodDate          = date('Y-m-d H:i:s',
                    strtotime($releaseTime) + 60 * 60 * 24 * self::ADD_RELEASE_AGAIN_DAY);
                $list['period_date'] = $periodDate;
                $periodDay           = floor((strtotime($periodDate) - time()) / 60 / 60 / 24);
                if ($periodDay > 0 && $periodDay < (self::ATTENTION_PERIOD_DAY + 1)) {
                    $announcementList[$k]['periodAttention'] = $periodDay . "天后下线";
                }
            }
            // 日期时间类型处理
            foreach ($timeArray as $item) {
                if (strtotime($list[$item]) < 1) {
                    $announcementList[$k][$item] = "";
                } else {
                    $announcementList[$k][$item] = TimeHelper::formatDateByYear($list[$item]);
                }
            }

            //            // 发布时间
            //            if (TimeHelper::countYears($list['release_time']) < 1) {
            //                $announcementList[$k]['release_time'] = date('m/d', strtotime($list['release_time']));
            //            } else {
            //                if (strtotime($list['release_time']) < 1) {
            //                    $announcementList[$k]['release_time'] = '-';
            //                } else {
            //                    $announcementList[$k]['release_time'] = date('Y/m/d', strtotime($list['release_time']));
            //                }
            //            }
            //            // 刷新时间
            //            if (TimeHelper::countYears($list['real_refresh_time']) < 1) {
            //                $announcementList[$k]['real_refresh_time'] = date('m/d', strtotime($list['real_refresh_time']));
            //            } else {
            //                if (strtotime($list['real_refresh_time']) < 1) {
            //                    $announcementList[$k]['real_refresh_time'] = '-';
            //                } else {
            //                    $announcementList[$k]['real_refresh_time'] = date('Y/m/d', strtotime($list['real_refresh_time']));
            //                }
            //            }
            //            // 下线时间
            //            if (TimeHelper::countYears($list['offline_time']) < 1) {
            //                $announcementList[$k]['offline_time'] = date('m/d', strtotime($list['offline_time']));
            //            } else {
            //                if (strtotime($list['offline_time']) < 1) {
            //                    $announcementList[$k]['offline_time'] = '-';
            //                } else {
            //                    $announcementList[$k]['offline_time'] = date('Y/m/d', strtotime($list['offline_time']));
            //                }
            //            }

            $announcementList[$k]['jobNum'] = $jobNum;

            //公告职位协同信息
            $announcementList[$k]['jobContactSynergyNum'] = 0;
            if ($companyMemberInfo['company_member_type'] == BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_SUB) {
                $announcementList[$k]['jobContactSynergyNum'] = BaseJobContactSynergy::find()
                    ->where([
                        'announcement_id'        => $announcementList[$k]['id'],
                        'company_member_info_id' => $companyMemberInfo['id'],
                    ])
                    ->groupBy('job_id,company_member_info_id')
                    ->count();
            }

            //按钮组
            $announcementList[$k]['buttonGroup'] = (new ButtonGroupAuthService())->setType(ButtonGroupAuthService::TYPE_ANNOUNCEMENT)
                ->run($list['id']);

            //公告过期时间
            if ($list['status'] == BaseArticle::STATUS_ONLINE || ($list['status'] == BaseArticle::STATUS_OFFLINE && $list['offline_time'] == '-')) {
                $announcementList[$k]['offline_time'] = TimeHelper::formatDateByYear($list['period_date']);
            }
        }

        return [
            'list'                 => $announcementList ?: [],
            'page'                 => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
            'companyAuthorityList' => $companyAuthorityList,
        ];
    }

    /**
     * 获取列表状态数量统计
     * @return array
     * @throws Exception
     */
    public static function getColumnAmount(): array
    {
        $memberId    = Yii::$app->user->id;
        $companyInfo = BaseCompany::getCompanyInfo($memberId);
        if (!$companyInfo) {
            throw new Exception('非法访问');
        }

        $where = [
            'a.company_id' => $companyInfo['id'],
            //'a.member_id'  => $memberId,
            'ar.is_show'   => Article::IS_SHOW_YES,
        ];
        $list  = [];
        //column=1 在线
        $queryOne = self::find()
            ->alias('a')
            ->leftJoin(['ar' => Article::tableName()], 'ar.id=a.article_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.announcement_id=a.id')
            ->select('a.id')
            ->where([
                '<>',
                'ar.is_delete',
                self::STATUS_ACTIVE,
            ])
            ->andWhere($where)
            ->groupBy('a.id');

        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'a.company_id',
            'memberId'        => Yii::$app->user->id,
            'query'           => $queryOne,
            'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
        ]);

        if ($authorityList) {
            $queryOne = $authorityList['query'];
        }

        $queryOne->andFilterCompare('j.status', BaseJob::STATUS_DELETE, '<>');
        $queryOne->andFilterCompare('ar.status', self::STATUS_ONLINE);
        $queryOne->andFilterCompare('ar.first_release_time', TimeHelper::ZERO_TIME, '<>');
        $list['column_1'] = $queryOne->count();

        //column=2 待发布
        $queryTwo = self::find()
            ->alias('a')
            ->leftJoin(['ar' => Article::tableName()], 'ar.id=a.article_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.announcement_id=a.id')
            ->select('a.id')
            ->where([
                '<>',
                'ar.is_delete',
                self::STATUS_ACTIVE,
            ])
            ->andWhere($where)
            ->groupBy('a.id');

        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'a.company_id',
            'memberId'        => Yii::$app->user->id,
            'query'           => $queryTwo,
            'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
        ]);

        if ($authorityList) {
            $queryTwo = $authorityList['query'];
        }

        $whereTwo = [
            self::STATUS_AUDIT_STAGING,
            self::STATUS_AUDIT_AWAIT,
            self::STATUS_AUDIT_REFUSE,
        ];
        $queryTwo->andFilterCompare('j.status', BaseJob::STATUS_DELETE, '<>');
        $queryTwo->andFilterCompare('a.create_type', BaseAnnouncement::CREATE_TYPE_COMPANY);
        $queryTwo->andFilterCompare('a.audit_status', $whereTwo, 'in');
        $queryTwo->andFilterCompare('ar.first_release_time', TimeHelper::ZERO_TIME);
        $list['column_2'] = $queryTwo->count();

        //column=3 已下线
        $whereThree = self::find()
            ->alias('a')
            ->leftJoin(['ar' => Article::tableName()], 'ar.id=a.article_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.announcement_id=a.id')
            ->select('a.id')
            ->where([
                '<>',
                'ar.is_delete',
                self::STATUS_ACTIVE,
            ])
            ->andWhere($where)
            ->groupBy('a.id');

        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'a.company_id',
            'memberId'        => Yii::$app->user->id,
            'query'           => $whereThree,
            'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
        ]);

        if ($authorityList) {
            $whereThree = $authorityList['query'];
        }

        $whereThree->andFilterCompare('j.status', BaseJob::STATUS_DELETE, '<>');
        $whereThree->andFilterCompare('ar.status', Article::STATUS_OFFLINE);
        $list['column_3'] = $whereThree->count();

        //column=4 全部
        $whereFour = self::find()
            ->alias('a')
            ->leftJoin(['ar' => Article::tableName()], 'ar.id=a.article_id')
            ->leftJoin(['j' => BaseJob::tableName()], 'j.announcement_id=a.id')
            ->select('a.id')
            ->where([
                '<>',
                'ar.is_delete',
                self::STATUS_ACTIVE,
            ])
            ->andWhere($where)
            ->groupBy('a.id');

        $authorityList = (new CompanyAuthorityClassify())->run([
            'associatedField' => 'a.company_id',
            'memberId'        => Yii::$app->user->id,
            'query'           => $whereFour,
            'returnType'      => CompanyAuthorityClassify::DATA_JOB_COOPERATE,
        ]);

        if ($authorityList) {
            $whereFour = $authorityList['query'];
        }

        $whereFour->andFilterCompare('j.status', BaseJob::STATUS_DELETE, '<>');
        $statusList = [
            self::STATUS_AUDIT_STAGING,
            self::STATUS_AUDIT_AWAIT,
            self::STATUS_AUDIT_REFUSE,
        ];
        $status     = [
            BaseArticle::STATUS_ONLINE,
            BaseArticle::STATUS_OFFLINE,
        ];
        $whereFour->andWhere([
            'or',
            [
                'and',
                [
                    'in',
                    'ar.status',
                    $status,
                ],
                [
                    '<>',
                    'ar.first_release_time',
                    TimeHelper::ZERO_TIME,
                ],
            ],
            [
                'and',
                [
                    'in',
                    'a.audit_status',
                    $statusList,
                ],
                [
                    '=',
                    'a.create_type',
                    BaseAnnouncement::CREATE_TYPE_COMPANY,
                ],
            ],
        ]);
        $list['column_4'] = $whereFour->count();

        return $list;
    }

    /**
     * 获取公告编辑数据
     * @param $id
     * @return array
     */
    public static function getEditInfo($id)
    {
        $announcement = self::findOne(['id' => $id]);
        if (!$announcement) {
            throw new Exception('非法访问');
        }

        $auditStatus = self::findOneVal(['id' => $id], 'audit_status');
        $status      = Article::findOneVal(['id' => $announcement->article_id], 'status');
        if ($auditStatus == self::STATUS_AUDIT_AWAIT || $status == Article::STATUS_OFFLINE) {
            throw new Exception('待审核或下线状态不支持操作编辑');
        }

        $select = [
            'a.id',
            'a.article_id',
            'a.title',
            'a.period_date',
            'a.audit_status',
            'a.apply_type',
            'a.apply_address',
            'a.delivery_type',
            'ar.status',
            'ar.content',
            'ar.first_release_time',
            'ar.release_time',
            'a.file_ids',
            'ar.home_column_id',
            'ar.home_sub_column_ids',
            'a.template_id',
            'ar.cover_thumb',
            'ar.seo_description',
            'ar.seo_keywords',
        ];

        $announcementInfo                  = self::find()
            ->alias('a')
            ->leftJoin(['ar' => Article::tableName()], 'ar.id = a.article_id')
            ->select($select)
            ->where(['a.id' => $id])
            ->asArray()
            ->one();
        $announcementInfo['delivery_type'] = intval($announcementInfo['delivery_type']);
        $announcementInfo['fileList']      = [];
        if (!empty($announcementInfo['file_ids'])) {
            $announcementInfo['fileList'] = Announcement::getAppendixList($announcementInfo['file_ids']);
        }

        // 如果是审核拒绝，回显拒绝的数据
        if ($announcementInfo['audit_status'] == self::STATUS_AUDIT_REFUSE) {
            $editContent = BaseAnnouncementEdit::find()
                ->select('edit_content')
                ->where(['announcement_id' => $id])
                ->orderBy('id desc')
                ->asArray()
                ->one();

            $announcementInfo['content'] = json_decode($editContent['edit_content'],
                true)['content'] ?: $announcementInfo['content'];
        }

        if (strtotime($announcementInfo['period_date']) < 1) {
            if (strtotime($announcementInfo['release_time']) < 1) {
                $releaseTime = CUR_DATETIME;
            } else {
                $releaseTime = $announcementInfo['release_time'];
            }
            // 没有填截止日期，默认给发布时间后的365天
            $periodDate                      = strtotime($releaseTime) + 60 * 60 * 24 * self::ADD_RELEASE_AGAIN_DAY;
            $announcementInfo['period_date'] = date('Y-m-d', $periodDate);
        }

        if ($announcementInfo['first_release_time'] == TimeHelper::ZERO_TIME) {
            $announcementInfo['first_release_time'] = '';
        }

        // 公告职位
        $jobSelect = [
            'id',
            'name',
            'code',
            'amount',
            'department',
            'major_id',
            'education_type',
            'province_id',
            'city_id',
            'audit_status',
            'status',
            'audit_status',
            'wage_type',
            'min_wage',
            'max_wage',
            'company_id',
            'first_release_time',
            'create_type',
        ];

        $announcementJobList = Job::find()
            ->select($jobSelect)
            ->where([
                'announcement_id' => $announcementInfo['id'],
            ])
            ->andWhere([
                '<>',
                'status',
                BaseJob::STATUS_DELETE,
            ])
            ->asArray()
            ->all();

        foreach ($announcementJobList as &$item) {
            $majorArr             = explode(',', $item['major_id']);
            $item['majorTxt']     = Major::getAllMajorName($majorArr) ?: '';
            $item['educationTxt'] = Dictionary::getEducationName($item['education_type']) ?: '';
            $item['provinceTxt']  = Area::getAreaName($item['province_id']) ?: '';
            $item['areaTxt']      = Area::getAreaName($item['city_id']) ?: '';
            $item['isTemp']       = BaseJobTemp::IS_TEMP_NO;
            if ($item['first_release_time'] != TimeHelper::ZERO_TIME) {
                $item['statusTxt']      = BaseJob::JOB_SEARCH_STATUS_NAME[$item['status']];
                $item['auditStatusTxt'] = BaseJob::JOB_AUDIT_STATUS_NAME[$item['audit_status']];
            } else {
                $item['statusTxt']      = '待发布';
                $item['auditStatusTxt'] = $item['audit_status'] == BaseJob::AUDIT_STATUS_WAIT ? '-' : BaseJob::JOB_AUDIT_STATUS_NAME[$item['audit_status']];
            }
            if (!$item['min_wage'] && !$item['max_wage']) {
                $item['wage'] = '面议';
            } else {
                $item['wage'] = BaseJob::formatWage($item['min_wage'], $item['max_wage'], $item['wage_type']) ?: '-';
            }
            if ($item['provinceTxt']) {
                $provinceArr[] = $item['provinceTxt'];
            }
            if ($item['areaTxt']) {
                $cityArr[] = $item['areaTxt'];
            }
            //学历要求
            if ($item['educationTxt']) {
                $educationArr[] = $item['educationTxt'];
            }

            //检查是否有投递数据
            $jobApply      = JobApply::findOne(['job_id' => $item['id']]);
            $isCooperation = Company::findOneVal(['id' => $item['company_id']], 'is_cooperation');
            // 有投递数据or待审核状态or合作单位&有审核通过历史or单位发布&没有审核通过历史&审核拒绝职位不可删除
            if ($jobApply) {
                $item['canDel'] = false;
            } elseif (!empty($item['first_release_time']) && $item['first_release_time'] != TimeHelper::ZERO_TIME && $isCooperation == Company::COOPERATIVE_UNIT_YES) {
                $item['canDel'] = false;
            } elseif ($item['audit_status'] == Job::AUDIT_STATUS_WAIT_AUDIT) {
                $item['canDel'] = false;
            } elseif (!empty($item['first_release_time']) && $item['first_release_time'] == TimeHelper::ZERO_TIME && $item['create_type'] == Job::CREATE_TYPE_SELF && $item['audit_status'] == Job::AUDIT_STATUS_REFUSE_AUDIT) {
                $item['canDel'] = false;
            } else {
                $item['canDel'] = true;
            }
            unset($item['major_id'], $item['education_type'], $item['province_id'], $item['city_id'], $item['audit_status']);

            //职位联系人
            $contact                = BaseJob::getJobContact($item['id']);
            $item['job_contact']    = $contact;
            $item['job_contact_id'] = $contact['company_member_info_id'];
            //职位协同账号
            $contact_synergy                 = BaseJob::getJobContactSynergy($item['id']);
            $item['job_contact_synergy']     = $contact_synergy;
            $item['job_contact_synergy_num'] = count($contact_synergy);
            $item['job_contact_synergy_ids'] = array_column($contact_synergy, 'company_member_info_id');
            $information                     = [];
            if ($item['areaTxt']) {
                array_push($information, $item['areaTxt']);
            }
            if ($item['amount']) {
                array_push($information, $item['amount']);
            }
            if ($item['educationTxt']) {
                array_push($information, $item['educationTxt']);
            }
            if ($item['wage']) {
                array_push($information, $item['wage']);
            }
            if ($item['majorTxt']) {
                array_push($information, $item['majorTxt']);
            }
            $item['information'] = implode(' | ', $information);
        }

        return compact('announcementInfo', 'announcementJobList');
    }

    /**
     * 获取公告剩余可发布数量
     * @param $memberId
     * @return array
     */
    public static function getSurplusReleaseAmount($memberId)
    {
        $companyPackageConfig = BaseCompanyPackageConfig::find()
            ->alias('p')
            ->leftJoin(['s' => BaseCompanyPackageSystemConfig::tableName()], 's.id = p.package_id')
            ->select([
                'p.id',
                'p.add_time',
                'p.job_amount',
                'p.announcement_amount',
                'p.job_refresh_amount',
                'p.announcement_refresh_amount',
                'p.expire_time',
                'p.effect_time',
                'p.package_id',
                'p.package_amount',
                's.job_amount as job_amount_system',
                's.announcement_amount as announcement_amount_system',
                's.job_refresh_amount as job_refresh_amount_system',
                's.announcement_refresh_amount as announcement_refresh_amount_system',
            ])
            ->where(['p.member_id' => $memberId])
            ->orderBy('p.id desc')
            ->asArray()
            ->one();

        $packageConfigName = BaseCompanyPackageSystemConfig::PACKAGE_CONFIG_NAME;

        // $used = BaseAnnouncement::find()
        //     ->alias('a')
        //     ->leftJoin(['ar' => BaseArticle::tableName()], 'a.article=ar.id')
        //     ->where([
        //         'ar.status' => [
        //             BaseArticle::STATUS_ONLINE,
        //             BaseArticle::STATUS_OFFLINE,
        //         ],
        //     ])
        //     ->andWhere([
        //         'a.member_id' => $memberId,
        //         'create_type' => BaseAnnouncement::CREATE_TYPE_COMPANY,
        //     ])
        //     ->count();

        return [
            'package' => $packageConfigName['announcement_amount'],
            'surplus' => (int)$companyPackageConfig['announcement_amount'] < 0 ? 0 : (int)$companyPackageConfig['announcement_amount'],
            'total'   => $companyPackageConfig['announcement_amount_system'] * $companyPackageConfig['package_amount'],
        ];
    }

    /**
     * 获取公告详情页数据(单位端)
     * @param $id
     * @param $type
     * @return array|\yii\db\ActiveRecord|null
     * @throws \Exception
     */
    public static function getAnnouncementDetailInfo($id, $type)
    {
        $info = self::find()
            ->alias('an')
            ->leftJoin(['a' => BaseArticle::tableName()], 'a.id=an.article_id')
            ->leftJoin(['c' => BaseCompany::tableName()], 'an.company_id = c.id')
            ->where(['an.id' => $id])
            ->select([
                'an.id',
                'an.title',
                'an.add_time',
                'an.period_date as periodDate',
                'an.apply_type',
                'an.file_ids',
                'an.delivery_way',
                'a.refresh_time as refreshTime',
                'a.content',
                'a.release_time',
                'a.id as articleId',
                'a.status',
                'a.home_column_id',
                'a.seo_keywords',
                'a.seo_description',
                'c.full_name as companyName',
                'c.id as companyId',
                'c.logo_url as logoUrl',
                'c.english_name as englishName',
                'c.type',
                'c.scale',
                'c.nature',
                'c.member_id as companyMemberId',
                'c.industry_id',
                'c.is_cooperation',
            ])
            ->asArray()
            ->one();
        //职位附件
        $info['fileList'] = BaseAnnouncement::getAppendixList($info['file_ids']);
        //获取公告在招职位数量
        $info['jobAmount'] = BaseJob::getAnnouncementJobAmount($id, $type);
        //获取公告招聘人
        $info['jobRecruitAmount'] = BaseJob::getAnnouncementJobRecruitAmount($id, $type);
        //获取公告下所有省份
        $info['provinceName'] = self::getAllProvinceName($id, $type);
        //获取公告下的所有城市
        $info['cityName'] = self::getAllCityName($id, $type);
        //获取公告下福利
        $info['allWelfareLabel'] = self::getAllWelfareLabel($id);
        if ($info['periodDate'] == TimeHelper::ZERO_TIME) {
            $info['periodDate'] = '详见正文';
        } else {
            $info['periodDate'] = date('Y-m-d', strtotime($info['periodDate']));
        }
        if ($info['refreshTime'] == TimeHelper::ZERO_TIME) {
            $info['refreshTime'] = date('Y-m-d', strtotime($info['add_time']));
        } else {
            $info['refreshTime'] = date('Y-m-d', strtotime($info['refreshTime']));
        }

        //获取公告下的最低学历要求
        $info['minEducation'] = trim(BaseJob::getAnnouncementJobEducationType($id));
        //获取公告下的专业列表
        $info['majorName'] = self::getAllMajorName($id, 'text', $type);

        //获取报名方式
        ////获取公告职位信息
        $job_list        = BaseJob::find()
            ->select('delivery_way,apply_type,apply_address')
            ->andWhere(['announcement_id' => $info['id']])
            ->asArray()
            ->all();
        $jobApplyTypeArr = [];
        foreach ($job_list as $item) {
            if (!empty($item['delivery_way'])) {
                if ($item['delivery_way'] == BaseJob::DELIVERY_WAY_LINK) {
                    $applyTypeList = explode(',', $item['apply_type']);
                    foreach ($applyTypeList as $type_item) {
                        array_push($jobApplyTypeArr, BaseDictionary::getSignUpName($type_item));
                    }
                } else {
                    array_push($jobApplyTypeArr, '站内投递');
                }
            }
        }
        $announcementApplyTypeArr = [];
        if ($info['delivery_way'] > 0) {
            if ($info['delivery_way'] == BaseAnnouncement::DELIVERY_WAY_LINK) {
                $announcementApplyTypeTxt = BaseJob::getApplyTypeName($info['apply_type']);
                $announcementApplyTypeArr = explode(',', $announcementApplyTypeTxt);
            } else {
                $announcementApplyTypeArr = ['站内投递'];
            }
        }
        $applyTypeArr            = array_merge($announcementApplyTypeArr, $jobApplyTypeArr);
        $applyTypeArr            = array_unique($applyTypeArr);
        $info['applyTypeText']   = implode(',', $applyTypeArr);
        $info['announcementUrl'] = Announcement::getDetailUrl($info['id']);

        return $info;
    }

    /**
     * 获取栏目公告下职位(专题模版B)
     * 最新职位
     * @param     $keywords
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public static function getSpecialBColumnJob($keywords): array
    {
        $config = Yii::$app->params['specialBColumn']['job'];
        $list   = [];

        foreach ($config as $k => $item) {
            $keywords['limit'] = $item['count'];
            $keywords['key']   = $k;
            $announcementList  = BaseAnnouncement::changeSecondColumnJobList($keywords);
            $list[$k]          = $announcementList;
        }

        return $list;
    }

    /**
     * 获取栏目页公告数据（专题模版B）
     * @param     $keywords
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public static function specialBColumnAnnouncementList($keywords): array
    {
        $config = Yii::$app->params['specialBColumn']['announcement'];
        $list   = [];

        foreach ($config as $k => $item) {
            $keywords['limit'] = $item['count'];
            $keywords['key']   = $k;

            switch ($k) {
                case 'special_latest_announcement':
                    $data          = [];
                    $temp          = BaseAnnouncement::changeSpecialBColumnAnnouncementListNew($keywords);
                    $data['list']  = $temp['list'];
                    $data['count'] = $temp['count'];
                    break;
                case 'right_hot_announcement':
                    $data['list'] = BaseAnnouncement::getRecommendAnnouncementList($keywords);
                    break;
                default:
                    $data = BaseAnnouncement::getSpecialBColumnAnnouncementList($keywords);
                    break;
            }

            $list[$k] = $data;
        }

        return $list;
    }

    /**
     * 获取栏目页公告数据（专题栏目B）切换
     * @param     $keywords
     * @return array
     * @throws \Exception
     */
    public static function changeSpecialBAnnouncementList($keywords): array
    {
        $config            = Yii::$app->params['specialBColumn']['announcement'][$keywords['key']];
        $keywords['limit'] = $config['count'];

        return BaseAnnouncement::changeSpecialBColumnAnnouncementListNew($keywords);
    }

    /**
     * 获取栏目页公告数据（博士后模版B栏目）
     * @param     $keywords
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public static function PostDoctorBColumnAnnouncementList($keywords): array
    {
        $config = Yii::$app->params['postDoctorColumn']['announcement'];

        $list = [];

        foreach ($config as $k => $item) {
            $keywords['limit'] = $item['count'];
            $keywords['key']   = $k;

            switch ($k) {
                case 'right_announcement_classification':
                    $tapList          = BaseAnnouncement::getHomeSubColumnIdsList($keywords);
                    $data             = [];
                    $announcementList = [];
                    foreach ($tapList as $value) {
                        $keywords['columnId'] = $value['id'];
                        $announcementList[]   = BaseAnnouncement::getPostDoctorColumnAnnouncementList($keywords);
                    }

                    $data['tapList'] = $tapList;
                    $data['list']    = $announcementList;
                    break;
                default:
                    $data = BaseAnnouncement::getPostDoctorColumnAnnouncementList($keywords);
                    break;
            }

            $list[$k] = $data;
        }

        return $list;
    }

    /**
     * @param $title     string 公告名称
     * @param $companyId int 单位id
     * @return array
     */
    public static function getListByName($title, $companyId): array
    {
        $query = new self();
        $where = [
            [
                '=',
                'an.company_id',
                $companyId,
            ],
            [
                '=',
                'art.status',
                BaseArticle::STATUS_ONLINE,
            ],
        ];

        if ($title) {
            $where[] = [
                'like',
                'an.title',
                $title,
            ];
        }

        return $query::find()
            ->alias('an')
            ->select([
                'an.id',
                'an.title',
            ])
            ->leftJoin(['art' => BaseArticle::tableName()], 'art.id = an.article_id')
            ->where(new AndCondition($where))
            ->limit(10)
            ->asArray()
            ->all();
    }
}