<?php

namespace frontendPc\models;

use common\base\models\BaseMember;
use common\base\models\BaseMemberActionLog;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAcademicBook;
use common\helpers\IpHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use yii\base\Exception;

class ResumeAcademicBook extends BaseResumeAcademicBook
{
    /**
     * 获取学术专著列表
     * @param $memberId
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getBookList($memberId): array
    {
        $list = self::find()
            ->where([
                'member_id' => $memberId,
                'status'    => self::STATUS_ACTIVE,
            ])
            ->select([
                'id',
                'publish_date as publishDate',
                'name',
                'words',
                'publish_amount as publishAmount',
            ])
            //            ->limit(self::LIMIT_NUM)
            ->orderBy('publish_date desc')
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            $item['publishDate'] = TimeHelper::formatToYearMonth($item['publishDate']);
        }

        return $list;
    }

    /**
     * 新增或修改数据
     * @param $data
     * @throws Exception
     */
    public static function saveInfo($data)
    {
        $name          = $data['name'];
        $publishDate   = $data['publishDate'];
        $words         = $data['words'];
        $publishAmount = $data['publishAmount'];

        //判断数据
        if (strlen($name) < 1 || strlen($publishDate) < 1 || strlen($words) < 1) {
            throw new Exception('数据出错!');
        }

        //判断是新增还是编辑
        if (!empty($data['id'])) {
            $model    = self::findOne($data['id']);
            $memberId = $model->member_id;
            if (empty($model) || $model->status != self::STATUS_ACTIVE) {
                throw new Exception('该学术专著记录不存在');
            }
        } else {
            $memberId = $data['memberId'];

            $model = new self();

            $model->resume_id = BaseMember::getMainId($memberId);
            $model->member_id = $memberId;
        }

        $model->name           = $name;
        $model->publish_date   = TimeHelper::formatAddDay($publishDate);
        $model->words          = $words;
        $model->publish_amount = $publishAmount;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        //更新简历完成度表
        ResumeComplete::updateResumeCompleteInfo($memberId);
        //新增操作日志
        $log_data = [
            'content' => '保存简历学术专著信息，memberId：' . $memberId,
        ];
        // 写日志
        BaseMemberActionLog::log($log_data);
    }

    /**
     * 删除数据
     * @param $id
     * @throws Exception
     */
    public static function delBook($id, $memberId)
    {
        $model = self::findOne($id);
        if ($model->member_id != $memberId) {
            throw new Exception('该记录与当前用户信息不匹配');
        }

        $model->status = self::STATUS_DELETE;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //更新简历完成度表
        ResumeComplete::updateResumeCompleteInfo($memberId);

        //新增操作日志
        $data = [
            'content' => '删除学术著作id：' . $id,
        ];
        // 写登录日志
        BaseMemberActionLog::log($data);
    }

}