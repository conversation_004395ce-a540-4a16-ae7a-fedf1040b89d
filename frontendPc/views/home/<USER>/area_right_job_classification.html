<div class="selected-job w">
    <div class="job-nav component">
        <div class="common-title eliminate">
            <h2>精选职位</h2>
        </div>

        <span @click="handleSelectedJob" class="update change-content">换一批</span>
    </div>

    <div class="tab-con">
        <div class="con">
            <div class="option ">
                <ul class="flex">
                    <?php foreach($list['list'] as $k => $v):?>
                    <!--                这里有特殊情况，省区栏目的category_id是没有的，这里用k去传，后端拿到再去数组查出列表-->
                    <li data-id="<?php echo $v['job_category_id'] ?: $k ?>" <?php if(empty($v['job_category_id'])):?>data-special="1"<?php endif?> class="<?php if($k==0){ echo 'active';}?>"  title="<?php echo \common\helpers\StringHelper::changeQuotationMark($v['name']);?>">
                            <?php echo $v['name'] ?>
                        </li>
                    <?php endforeach?>
                </ul>
                <div class="more option-more">
                    <a href="/" target="_blank">更多>></a>
                </div>
            </div>

            <div class="job-board">
                <?php foreach($list['list'] as $key1 => $value1):?>
                <div class="list" <?php if($key1==0):?>style="display: block"<?php endif?>>
                    <ul>
                        <?php foreach ($value1['list'] as $k1 => $item1):?>
                        <li>
                            <a href="<?php echo $item1['url'];?>" target="_blank" title="<?=common\helpers\StringHelper::changeQuotationMark($item1['name']);?>">
                                <div class="position">
                                    <h6>
                                        <?php echo $item1['name']?>
                                    </h6>
                                    <span class="money">
                                            <?php echo $item1['wage']?>
                                        </span>
                                </div>
                                <div class="requirement">
                                    <?php if($item1['educationText']){?>
                                    <span>
                                        <?php echo $item1['educationText']?>
                                    </span>
                                    <?php }?>
                                    <?php if($item1['major']){?>
                                    <span>
                                            <?php echo $item1['major']?>
                                        </span>
                                    <?php }?>
                                    <?php if($item1['amount']){?>
                                    <span>
                                            <?php echo $item1['amount']?>
                                        </span>
                                    <?php }?>
                                </div>
                                <div class="region">
                                        <span class="school">
                                            <?php echo $item1['companyName']?>
                                        </span>
                                    <span class="city-place">
                                            <span class="city">
                                                <?php echo $item1['city']?>
                                            </span>
                                        </span>
                                </div>
                            </a>
                        </li>
                        <?php endforeach?>
                    </ul>
                </div>
            <?php endforeach?>
            </div>
        </div>



<!--    <div class="con">-->
<!--        <div class="option ">-->
<!--            <ul class="flex">-->
<!--                <?php foreach($list['hotList'] as $k => $v):?>-->
<!--&lt;!&ndash;                这里有特殊情况，省区栏目的category_id是没有的，这里用k去传，后端拿到再去数组查出列表,同时更多按钮，不传这个类型id&ndash;&gt;-->
<!--                <li data-id="<?php echo $v['job_category_id']?: $k ?>" <?php if(empty($v['job_category_id'])):?>data-special="1"<?php endif?> class="<?php if($k==0){ echo 'active';}?>">-->
<!--                    <?php echo $v['name'] ?>-->
<!--                </li>-->
<!--                <?php endforeach?>-->
<!--            </ul>-->
<!--            <div class="more option-more">-->
<!--                <a href="/" target="_blank">更多>></a>-->
<!--            </div>-->
<!--        </div>-->

<!--        <div class="job-board">-->
<!--            <?php foreach($list['hotList'] as $key1 => $value1):?>-->
<!--            <div class="list" <?php if($key1==0):?>style="display: block"<?php endif?>>-->
<!--                <ul>-->
<!--                    <?php foreach ($value1['list'] as $k1 => $item1):?>-->
<!--                    <li>-->
<!--                        <a href="<?php echo $item1['url'];?>" target="_blank">-->
<!--                            <div class="position">-->
<!--                                <h6>-->
<!--                                    <?php echo $item1['name']?>-->
<!--                                </h6>-->
<!--                                <span class="money">-->
<!--                                                <?php echo $item1['wage']?>-->
<!--                                            </span>-->
<!--                            </div>-->
<!--                            <div class="requirement">-->
<!--                                <?php if($item1['educationText']){?>-->
<!--                                <span>-->
<!--                                            <?php echo $item1['educationText']?>-->
<!--                                        </span>-->
<!--                                <?php }?>-->
<!--                                <?php if($item1['major']){?>-->
<!--                                <span>-->
<!--                                                <?php echo $item1['major']?>-->
<!--                                            </span>-->
<!--                                <?php }?>-->
<!--                                <?php if($item1['amount']){?>-->
<!--                                <span>-->
<!--                                                <?php echo $item1['amount']?>-->
<!--                                            </span>-->
<!--                                <?php }?>-->
<!--                            </div>-->
<!--                            <div class="region">-->
<!--                                            <span class="school">-->
<!--                                                <?php echo $item1['companyName']?>-->
<!--                                            </span>-->
<!--                                <span class="city-place">-->
<!--                                                <span class="city">-->
<!--                                                    <?php echo $item1['city']?>-->
<!--                                                </span>-->
<!--                                            </span>-->
<!--                            </div>-->
<!--                        </a>-->
<!--                    </li>-->
<!--                    <?php endforeach?>-->
<!--                </ul>-->
<!--            </div>-->
<!--        <?php endforeach?>-->
<!--    </div>-->
<!--    </div>-->

    </div>


</div>




<script>
    $(function () {
        function setMoreHref() {
            var parentIndex = $('.selected-job .switch .change').index()

            var jobCategoryId = $('.selected-job .tab-con .con').eq(parentIndex).find('li.active').attr('data-id')

            var special = $('.selected-job .tab-con .con').eq(parentIndex).find('li.active').attr('data-special')

            $('.selected-job .tab-con .con').eq(parentIndex).find('.more a').attr('href', '/job?jobType=' + jobCategoryId)


            $('body').on('mouseenter', '.selected-job .switch li', function () {
                var index = $(this).index()
                jobCategoryId = $('.selected-job .tab-con .con').eq(index).find('.option li.active').attr('data-id')
                special = $('.selected-job .tab-con .con').eq(index).find('.option li.active').attr('data-special')
                $('.selected-job .tab-con .con').eq(index).find('.more a').attr('href', special ? '/job' : '/job?jobType=' + jobCategoryId)
            })
            $('body').on('mouseenter', '.selected-job .option ul li', function () {
                jobCategoryId = $(this).attr('data-id')
                special = $(this).attr('data-special')

                $(this).parents('.con').find('.more a').attr('href', special ? '/job' : '/job?jobType=' + jobCategoryId)
            })
        }

        setMoreHref()
    })
</script>