<?php

namespace frontendPc\controllers\api\person;

use frontendPc\models\ResumeResearchProject;

class ResumeResearchProjectController extends BaseFrontPcApiPersonController
{
    /**
     * 保存科研项目
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSave()
    {
        $data = \Yii::$app->request->post();

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            ResumeResearchProject::saveInfo($data);

            $memberId = \Yii::$app->user->id;

            $transaction->commit();

            return $this->success(ResumeResearchProject::getInfoList($memberId));
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除科研项目
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDel()
    {
        $id       = \Yii::$app->request->post('id');
        $memberId = \Yii::$app->user->id;

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            ResumeResearchProject::delProject($id, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

}