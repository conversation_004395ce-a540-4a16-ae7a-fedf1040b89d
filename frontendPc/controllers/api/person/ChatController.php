<?php

namespace frontendPc\controllers\api\person;

use common\base\models\BaseChatRoom;
use common\base\models\BaseMember;
use common\libs\Cache;
use common\service\chat\ChatApplication;
use Yii;
use yii\db\Exception;

class ChatController extends BaseFrontPcApiPersonController
{
    /**
     * 创建聊天室
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCreateRoom()
    {
        $jobId       = Yii::$app->request->post('jobId');
        $memberId    = Yii::$app->user->id;
        $resumeId    = BaseMember::getMainId($memberId);
        $transaction = Yii::$app->db->beginTransaction();

        // 这里会有一个问题,就是求职者这个时候是之前登录的,token是有问题的,需要重置token,
        // 先拿求职者的信息
        $memberLastLoginTime = BaseMember::findOneVal([
            'id' => $memberId,
        ], 'last_login_time');

        // 转时间戳
        $memberLastLoginTime = strtotime($memberLastLoginTime);

        if ($memberLastLoginTime && $memberLastLoginTime <= 1701340480) {
            // 返回403
            Yii::$app->user->logout();
            echo json_encode([
                'code'   => 403,
                'result' => 0,
                'msg'    => '请您先进行登录后再操作',
            ]);
            exit;
        }

        try {
            $app  = ChatApplication::getInstance();
            $data = [
                'resumeId'    => $resumeId,
                'jobId'       => $jobId,
                'creatorType' => BaseChatRoom::CREATOR_TYPE_PERSON,
            ];
            $data = $app->resumeCreateRoom($data);
            $transaction->commit();

            return $this->success($data);
        } catch (\Exception $e) {
            //如果有错误数组，返回数组消息
            $errorInfo = $e->getMessage();

            $transaction->rollBack();

            return $this->fail($errorInfo);
        }
    }

    public function actionChangeJob()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $chatApp = ChatApplication::getInstance();
            $params  = [
                // 先改成get
                'chatId'   => \Yii::$app->request->post('chatId'),
                'jobId'    => \Yii::$app->request->post('jobId'),
                'memberId' => Yii::$app->user->id,
            ];
            $chatApp->resumechangeJob($params);

            $transaction->commit();

            return $this->success();
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    public function actionUpload()
    {
        $memberId = Yii::$app->user->id;

        $chatId = Yii::$app->request->post('chatId');
        try {
            $app  = ChatApplication::getInstance();
            $data = $app->resumeUpload($memberId, $chatId);

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionDownload()
    {
        $memberId = Yii::$app->user->id;

        $messageId = Yii::$app->request->get('messageId');
        try {
            $app = ChatApplication::getInstance();

            // 这里直接是文件流了
            return $app->download($memberId, $messageId);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 申请发送附件前置检查
     * @return bool|void|\yii\console\Response|\yii\web\Response
     */
    public function actionCheckRequestFile()
    {
        $jobId  = Yii::$app->request->post('jobId');
        $chatId = Yii::$app->request->post('chatId');

        try {
            $app  = ChatApplication::getInstance();
            $data = $app->checkPersonRequestFile($jobId, $chatId);

            return $this->success($data);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 设置直聊弹窗缓存
     */
    public function actionNoticeCache()
    {
        //isRemember  1记住 2不记住
        //rememberType  1停留此页 2继续聊
        $isRemember   = Yii::$app->request->post('isRemember');
        $rememberType = Yii::$app->request->post('rememberType');
        if ($isRemember == 1) {
            //时效当前时间+6天
            $time = strtotime(date('Y-m-d 23:59:59', strtotime('+6 day'))) - time();
            Cache::set(Cache::PC_CHAT_NOTICE_TYPE . ':' . Yii::$app->user->id, $rememberType, $time);
        }

        return $this->success();
    }

}