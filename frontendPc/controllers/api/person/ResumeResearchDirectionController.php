<?php

namespace frontendPc\controllers\api\person;

use frontendPc\models\ResumeResearchDirection;

class ResumeResearchDirectionController extends BaseFrontPcApiPersonController
{
    /**
     * 保存研究方向
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSave()
    {
        $data             = \Yii::$app->request->post();
        $data['memberId'] = \Yii::$app->user->id;
        $transaction      = \Yii::$app->db->beginTransaction();

        try {
            ResumeResearchDirection::saveInfo($data);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 删除研究方向
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDel()
    {
        $id       = \Yii::$app->request->post('id');
        $memberId = \Yii::$app->user->id;

        $transaction = \Yii::$app->db->beginTransaction();

        try {
            ResumeResearchDirection::delDirection($id, $memberId);
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

}