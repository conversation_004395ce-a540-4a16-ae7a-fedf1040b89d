<?php
namespace frontendPc\components;

use common\base\BaseActiveRecord;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use Faker\Provider\Base;
use Yii;
use yii\base\Widget;

class TopBannerWidget extends BaseWidget
{

    public $showcaseName;
    public $list;
    public $tapList;
    public $columnId;
    public $templateType;
    //    public $limit=8;

    public function init()
    {
        parent::init();
        $this->showcaseName = 'top_banner';

        //这里取不同栏目banner广告位
        $name       = BaseHomeColumn::carryColumnToPinyin($this->columnId);
        $name       = $name . "_A1";
        $positionId = BaseHomePosition::findOneVal(['number' => $name], 'id');
        $this->list = BaseShowcase::getByPositionConfig($positionId, $name);
    }

    public function run()
    {
        return $this->render('top_banner.html', [
            'list'    => $this->list,
        ]);
    }
}