<footer class="page-footer-container">
    <div class="site-foot-menu">
        <a href="//www.gaoxiaojob.com/zhaopin/aboutUs/index.html" target="_blank" rel="nofollow">关于我们</a>|
        <a href="//www.gaoxiaojob.com/zhaopin/aboutUs/contactUs.html" rel="nofollow">联系我们</a>|
        <a href="//www.gaoxiaojob.com/zhaopin/aboutUs/joinUs.html" rel="nofollow" target="_blank">人才招聘</a>|
        <a href="//www.gaoxiaojob.com/zhaopin/aboutUs/productService.html" rel="nofollow">产品服务</a>|
        <a href="//www.gaoxiaojob.com/zhaopin/aboutUs/disclaimers.html" target="_blank" rel="nofollow">免责声明</a>|
        <a href="/data/sitemap.html" target="_blank">网站导航</a>|
        <a href="//www.gaoxiaojob.com/zhaopin/zhuanti/zzzm2021/index.html" target="_blank" rel="nofollow">资质证明</a>
    </div>

    <div class="site-foot-copyright">
        <p>
            Copyright © 2007-<?= date('Y') ?> 高校人才网 版权所有 网站备案信息：
            <a href="https://beian.miit.gov.cn/" target="_blank" rel="nofollow">粤ICP备13048400号</a>
            粤公网安备：
            <a href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=44010602004138" target="_blank" rel="nofollow">44010602004138号</a>
        </p>
        <p>本站由广州高才信息科技有限公司运营</p>
        <p>
            中华人民共和国增值电信业务经营许可证：
            <a href="//zt.gaoxiaojob.com/zzdxywjyxkz.jpg" target="_blank" rel="nofollow">粤B2-20180648</a>
        </p>
        <p>人力资源服务许可证编号：(粤)人服证字(2022) 第0106114823&nbsp;&nbsp;企业统一社会信用代码：91440106MA59BTXW56</p>
        <p>客户咨询电话：020-85611139 QQ：2881224205 邮箱：<EMAIL></p>
        <p>高校人才网——国内访问量、信息量排名前列的高层次人才需求信息平台</p>
        <p>本平台由广东同福律师事务所提供法律支持服务</p>
    </div>
</footer>
<!--下面总是报错没有$所以特地在这里多加一个jquery的引入-->
<?= \frontendPc\components\DialogLogin::widget()?>
<?= \frontendPc\components\DialogApplyJob::widget()?>
<?php if (Yii::$app->params['private'] && in_array(\Yii::$app->controller->route,
Yii::$app->params['showAttentionRouter'])): ?>
<style>
    .beta-test {
        overflow: visible;
        padding: 20px;
        border-radius: 10px;
        width: 350px;
    }

    .beta-test.right {
        right: 45px;
    }

    .beta-test .el-notification__title {
        text-align: center;
    }

    .beta-test .el-notification__content {
        text-align: left;
        word-break: break-all;
    }

    .beta-test .el-notification__content p {
        text-indent: 2em;
    }

    .beta-test .el-notification__content a {
        color: #ffa000;
    }
</style>

<div id="globalNotice"></div>

<script>
    $(function () {
        const globalNoticeComponent = {
            mounted() {
                this.$notify({
                    title: "<?=Yii::$app->params['privateTitle']?>" || '提示',
                    customClass: "beta-test",
                    duration: 0,
                    position: 'bottom-right',
                    dangerouslyUseHTMLString: true,
                    message: `<?=Yii::$app->params['privateMessage']?>`
                });
            },

        }
        Vue.createApp(globalNoticeComponent).use(ElementPlus).mount('#globalNotice')
    });
</script>
<?php endif; ?>

<!-- 返回顶部 start -->
<link rel="stylesheet" href="/static/css/feedback.css?v=1.0.1">

<div id="backtopTemplate">
    <el-backtop class="fixed-aside" :visibility-height="viewportHeight" :right="190" :bottom="100">
        <span class="backtop-button"></span>
        <div class="top">TOP</div>
    </el-backtop>
</div>


<script>
    $(function () {
        const backtopOptions = {
            computed: {
                viewportHeight() {
                    return window.innerHeight
                }
            }
        }
        Vue.createApp(backtopOptions).use(ElementPlus).mount('#backtopTemplate')
    })
</script>
<!-- 返回顶部 end -->

<script>
    var _maq = _maq || []
    var token = "<?php echo Yii::$app->params['showcaseBrowse']['token'];?>"
    _maq.push(['_setAccount', token]);
</script>

<script src="/static/js/position.js"></script>

<?php if (Yii::$app->params['bdtj']): ?>
<script>
    var _hmt = _hmt || []
    ;(function () {
        var hm = document.createElement('script')
        hm.src = 'https://hm.baidu.com/hm.js?<?=Yii::$app->params["bdtj"] ?>'
        var s = document.getElementsByTagName('script')[0]
        s.parentNode.insertBefore(hm, s)
    })()
</script>
<?php endif; ?>