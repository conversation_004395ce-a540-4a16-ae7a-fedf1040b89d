<?php
namespace frontendPc\components;

use common\base\BaseActiveRecord;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseShowcase;
use Faker\Provider\Base;
use Yii;
use yii\base\Widget;

class CommunicationWidget extends BaseWidget
{
    public $list;

    public function init()
    {
        parent::init();

        $this->list = Yii::$app->params['configure']['communication'];
    }

    public function run(): string
    {
        return $this->render('communication.html', [
            'list' => $this->list,
        ]);
    }
}