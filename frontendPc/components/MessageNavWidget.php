<?php
namespace frontendPc\components;

use frontendPc\models\HomeColumn;
use Yii;
use yii\base\Widget;

class MessageNavWidget extends BaseWidget
{

    private $columnList;
    private $provinceList;
    private $cityList;

    public function init()
    {
        parent::init();

        $config           = Yii::$app->params['homeMessageNav'];
        $homeNavConfig    = Yii::$app->params['homeNav'];
        $homeRegionColumn = Yii::$app->params['homeRegionColumn'];
        // 保持这两个配置一致性

        $columnList = $config['column'];
        $tmpList    = [];
        foreach ($columnList as $item) {
            $url = HomeColumn::getDetailUrl($item);
            foreach ($homeNavConfig as $nav) {
                if ($item == $nav['id']) {
                    if ($nav['children']) {
                        $children = [];
                        foreach ($nav['children'] as $child) {
                            $children[] = [
                                'name' => $child['name'],
                                'url'  => $child['url'] ?: HomeColumn::getDetailUrl($child['id']),
                            ];
                        }
                        $tmpList[] = [
                            'name'     => $nav['name'],
                            'url'      => $url,
                            'children' => $children,
                        ];
                    } else {
                        $tmpList[] = [
                            'name' => $nav['name'],
                            'url'  => $url,
                        ];
                    }
                }
            }
        }
        $this->columnList = $tmpList;

        $homeRegionColumnList = $homeRegionColumn['list'];

        foreach ($homeRegionColumnList as $k1 => $region) {
            foreach ($region['province'] as $k2 => $province) {
                $homeRegionColumnList[$k1]['province'][$k2]['url'] = HomeColumn::getDetailUrl($province['id']);
            }
        }

        $this->provinceList = $homeRegionColumnList;

        $cityList = Yii::$app->params['homeNavArea']['city'];
        foreach ($cityList as &$item) {
            $item['url'] = HomeColumn::getDetailUrl($item['id']);
        }
        $this->cityList = $cityList;
    }

    public function run()
    {
        return $this->render('message_nav.html', [
            'columnList'   => $this->columnList,
            'provinceList' => $this->provinceList,
            'cityList'     => $this->cityList,
        ]);
    }
}