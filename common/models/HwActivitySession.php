<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "hw_activity_session".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态
 * @property int $activity_id 活动id
 * @property string $name 场次名称
 * @property string $number 场次编号
 * @property string $custom_time 自定义举办时间
 * @property string $start_date 举办开始日期
 * @property string $start_time 举办开始时间
 * @property string $end_date 举办结束日期
 * @property string $end_time 举办结束时间
 * @property int $time_type 举办时间类型（1当地  2北京）
 * @property string $custom_address 自定义举办地点
 * @property string $detail_address 详细地址
 * @property int $sort 排序
 * @property int $is_top 是否置顶；1:置顶；2:不置顶
 */
class HwActivitySession extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'hw_activity_session';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'start_date', 'end_date'], 'safe'],
            [['status', 'activity_id', 'time_type', 'sort', 'is_top'], 'integer'],
            [['name', 'number', 'custom_time', 'custom_address', 'detail_address'], 'string', 'max' => 255],
            [['start_time', 'end_time'], 'string', 'max' => 4],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'activity_id' => 'Activity ID',
            'name' => 'Name',
            'number' => 'Number',
            'custom_time' => 'Custom Time',
            'start_date' => 'Start Date',
            'start_time' => 'Start Time',
            'end_date' => 'End Date',
            'end_time' => 'End Time',
            'time_type' => 'Time Type',
            'custom_address' => 'Custom Address',
            'detail_address' => 'Detail Address',
            'sort' => 'Sort',
            'is_top' => 'Is Top',
        ];
    }
}
