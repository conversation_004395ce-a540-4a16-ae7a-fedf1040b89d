<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_click_total_daily".
 *
 * @property int $id ID
 * @property string $add_date 时间格式
 * @property int $job_id 职位ID
 * @property int $total 点击量
 */
class JobClickTotalDaily extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_click_total_daily';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_date'], 'required'],
            [['add_date'], 'safe'],
            [['job_id', 'total'], 'integer'],
            [['job_id', 'add_date'], 'unique', 'targetAttribute' => ['job_id', 'add_date']],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_date' => 'Add Date',
            'job_id' => 'Job ID',
            'total' => 'Total',
        ];
    }
}
