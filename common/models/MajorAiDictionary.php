<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "major_ai_dictionary".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property int $status 状态
 * @property string $name 名字(需要识别的文案)
 */
class MajorAiDictionary extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'major_ai_dictionary';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['status'], 'integer'],
            [['name'], 'string', 'max' => 256],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'status' => 'Status',
            'name' => 'Name',
        ];
    }
}
