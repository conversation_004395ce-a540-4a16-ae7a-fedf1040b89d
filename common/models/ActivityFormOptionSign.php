<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "activity_form_option_sign".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property int $activity_form_id 活动表单id
 * @property int $option_id 活动选项场次id
 * @property int $registration_form_id 报名表id
 * @property int $resume_id 人才id
 * @property int $serial_number 序列号
 * @property int $is_sign 是否已签到，1:是；2:否
 * @property string $sign_time 签到时间
 */
class ActivityFormOptionSign extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'activity_form_option_sign';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'sign_time'], 'safe'],
            [['activity_form_id', 'option_id', 'registration_form_id', 'resume_id', 'serial_number', 'is_sign'], 'integer'],
            [['sign_time'], 'required'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'activity_form_id' => 'Activity Form ID',
            'option_id' => 'Option ID',
            'registration_form_id' => 'Registration Form ID',
            'resume_id' => 'Resume ID',
            'serial_number' => 'Serial Number',
            'is_sign' => 'Is Sign',
            'sign_time' => 'Sign Time',
        ];
    }
}
