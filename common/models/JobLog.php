<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "job_log".
 *
 * @property int $id
 * @property string $update_time 修改时间
 * @property string $add_time 创建时间
 * @property int $create_type 类型1=运营；2=单位
 * @property int $create_id 创建人ID
 * @property string $create_name 创建人名称：运营记录运营名称；单位记录单位名称
 * @property string $remark 备注
 * @property string $route 控制器路由
 * @property int $type 操作类型(4位数00前面两位代表大操作类型00后面两位代表小操作类型；例如：刷新1001；批量刷新1002)：1001=添加；1002=批量添加；1101=编辑；
 * @property int $job_id 职位ID
 * @property int $announcement_id 公告ID
 */
class JobLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'job_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['update_time', 'add_time'], 'safe'],
            [['create_type', 'create_id', 'type', 'job_id', 'announcement_id'], 'integer'],
            [['type', 'job_id', 'announcement_id'], 'required'],
            [['create_name'], 'string', 'max' => 100],
            [['remark', 'route'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'update_time' => 'Update Time',
            'add_time' => 'Add Time',
            'create_type' => 'Create Type',
            'create_id' => 'Create ID',
            'create_name' => 'Create Name',
            'remark' => 'Remark',
            'route' => 'Route',
            'type' => 'Type',
            'job_id' => 'Job ID',
            'announcement_id' => 'Announcement ID',
        ];
    }
}
