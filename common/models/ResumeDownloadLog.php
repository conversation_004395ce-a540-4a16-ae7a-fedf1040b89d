<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_download_log".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property int $job_id 职位id
 * @property int $handler_type 用户类型，1:运营平台用户；2:普通用户
 * @property int $handler_id 操作用户id
 * @property string $handler_name 操作用户名称
 * @property int $resume_member_id 人才编号
 * @property string $resume_name 人才姓名
 * @property int $resume_id 简历id
 * @property int $resume_attachment_id 简历附件id
 */
class ResumeDownloadLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_download_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['job_id', 'handler_type', 'handler_id', 'resume_member_id', 'resume_id', 'resume_attachment_id'], 'integer'],
            [['handler_name', 'resume_name'], 'string', 'max' => 90],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'job_id' => 'Job ID',
            'handler_type' => 'Handler Type',
            'handler_id' => 'Handler ID',
            'handler_name' => 'Handler Name',
            'resume_member_id' => 'Resume Member ID',
            'resume_name' => 'Resume Name',
            'resume_id' => 'Resume ID',
            'resume_attachment_id' => 'Resume Attachment ID',
        ];
    }
}
