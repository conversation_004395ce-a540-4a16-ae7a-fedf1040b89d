<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "pay_transform_buried_point_log".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $ip 用户ip
 * @property string $user_cookies 用户的cookie
 * @property int $action_type 动作类型 
 * @property int $action_id 事件id
 * @property string $params 事件参数
 * @property string $action_url 操作页面
 * @property int $member_id 用户id
 * @property string $action_name 事件名称
 * @property int $platform 触发端口（1pc端  2小程序）
 * @property string $uuid 唯一码
 * @property string $order_no 平台订单号
 */
class PayTransformBuriedPointLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'pay_transform_buried_point_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['ip', 'action_type', 'action_id', 'member_id', 'platform', 'uuid'], 'integer'],
            [['action_id', 'params', 'action_name', 'platform', 'uuid'], 'required'],
            [['params'], 'string'],
            [['user_cookies'], 'string', 'max' => 64],
            [['action_url'], 'string', 'max' => 1024],
            [['action_name', 'order_no'], 'string', 'max' => 255],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'ip' => 'Ip',
            'user_cookies' => 'User Cookies',
            'action_type' => 'Action Type',
            'action_id' => 'Action ID',
            'params' => 'Params',
            'action_url' => 'Action Url',
            'member_id' => 'Member ID',
            'action_name' => 'Action Name',
            'platform' => 'Platform',
            'uuid' => 'Uuid',
            'order_no' => 'Order No',
        ];
    }
}
