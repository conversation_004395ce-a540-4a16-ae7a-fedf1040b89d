<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "qinta_resume".
 *
 * @property int $id
 * @property string $add_time 添加时间
 * @property int $qid
 * @property string $qname
 * @property string $q_name
 * @property string $gender
 * @property string $age
 * @property string $after_phd
 * @property string $image
 * @property string $latest_job
 * @property string $subject
 * @property string $title
 * @property string $oversea_exp
 * @property string $university
 * @property string $level
 * @property string $major
 * @property string $key_result
 * @property string $unionid
 * @property string $achievement
 * @property string $is_invite
 * @property string $is_alive
 * @property string $intention_high
 * @property string $detail
 */
class QintaResume extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'qinta_resume';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time'], 'safe'],
            [['qid'], 'integer'],
            [['key_result', 'achievement', 'detail'], 'required'],
            [['key_result', 'achievement', 'detail'], 'string'],
            [['qname', 'q_name', 'gender', 'age', 'after_phd', 'image', 'latest_job', 'subject', 'title', 'oversea_exp', 'university', 'level', 'major', 'unionid', 'is_invite', 'is_alive', 'intention_high'], 'string', 'max' => 256],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'qid' => 'Qid',
            'qname' => 'Qname',
            'q_name' => 'Q Name',
            'gender' => 'Gender',
            'age' => 'Age',
            'after_phd' => 'After Phd',
            'image' => 'Image',
            'latest_job' => 'Latest Job',
            'subject' => 'Subject',
            'title' => 'Title',
            'oversea_exp' => 'Oversea Exp',
            'university' => 'University',
            'level' => 'Level',
            'major' => 'Major',
            'key_result' => 'Key Result',
            'unionid' => 'Unionid',
            'achievement' => 'Achievement',
            'is_invite' => 'Is Invite',
            'is_alive' => 'Is Alive',
            'intention_high' => 'Intention High',
            'detail' => 'Detail',
        ];
    }
}
