<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_remind_log".
 *
 * @property int $id
 * @property string $add_time 创建时间
 * @property string $update_time 更新时间
 * @property int $status 状态
 * @property int $apply_id 投递id
 * @property int $resume_id 简历id
 * @property int $company_id 单位id
 * @property int $type 操作类型
 */
class ResumeRemindLog extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_remind_log';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['status', 'apply_id', 'resume_id', 'company_id', 'type'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'apply_id' => 'Apply ID',
            'resume_id' => 'Resume ID',
            'company_id' => 'Company ID',
            'type' => 'Type',
        ];
    }
}
