<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "announcement".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $audit_status 审核状态（1审核通过，-1审核拒绝，7等待审核，3草稿）
 * @property string $title 公告标题或单位名称
 * @property int $article_id 文章id
 * @property int $member_id 会员id
 * @property int $company_id 企业id
 * @property int $create_type 创建的类型(1企业自行发布，2运营人员代发布,3平台公告,不属于任何企业）
 * @property int $creator_id 创建人的id（企业就企业的id)
 * @property string $work_area_id 工作地点
 * @property string $period_date 截止日期
 * @property string $apply_type 应聘方式(1电子邮件xxxx
 * @property string $apply_address 投递地址
 * @property string $read_permissions 阅读权限（暂时不知道是要用作什么）
 * @property string $major_ids 需求学科ids
 * @property string $relation_company_ids 关联单位ids
 * @property int $template_id 页面模版id
 * @property string $offline_time 操作下线时间
 * @property int $status
 * @property int $is_consume_release 是否消费过发布（2否 1是）
 * @property int $offline_type 下线方式 0:无；1:自动下线；2：手动下线；3:违规
 * @property string $offline_reason 违规下线原因
 * @property int $home_sort 单位主页公告排序，0-999，数值越大越靠前
 * @property string $file_ids 应聘材料附件id对应file表id
 * @property int $delivery_type 投递类型1=站外投递,2=站内投递
 * @property int $delivery_way 投递方式 1平台投递 2邮箱投递 3网址投递
 * @property string $extra_notify_address 投递通知地址
 * @property int $is_miniapp 是否被小程序调用1调用 2没调用
 * @property int $is_manual_tag 是否运营手动标记 0没标记 1标记 2不标记
 * @property int $is_attachment_notice 是否附件提醒 1是 2否
 * @property string $uuid uuid
 * @property int $announcement_heat_type 公告类型热度（90天内点击数量计算热度类型）
 * @property int $establishment_type 编制类型
 * @property int $address_hide_status 地址隐藏状态（1隐藏,2显示）
 * @property int $online_job_amount 在线职位数量
 * @property int $all_job_amount 职位总数（包含在线与下线）
 * @property int $is_first_release 首发:1=首发,2=非首发
 * @property string $sub_title 公告简标题
 * @property string $highlights_describe 公告亮点描述
 * @property int $background_img_file_id 背景图文件id
 * @property int $background_img_file_type 使用的背景图类型1模版默认背景，2单位主页背景，3自定义背景
 * @property int $background_img_file_id_2 高级模版2使用图片id
 * @property int $background_img_file_id_3 高级模版3使用图片id
 * @property string $activity_job_content 活动招聘岗位
 * @property int $audit_admin_id 审核人ID
 * @property string $audit_admin_name 审核人名称
 * @property int $apply_admin_id 申请人ID
 * @property string $apply_admin_name 申请人名称
 */
class Announcement extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'announcement';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time', 'period_date', 'offline_time'], 'safe'],
            [['audit_status', 'article_id', 'member_id', 'company_id', 'create_type', 'creator_id', 'template_id', 'status', 'is_consume_release', 'offline_type', 'home_sort', 'delivery_type', 'delivery_way', 'is_miniapp', 'is_manual_tag', 'is_attachment_notice', 'announcement_heat_type', 'establishment_type', 'address_hide_status', 'online_job_amount', 'all_job_amount', 'is_first_release', 'background_img_file_id', 'background_img_file_type', 'background_img_file_id_2', 'background_img_file_id_3', 'audit_admin_id', 'apply_admin_id'], 'integer'],
            [['highlights_describe'], 'string'],
            [['title'], 'string', 'max' => 256],
            [['work_area_id', 'apply_type', 'read_permissions', 'major_ids'], 'string', 'max' => 32],
            [['apply_address', 'activity_job_content'], 'string', 'max' => 600],
            [['relation_company_ids', 'uuid'], 'string', 'max' => 64],
            [['offline_reason', 'extra_notify_address'], 'string', 'max' => 255],
            [['file_ids'], 'string', 'max' => 100],
            [['sub_title'], 'string', 'max' => 2048],
            [['audit_admin_name', 'apply_admin_name'], 'string', 'max' => 60],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'audit_status' => 'Audit Status',
            'title' => 'Title',
            'article_id' => 'Article ID',
            'member_id' => 'Member ID',
            'company_id' => 'Company ID',
            'create_type' => 'Create Type',
            'creator_id' => 'Creator ID',
            'work_area_id' => 'Work Area ID',
            'period_date' => 'Period Date',
            'apply_type' => 'Apply Type',
            'apply_address' => 'Apply Address',
            'read_permissions' => 'Read Permissions',
            'major_ids' => 'Major Ids',
            'relation_company_ids' => 'Relation Company Ids',
            'template_id' => 'Template ID',
            'offline_time' => 'Offline Time',
            'status' => 'Status',
            'is_consume_release' => 'Is Consume Release',
            'offline_type' => 'Offline Type',
            'offline_reason' => 'Offline Reason',
            'home_sort' => 'Home Sort',
            'file_ids' => 'File Ids',
            'delivery_type' => 'Delivery Type',
            'delivery_way' => 'Delivery Way',
            'extra_notify_address' => 'Extra Notify Address',
            'is_miniapp' => 'Is Miniapp',
            'is_manual_tag' => 'Is Manual Tag',
            'is_attachment_notice' => 'Is Attachment Notice',
            'uuid' => 'Uuid',
            'announcement_heat_type' => 'Announcement Heat Type',
            'establishment_type' => 'Establishment Type',
            'address_hide_status' => 'Address Hide Status',
            'online_job_amount' => 'Online Job Amount',
            'all_job_amount' => 'All Job Amount',
            'is_first_release' => 'Is First Release',
            'sub_title' => 'Sub Title',
            'highlights_describe' => 'Highlights Describe',
            'background_img_file_id' => 'Background Img File ID',
            'background_img_file_type' => 'Background Img File Type',
            'background_img_file_id_2' => 'Background Img File Id 2',
            'background_img_file_id_3' => 'Background Img File Id 3',
            'activity_job_content' => 'Activity Job Content',
            'audit_admin_id' => 'Audit Admin ID',
            'audit_admin_name' => 'Audit Admin Name',
            'apply_admin_id' => 'Apply Admin ID',
            'apply_admin_name' => 'Apply Admin Name',
        ];
    }
}
