<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "announcement_edit".
 *
 * @property int $id 主键id
 * @property string $add_time 创建时间
 * @property string $update_time 修改时间
 * @property int $status 状态，1:在用；2:关闭
 * @property int $announcement_id 公告id
 * @property string $edit_content 编辑修改内容
 * @property string $editor 编辑人名称
 * @property int $editor_type 编辑类型，1:平台；2:用户
 * @property int $editor_id 用户id
 * @property string $job_add_ids 新增职位ids
 */
class AnnouncementEdit extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'announcement_edit';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['add_time', 'update_time'], 'safe'],
            [['status', 'announcement_id', 'editor_type', 'editor_id'], 'integer'],
            [['edit_content'], 'required'],
            [['edit_content'], 'string'],
            [['editor'], 'string', 'max' => 32],
            [['job_add_ids'], 'string', 'max' => 64],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'add_time' => 'Add Time',
            'update_time' => 'Update Time',
            'status' => 'Status',
            'announcement_id' => 'Announcement ID',
            'edit_content' => 'Edit Content',
            'editor' => 'Editor',
            'editor_type' => 'Editor Type',
            'editor_id' => 'Editor ID',
            'job_add_ids' => 'Job Add Ids',
        ];
    }
}
