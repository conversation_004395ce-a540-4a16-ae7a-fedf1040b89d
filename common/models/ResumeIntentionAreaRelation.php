<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "resume_intention_area_relation".
 *
 * @property int $id id;主键id
 * @property int $area_id 意向地区id
 * @property int $intention_id 地区级别 跟随area表level
 * @property int $resume_id 简历id 为后续直接对人的意向分析做铺垫
 * @property int $level 意向ID
 */
class ResumeIntentionAreaRelation extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'resume_intention_area_relation';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['area_id', 'intention_id', 'resume_id', 'level'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'area_id' => 'Area ID',
            'intention_id' => 'Intention ID',
            'resume_id' => 'Resume ID',
            'level' => 'Level',
        ];
    }
}
