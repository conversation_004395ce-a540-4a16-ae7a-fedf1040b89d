<?php

namespace common\models;

use Yii;

/**
 * This is the model class for table "announcement_extra".
 *
 * @property int $id
 * @property int $announcement_id 公告ID
 * @property int $company_id 单位ID
 * @property int $is_pi 是否PI:1=是,2=不是
 * @property int $is_pay 是否付费单位；1:包含；2:不包含
 * @property int $is_boshihou_pay 博士后广告是否付费单位；1:包含；2:不包含
 */
class AnnouncementExtra extends \common\base\BaseActiveRecord
{
    /**
     * {@inheritdoc}
     */
    public static function tableName()
    {
        return 'announcement_extra';
    }

    /**
     * {@inheritdoc}
     */
    public function rules()
    {
        return [
            [['announcement_id', 'company_id', 'is_pi', 'is_pay', 'is_boshihou_pay'], 'integer'],
        ];
    }

    /**
     * {@inheritdoc}
     */
    public function attributeLabels()
    {
        return [
            'id' => 'ID',
            'announcement_id' => 'Announcement ID',
            'company_id' => 'Company ID',
            'is_pi' => 'Is Pi',
            'is_pay' => 'Is Pay',
            'is_boshihou_pay' => 'Is Boshihou Pay',
        ];
    }
}
