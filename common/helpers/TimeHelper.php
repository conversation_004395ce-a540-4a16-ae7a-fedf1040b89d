<?php

namespace common\helpers;

class TimeHelper
{

    const ZERO_TIME  = '0000-00-00 00:00:00';
    const ZERO_DATE  = '0000-00-00';
    const ZERO_MONTH = '0000-00';

    /**
     * 计算出合适的日期
     * @param string $datetime 原来的时间 (Y-m-d H:i:s)
     * @param int    $time     秒
     * @param int    $type     默认为1,也就是加,2的时候为减
     *
     * @return string 计算好的时间 (Y-m-d H:i:s)
     */

    public static function computeDatetime($datetime, $time, $type = 1)
    {
        $t = strtotime($datetime);

        if ($type == 1) {
            $t += $time;
        } elseif ($type == 2) {
            $t -= $time;
        }

        return date('Y-m-d H:i:s', $t);
    }

    /**
     * 计算两个时间直接的时间差
     *
     * @param $datetime1
     * @param $datetime2
     * @return int  相差的实际
     */
    public static function reduceDateTime($datetime1, $datetime2)
    {
        $time = strtotime($datetime1) - strtotime($datetime2);

        if ($time <= 0) {
            return '';
        }

        $date   = floor($time / 86400);
        $hour   = floor($time % 86400 / 3600);
        $minute = floor($time % 86400 % 3600 / 60);
        $second = floor($time % 86400 % 60);

        $string = '';
        if ($date) {
            $string .= $date . '天';
        }

        if ($hour) {
            $string .= $hour . '小时';
        }

        if ($minute) {
            $string .= $minute . '分钟';
        }

        if ($second) {
            $string .= $second . '秒';
        }

        return $string;
    }

    /**
     * 计算两个时间直接的时间差
     *
     * @return int  相差的实际
     */
    public static function reduceDates($date1, $date2)
    {
        $time = strtotime($date1) - strtotime($date2);

        if ($time <= 0) {
            return '';
        }

        $date = floor($time / 86400);

        return $date;
    }

    /**
     * 计算两个日期加
     *
     * @return int  相差的实际
     */
    public static function datePlus($date, $n)
    {
        $time = strtotime($date) + $n * 3600 * 24;

        return date('Y-m-d', $time);
    }

    /**
     * 判断是否周末
     * @param $date
     * @return bool
     */
    public static function isWeekend($date)
    {
        if ((date('w', strtotime($date)) == 6) || (date('w', strtotime($date)) == 0)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 根据周和年获取开始日期和结束日期(周一和周五)
     *
     * @param $week
     * @param $year
     */
    public static function getDateByWeek($week, $year = null)
    {
        if (!$year) {
            $year = CUR_YEAR;
        }

        // 拿出1号时间戳
        $fyear = strtotime($year . '-01-01');
        //今年第一天 周几
        $fdate = date('N', $fyear);

        if ($fdate == 1) {
            $addDay = ($week - 1) * 7;
        } else {
            $addDay = ($week - 1) * 7 + 1 - $fdate;
        }

        $monday = date('Y-m-d', $fyear + $addDay * 3600 * 24);
        $sunday = date('Y-m-d', $fyear + ($addDay + 6) * 3600 * 24);

        return [
            'monday' => $monday,
            'sunday' => $sunday,
        ];
    }

    /**
     * 获取,某天是第几周
     */
    public static function getWeek($day = null)
    {
        $day  = $day ?: CUR_DATE;
        $time = strtotime($day);
        //当前时间的月份
        $month = date('m', $time);
        //今年第一天时间戳
        $fyear = strtotime(date('Y-01-01', $time));
        //今年第一天 周几
        $fdate = date('N', $fyear);
        //系统时间的第几周
        $sysweek = date('W', $time);
        //大于等于52 且 当前月为1时， 返回1
        if (($sysweek >= 52 && $month == 1)) {
            return 1;
        } elseif ($fdate == 1) {
            //如果今年的第一天是周一,返回系统时间第几周
            return $sysweek;
        } else {
            //返回系统周+1
            return $sysweek;
        }
    }

    /**
     * 获取中文的周
     * @param $num
     */
    public static function getWeekChina($num)
    {
        $list = [
            '1' => '一',
            '2' => '二',
            '3' => '三',
            '4' => '四',
            '5' => '五',
            '6' => '六',
            '7' => '日',
        ];

        return '周' . $list[$num];
    }

    /**
     * 获取两个日期直接的所有天数
     * @param $beginDate
     * @param $endDate
     */
    public static function getDateList($beginDate, $endDate)
    {
        $return = [];

        $nextDate = $beginDate;
        while ($nextDate != $endDate) {
            $return[] = $nextDate;
            $nextDate = date('Y-m-d', strtotime("$nextDate +1 day"));
        }

        $return[] = $endDate;

        return $return;
    }

    /**
     * 根据年月拿上个月最后一天
     */
    public static function prevMonthLastDay($month)
    {
        $beginDay = $month . '-01';

        return date('Y-m-d', strtotime("$beginDay -1 day"));
    }

    /**
     * 上个月第一天
     * @param $month
     * @return false|string
     */
    public static function prevMonthFirstDay($month)
    {
        $beginDay = $month . '-01';

        return date('Y-m-01', strtotime("$beginDay -1 month"));
    }

    /**
     * 根据年月拿这个月第一天
     */
    public static function thisMonthFirstDay($month)
    {
        return $month . '-01';
    }

    /**
     * 根据年月拿这个月最后一天
     */
    public static function thisMonthLastDay($month)
    {
        $beginDay = $month . '-01';

        return date('Y-m-d', strtotime("$beginDay +1 month -1 day"));
    }

    /**
     * 根据年拿这个年第一天
     */
    public static function thisYearFirstDay($year)
    {
        return $year . '-01-01';
    }

    /**
     * 根据年拿这个年最后一天
     */
    public static function thisYearLastDay($year)
    {
        return $year . '-12-31';
    }

    /**
     * 根据年月拿下个月1号
     */
    public static function nextMonthFirstDay($month)
    {
        $beginDay = substr($month, 0, 7) . '-01';

        return date('Y-m-01', strtotime("$beginDay +1 month "));
    }

    /**
     * 根据年月拿下个月
     */
    public static function nextMonth($month)
    {
        $beginDay = $month . '-01';

        return date('Y-m', strtotime("$beginDay +1 month "));
    }

    /**
     * 列出开始到结束的每一天
     * @param $beginDay
     * @param $endDay
     * @return array|bool
     */
    public static function everyDayList($beginDay, $endDay)
    {
        // 保证两个是正常的日期
        $reduce = self::reduceDates($endDay, $beginDay);
        if ($reduce > 366 || $reduce < 1) {
            return false;
        }
        $list    = [$beginDay];
        $nextDay = $beginDay;
        while ($nextDay != $endDay) {
            $nextDay = date('Y-m-d', strtotime("$nextDay +1 day"));
            $list[]  = $nextDay;
        }

        return $list;
    }

    /**
     * 列出开始到结束的每一月
     * @param $beginMonth
     * @param $endMonth
     * @return array|bool
     */
    public static function everyMonthList($beginMonth, $endMonth)
    {
        // 保证两个是正常的日期
        $endDay   = date('Y-m-d', strtotime("$endMonth-01 +1 month -1 day"));
        $beginDay = $beginMonth . '-01';
        $reduce   = self::reduceDates($endDay, $beginDay);
        if ($reduce > 400 || $reduce < 1) {
            return false;
        }
        $list      = [$beginMonth];
        $nextMonth = $beginMonth;
        while ($nextMonth != $endMonth) {
            $nextMonth = self::nextMonth($nextMonth);
            $list[]    = $nextMonth;
        }

        return $list;
    }

    /**
     * 获取某天的前一天的日期
     * @param false|string $date
     * @return false|string
     */
    public static function getYesterday($date = CUR_DATE)
    {
        return date('Y-m-d', strtotime("$date -1 day"));
    }

    /**
     * 获取某天的前一天的日期
     * @param false|string $date
     * @return false|string
     */
    public static function getTomorrow($date = CUR_DATE)
    {
        return date('Y-m-d', strtotime("$date +1 day"));
    }

    /**
     * 获取某月的前一个月的月份
     * @param false|string $date
     * @return false|string
     */
    public static function getLastMonth($month = CUR_MONTH)
    {
        return date('Y-m', strtotime("$month-01 -1 month"));
    }

    /**
     * 获取某月的后一个月的月份
     * @param false|string $date
     * @return false|string
     */
    public static function getNextMonth($month = CUR_MONTH)
    {
        return date('Y-m', strtotime("$month-01 +1 month"));
    }

    /**
     * 获取是星期几
     * @param false|string $date
     * @return false|string
     */
    public static function getWeekDay($date = CUR_DATE)
    {
        $week    = date('w', strtotime($date));
        $weekArr = [
            '日',
            '一',
            '二',
            '三',
            '四',
            '五',
            '六',
        ];

        return $weekArr[$week];
    }

    /**
     * 列出开始到结束的每一月
     * @param $beginMonth
     * @param $endMonth
     * @return array|bool
     */
    public static function everyYear($beginYear, $endYear)
    {
        $list     = [$beginYear];
        $nextYear = $beginYear;
        while ($nextYear != $endYear) {
            $nextYear += 1;
            $list[]   = $nextYear;
        }

        return $list;
    }

    /**
     * 拼接开始时间
     * @param $day
     * @return string
     */
    public static function dayToBeginTime($day)
    {
        if (!empty($day)) {
            return $day . ' 00:00:00';
        } else {
            return '';
        }
    }

    /**
     * 拼接结束时间
     * @param $day
     * @return string
     */
    public static function dayToEndTime($day)
    {
        if (!empty($day)) {
            return $day . ' 23:59:59';
        } else {
            return '';
        }
    }

    /**
     * 月拼接开始时间
     * @param $day
     * @return string
     */
    public static function monthToBeginTime($month)
    {
        return $month . '-01 00:00:00';
    }

    /**
     * 月拼接结束时间
     * @param $day
     * @return string
     */
    public static function monthToEndTime($month)
    {
        return self::thisMonthLastDay($month) . ' 23:59:59';
    }

    /**
     * 年拼接开始时间
     * @param $day
     * @return string
     */
    public static function yearToBeginTime($year)
    {
        return $year . '-01-01 00:00:00';
    }

    /**
     * 年拼接结束时间
     * @param $day
     * @return string
     */
    public static function yearToEndTime($year)
    {
        return $year . '-12-31 23:59:59';
    }

    /**
     * --------------------------------------------------
     * 检查数据是否符合日期格式
     * --------------------------------------------------
     *
     * @param  [array]     $attribute
     * @param  [array]     $params    错误提示
     * <AUTHOR>
     * @since  2016-12-28
     */
    public function isDateDatetime($string)
    {
        if (date('Y-m-d H:i:s', strtotime($string)) !== $string) {
            return false;
        }

        return true;
    }

    /**
     * 根据时间计算年龄
     * @param $age
     * @return int|mixed|string
     */
    public static function countYears($age)
    {
        $age ? $age : $age = CUR_DATETIME;

        // 如果age只是到年月，如果是这种情况，就加上-01
        if (strlen($age) == 7) {
            $age = $age . '-01';
        }

        [
            $y1,
            $m1,
            $d1,
        ] = explode("-", $age);
        $now = strtotime("now");
        [
            $y2,
            $m2,
            $d2,
        ] = explode("-", date("Y-m-d", $now));
        $age = $y2 - $y1;
        // 如果现在的月份日期小于生日的就是未满一周岁
        if ((int)($m2 . $d2) < (int)($m1 . $d1)) {
            $age -= 1;
        }

        return $age;
    }

    /**
     * 根据时间计算工作经验
     * 规则：向下取整
     * 使用：num=0 0年 0<num<1 1年以内 num>=1 num年
     * @param string $begin_date
     * @return int|mixed|string
     */
    public static function workTime($begin_date)
    {
        $begin_date = $begin_date ?: CUR_DATE;//开始时间

        $begin_arr   = explode("-", $begin_date);
        $current_arr = explode("-", date("Y-m-d"));//当前时间
        $year_num    = $current_arr[0] - $begin_arr[0];//计算年份相差

        if ($year_num < 0) {
            $year_num = 0;
        } else {
            if (($year_num == 1) && intval($current_arr[1] . $current_arr[2]) - intval($begin_arr[1] . $begin_arr[2]) < 0) {
                $year_num = 0.1; //固定值 表明小于一年
            } elseif (($year_num == 0) && intval($current_arr[1] . $current_arr[2]) - intval($begin_arr[1] . $begin_arr[2]) > 0) {
                $year_num = 0.1; //固定值 表明小于一年
            } elseif ($year_num > 1 && intval($current_arr[1] . $current_arr[2]) - intval($begin_arr[1] . $begin_arr[2]) < 0) {
                $year_num -= 1; //当前月日小于开始计算月日说明
            }
        }

        return $year_num;
    }

    /**
     * 计算出两个日期的相差年份
     * @param $date1
     * @param $date2
     * @return false|float
     */
    public static function countDifferYears($date1, $date2)
    {
        $days = self::reduceDates($date1, $date2);
        if (!empty($days)) {
            return floor($days / 365);
        }
    }

    /**
     * --------------------------------------------------
     * 将标准时间转为中文时间
     * --------------------------------------------------
     *
     * @return [type]     [description]
     * @since  2017-02-15
     * <AUTHOR>
     */
    public static function convertDateToCN($time)
    {
        // 今天凌晨的时间戳
        $today = strtotime('today');
        // 昨天凌晨的时间戳
        $yesterday = strtotime('yesterday');
        // 传过来的时间戳
        $timeTamp = strtotime($time);

        if ($timeTamp > $today) {
            // 今天的文章
            $sub = CUR_TIMESTAMP - $timeTamp;
            if ($sub == '0') {
                return '刚刚';
            }
            if ($sub < 60) {
                return $sub . '秒前';
            }

            if ($sub < 3600) {
                return floor($sub / 60) . '分钟前';
            }

            return floor($sub / 3600) . '小时前';
        }

        if ($timeTamp > $yesterday) {
            return '昨天';
        }

        return substr($time, 5, 5);
    }

    /**
     * 把时间戳转化为倒计时
     *
     * 只有时分秒,如果大于24小时,也加到小时数里面去
     * 例如: 30:25:35
     *
     */
    public static function converTimeStampToCountDown($timeStamp)
    {
        // 首先计算有多少天
        $day = intval(floor($timeStamp / (24 * 3600)));

        if ($day >= 1) {
            // 超过了一天
            return ($day * 24 + date('H', $timeStamp)) . ':' . date('i:s', $timeStamp);
        } else {
            // 一天以内
            return date('H:i:s', $timeStamp);
        }
    }

    /**
     * 计算两个日期的日差
     *
     * @param $d1
     * @param $d2
     * @return int
     */
    public static function computeDaySub($d1, $d2)
    {
        $second1 = strtotime($d1);
        $second2 = strtotime($d2);

        if ($second1 < $second2) {
            $tmp     = $second2;
            $second2 = $second1;
            $second1 = $tmp;
        }

        return ($second1 - $second2) / 86400;
    }

    public static function subTime($d1, $d2)
    {
        $date1 = strtotime($d1);

        $date2 = strtotime($d2);

        //计算两个日期之间的时间差

        $diff = abs($date2 - $date1);

        //转换时间差的格式

        $years = floor($diff / (365 * 60 * 60 * 24));

        $months = floor(($diff - $years * 365 * 60 * 60 * 24) / (30 * 60 * 60 * 24));

        $days = floor(($diff - $years * 365 * 60 * 60 * 24 - $months * 30 * 60 * 60 * 24) / (60 * 60 * 24));

        $hours = floor(($diff - $years * 365 * 60 * 60 * 24 - $months * 30 * 60 * 60 * 24 - $days * 60 * 60 * 24) / (60 * 60));

        $minutes = floor(($diff - $years * 365 * 60 * 60 * 24 - $months * 30 * 60 * 60 * 24 - $days * 60 * 60 * 24 - $hours * 60 * 60) / 60);

        $seconds = floor(($diff - $years * 365 * 60 * 60 * 24 - $months * 30 * 60 * 60 * 24 - $days * 60 * 60 * 24 - $hours * 60 * 60 - $minutes * 60));

        $return = '';
        if ($years) {
            $return .= $years . '年';
        }
        if ($months) {
            $return .= $months . '月';
        }

        if ($days) {
            $return .= $days . '天';
        }

        if ($hours) {
            $return .= $hours . '小时';
        }

        if ($minutes) {
            $return .= $minutes . '分';
        }

        $return .= $seconds . '秒';

        return $return;
    }

    public static function short($time)
    {
        $time = strtotime($time);

        return date('m.d', $time);
    }

    public static function getChinese($date)
    {
        $date = strtotime($date);

        return date('Y年n月j日', $date);
    }

    /**
     * 需求变化太多，这里加个链接符号，方便后续使用
     * 格式化日期，只保留年-月
     * @param $date
     * @return false|string
     */
    public static function formatToYearMonth($date, $connect = '-')
    {
        if ($date == self::ZERO_DATE || $date == self::ZERO_TIME || empty($date)) {
            return '';
        }
        $time = strtotime($date);

        return date('Y' . $connect . 'm', $time);
    }

    /**
     * 格式化日期，将年月加上日
     * @param $date
     * @return string
     */
    public static function formatAddDay($date): string
    {
        return $date . '-01';
    }

    public static function formatAddZeroDay($date): string
    {
        return $date . '-00';
    }

    /**
     * 将日期格式化为年月日，前端可能传年月，可能传年月日
     * @param $date
     * @return false|string
     */
    public static function formatFullDate($date)
    {
        //如果是0000-00或者0000-00-00，返回0000-00
        if ($date == self::ZERO_DATE || $date == self::ZERO_MONTH) {
            return self::ZERO_DATE;
        } else {
            $time = strtotime($date);

            return date('Y-m-d', $time);
        }
    }

    /**
     * 将年-月格式的日期，转化为{'年'，'-'，'月'}
     * @param $date
     * @return false|string
     */
    public static function setH5DefaultDateArr($date)
    {
        if (empty($date)) {
            return '';
        }
        $arr = explode('-', $date);

        return json_encode([
            $arr[0],
            '-',
            $arr[1],
        ]);
    }

    public static function shortSlash($time)
    {
        $time = strtotime($time);

        return date('m/d', $time);
    }

    /**
     * 判断时间是否是默认的0000-00-00格式
     * @param $date
     * @return bool
     */
    public static function checkIsZeroTimeOrDate($date): bool
    {
        if ($date == self::ZERO_DATE || $date == self::ZERO_TIME) {
            return true;
        } else {
            return false;
        }
    }

    public static function getLong($date)
    {
        $date = strtotime($date);

        return date('Y.m.d', $date);
    }

    /**
     * 需求变化太多了，这里加个链接符号，方便后续使用
     * 判断日期是否是在今年的，如果是今年的日期，返回月-日，如果不是今年的，返回年-月-日
     * @param $date
     * @return false|string
     */
    public static function formatDateByYear($date, $connect = '-')
    {
        // $thisYearTime = mktime(0, 0, 0, 1, 1, date('Y'));
        // 获取当前年初的时间戳
        $time     = strtotime($date);
        $dateYear = date('Y', $time);
        $thisYear = date('Y');
        if ($dateYear == $thisYear) {
            $resultDate = date('m' . $connect . 'd', $time);
        } else {
            $resultDate = date('Y' . $connect . 'm' . $connect . 'd', $time);
        }

        return $resultDate;
    }

    /**
     * （当天显示时:分；当年显示 月-日；跨年显示 年-月-日）
     */
    public static function formatDateMinute($time = CUR_DATETIME)
    {
        $time = strtotime($time);
        if (date('Y-m-d') == date('Y-m-d', $time)) {
            return date('H:i', $time);
        } elseif (date('Y') == date('Y', $time)) {
            return date('m-d', $time);
        } else {
            return date('Y-m-d', $time);
        }
    }

    /**
     * 格式化聊天消息显示时间
     * 当天，显示 时-分，如: 12:00；
     * 当年，显示 月-日 时:分，如: 08-15 12:00；
     * 跨年，显示 年-月-日 时:分，如: 2023-08-15 12:00；
     * @param $time
     * @return false|string
     */
    public static function formatMessageShowTime($time)
    {
        $time = strtotime($time);
        if (date('Y-m-d') == date('Y-m-d', $time)) {
            return date('H:i', $time);
        } elseif (date('Y') == date('Y', $time)) {
            return date('m-d H:i', $time);
        } else {
            return date('Y-m-d H:i', $time);
        }
    }

    /**
     * 格式化聊天消息显示时间
     * 当天，显示 时-分，如: 12:00；
     * 当年，显示 月-日，如: 08-15 ；
     * 跨年，显示 年-月-日，如: 2023-08-15；
     * @param $time
     * @return false|string
     */
    public static function formatMessageSessionShowTime($time)
    {
        $time = strtotime($time);
        if (date('Y-m-d') == date('Y-m-d', $time)) {
            return date('H:i', $time);
        } elseif (date('Y') == date('Y', $time)) {
            return date('m-d', $time);
        } else {
            return date('Y-m-d', $time);
        }
    }

    public static function getMinuteChinese($date)
    {
        $date = strtotime($date);

        return date('Y年n月j日 H时i分', $date);
    }

    // 判断是否是工作日(周一到周五)
    public static function isWorkDay($date)
    {
        $week = date('w', strtotime($date));
        if ($week == 0 || $week == 6) {
            return false;
        } else {
            return true;
        }
    }

    // 判断是否是工作时间(周一到周五),上午9点到中午12点,下午1点半到5点
    public static function isWorkTime()
    {
        $week   = date('w');
        $hour   = date('H');
        $minute = date('i');
        if ($week == 0 || $week == 6) {
            return false;
        } else {
            if ($hour < 9 || $hour > 17) {
                return false;
            } else {
                if ($hour == 12 && $minute > 0) {
                    return false;
                } else {
                    if ($hour == 13 && $minute < 30) {
                        return false;
                    } else {
                        return true;
                    }
                }
            }
        }
    }

    // 得加上年判断
    public static function isThisWeek($day)
    {
        $week = date('W', strtotime($day));
        $year = date('Y', strtotime($day));
        $now  = date('W');
        $nowY = date('Y');
        if ($week == $now && ($nowY == $year || $nowY > $year)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 计算相差特定天数日期
     * @param $inputDate string 特定日期
     * @param $days      int 相差天数
     * @param $isFuture  int 未来还是曾经；1:未来天数；2:曾经的天数
     * @param $format    string 日期返回的格式
     * @return string
     * @throws \Exception
     */
    public static function calculateDifferenceDate($inputDate, $days, $isFuture = 1, $format = 'Y-m-d'): string
    {
        $date = new \DateTime($inputDate);

        if ($isFuture == 1) {
            $date->modify("+$days days"); // 未来日期
        } else {
            $date->modify("-$days days"); // 过去日期
        }

        return $date->format($format);
    }

    /**
     * 返回当前星期几
     * @param $timestamp
     * @return string
     */
    public static function getTimestampWeekday($timestamp)
    {
        $weekdays = [
            'Sun' => '日',
            'Mon' => '一',
            'Tue' => '二',
            'Wed' => '三',
            'Thu' => '四',
            'Fri' => '五',
            'Sat' => '六',
        ];

        $dayOfWeek = date('D', $timestamp); // 获取英文星期几
        return $weekdays[$dayOfWeek] ?? '未知'; // 转换为中文
    }
}
