<?php
/**
 * @link      https://www.yiiframework.com/
 * @copyright Copyright (c) 2008 Yii Software LLC
 * @license   https://www.yiiframework.com/license/
 */

namespace common\components;

class MessageException extends \Exception
{
    public function getName()
    {
        return 'Error';
    }

    public function __construct($message, $code = 0, \Exception $previous = null)
    {
        // 这种情况不发送邮件

        parent::__construct($message, $code, $previous);
    }
}
