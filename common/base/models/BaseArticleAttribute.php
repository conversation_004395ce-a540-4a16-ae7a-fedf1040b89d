<?php

namespace common\base\models;

use common\helpers\TimeHelper;
use common\models\ArticleAttribute;
use yii\base\Exception;

class BaseArticleAttribute extends ArticleAttribute
{
    const ATTRIBUTE_ROLLING          = 1; //公告+资讯  滚动
    const ATTRIBUTE_RECOMMEND        = 2; //公告+资讯  推荐
    const ATTRIBUTE_HOME_TOP         = 3; //公告+资讯  首页置顶
    const ATTRIBUTE_COLUMN_TOP       = 4; //公告+资讯  栏目置顶
    const ATTRIBUTE_HOME_TOP_ONE     = 5; //公告+资讯  首头1
    const ATTRIBUTE_HOME_TOP_TWO     = 6; //公告+资讯  首头2
    const ATTRIBUTE_COLUMN_HEAD      = 7; //公告+资讯  栏目头条
    const ATTRIBUTE_FOCUS            = 8; //公告   焦点
    const ATTRIBUTE_HOME             = 9; //公告   首页
    const ATTRIBUTE_HOME_TOP_THREE   = 10; //资讯   首头3
    const ATTRIBUTE_HOT_IMAGE        = 11; //资讯  热图
    const ATTRIBUTE_HOT_ARTICLE      = 12; //资讯  热文
    const ATTRIBUTE_JUMP             = 13; //资讯 跳转
    const ATTRIBUTE_HOME_TOP_FOUR    = 14; //资讯 首头4
    const ATTRIBUTE_HOME_TOP_FIVE    = 15; //资讯 首头5
    const ATTRIBUTE_SERIES           = 16; //资讯 系列
    const ATTRIBUTE_PREVIOUS_PROJECT = 18; //公告 往届专题
    const ATTRIBUTE_EXCELLENT        = 19; //公告 优质
    const ATTRIBUTE_COMPILE          = 20; //公告 有编制
    const ATTRIBUTE_PI               = 21; //公告 PI直招
    const ATTRIBUTE_DOCTOR_PUSH      = 22; //公告 博后加推

    // 专门给专题页临时使用的标签,往后随时有可能删除掉,所以在这里用特殊的值来标记
    const ATTRIBUTE_ZT_101 = 101; //公告 普本热招
    const ATTRIBUTE_ZT_102 = 102; //公告 普本最新
    const ATTRIBUTE_ZT_103 = 103; //公告 辅导员热招
    const ATTRIBUTE_ZT_104 = 104; //公告 辅导员最新
    const ATTRIBUTE_ZT_105 = 105; //公告 辅导员最新
    const ATTRIBUTE_ZT_106 = 106; //公告 辅导员最新
    const ATTRIBUTE_ZT_107 = 107; //公告 医学热招
    const ATTRIBUTE_ZT_108 = 108; //公告 医学最新
    const ATTRIBUTE_ZT_109 = 109; //公告 省考
    const ATTRIBUTE_ZT_110 = 110; //公告 论坛

    // 这里面是海外的调用属性 分别有
    const ATTRIBUTE_HOME_HAIYOU                      = 201; // 首页-海优
    const ATTRIBUTE_HOME_ANNOUNCEMENT_TOP            = 202; // 首页公告-置顶
    const ATTRIBUTE_HOME_HAIYOU_TOP                  = 203; // 海优-首头
    const ATTRIBUTE_HOME_HAIYOU_SECOND               = 204; //  海优-次条
    const ATTRIBUTE_HOME_ANNOUNCEMENT_QIUXIAN_TOP    = 205; // 求贤公告-首头
    const ATTRIBUTE_HOME_ANNOUNCEMENT_QIUXIAN_SECOND = 206; // 求贤公告-次条
    const ATTRIBUTE_COLUMN_ANNOUNCEMENT_TOP          = 207; //  栏目公告-置顶
    const ATTRIBUTE_HOME_ACTIVITY                    = 208; // 国内活动
    const ATT_ABROAD_ACTIVITY                        = 209; // 海外活动
    const ATT_HAIYOU_APPLY_ACTIVITY                  = 210; // 海优-申报动态
    const ATT_HAIYOU_APPLY_DRY                       = 211; // 海优-申报干货
    const ATT_HAIYOU_NEWS                            = 213; // 海优-资讯
    const ATT_HAIYOU_ACTIVITY                        = 214; // 活动快讯

    /**
     * 统一一下方便修改
     */
    const COLUMN_RECOMMEND_ATTRIBUTE = [
        self::ATTRIBUTE_ROLLING,
        self::ATTRIBUTE_RECOMMEND,
        self::ATTRIBUTE_COLUMN_TOP,
        self::ATTRIBUTE_COLUMN_HEAD,
        self::ATTRIBUTE_FOCUS,
    ];

    // 公告 文档属性列表
    const  ATTRIBUTE_ANNOUNCEMENT_LIST = [
        self::ATTRIBUTE_ROLLING                          => '滚动',
        self::ATTRIBUTE_RECOMMEND                        => '推荐',
        self::ATTRIBUTE_HOME_TOP                         => '首页置顶',
        self::ATTRIBUTE_COLUMN_TOP                       => '栏目置顶',
        self::ATTRIBUTE_HOME_TOP_ONE                     => '首头1',
        self::ATTRIBUTE_HOME_TOP_TWO                     => '首头2',
        self::ATTRIBUTE_COLUMN_HEAD                      => '栏目头条',
        self::ATTRIBUTE_FOCUS                            => '焦点',
        self::ATTRIBUTE_HOME                             => '首页',
        self::ATTRIBUTE_COMPILE                          => '编制',
        self::ATTRIBUTE_PI                               => 'PI直招',
        self::ATTRIBUTE_DOCTOR_PUSH                      => '博后加推',
        // self::ATTRIBUTE_ZT_101       => '普本热招',
        // self::ATTRIBUTE_ZT_102       => '普本最新',
        // self::ATTRIBUTE_ZT_103       => '辅导员热招',
        // self::ATTRIBUTE_ZT_104       => '辅导员最新',
        // self::ATTRIBUTE_ZT_105       => '高校教师热招',
        // self::ATTRIBUTE_ZT_106       => '高校教师最新',
        // self::ATTRIBUTE_ZT_107       => '医学热招',
        // self::ATTRIBUTE_ZT_108       => '医学最新',
        self::ATTRIBUTE_ZT_109                           => '省考',
        // self::ATTRIBUTE_PREVIOUS_PROJECT => '应届专题',
        // self::ATTRIBUTE_EXCELLENT        => '优质',
        self::ATTRIBUTE_ZT_110                           => '论坛',
        self::ATTRIBUTE_HOME_HAIYOU                      => '首页-海优',
        self::ATTRIBUTE_HOME_ANNOUNCEMENT_TOP            => '首页公告-置顶',
        self::ATTRIBUTE_HOME_HAIYOU_TOP                  => '海优-首头',
        self::ATTRIBUTE_HOME_HAIYOU_SECOND               => '海优-次条',
        self::ATTRIBUTE_HOME_ANNOUNCEMENT_QIUXIAN_TOP    => '求贤公告-首头',
        self::ATTRIBUTE_HOME_ANNOUNCEMENT_QIUXIAN_SECOND => '求贤公告-次条',
        self::ATTRIBUTE_COLUMN_ANNOUNCEMENT_TOP          => '栏目公告-置顶',
        self::ATTRIBUTE_HOME_ACTIVITY                    => '国内活动',
    ];

    const ATTRIBUTE_ANNOUNCEMENT_NOT_HAW_WAI_LIST = [
        self::ATTRIBUTE_ROLLING      => '滚动',
        self::ATTRIBUTE_RECOMMEND    => '推荐',
        self::ATTRIBUTE_HOME_TOP     => '首页置顶',
        self::ATTRIBUTE_COLUMN_TOP   => '栏目置顶',
        self::ATTRIBUTE_HOME_TOP_ONE => '首头1',
        self::ATTRIBUTE_HOME_TOP_TWO => '首头2',
        self::ATTRIBUTE_COLUMN_HEAD  => '栏目头条',
        self::ATTRIBUTE_FOCUS        => '焦点',
        self::ATTRIBUTE_HOME         => '首页',
        self::ATTRIBUTE_COMPILE      => '编制',
        self::ATTRIBUTE_ZT_109       => '省考',
        self::ATTRIBUTE_ZT_110       => '论坛',
        self::ATTRIBUTE_PI           => 'PI直招',
        self::ATTRIBUTE_DOCTOR_PUSH  => '博后加推',
    ];

    // 资讯 文档属性列表
    const  ATTRIBUTE_NEWS_LIST = [
        self::ATTRIBUTE_ROLLING        => '滚动',
        self::ATTRIBUTE_RECOMMEND      => '推荐',
        self::ATTRIBUTE_HOME_TOP       => '首页置顶',
        self::ATTRIBUTE_COLUMN_TOP     => '栏目置顶',
        self::ATTRIBUTE_HOME_TOP_ONE   => '首头1',
        self::ATTRIBUTE_HOME_TOP_TWO   => '首头2',
        self::ATTRIBUTE_COLUMN_HEAD    => '栏目头条',
        self::ATTRIBUTE_HOME_TOP_THREE => '首头3',
        self::ATTRIBUTE_HOME_TOP_FOUR  => '首头4',
        self::ATTRIBUTE_HOME_TOP_FIVE  => '首头5',
        self::ATTRIBUTE_HOT_IMAGE      => '热图',
        self::ATTRIBUTE_HOT_ARTICLE    => '热文',
        self::ATTRIBUTE_JUMP           => '跳转',
        self::ATTRIBUTE_SERIES         => '系列',
    ];

    const  ATTRIBUTE_NEWS_HAW_WAI_LIST = [
        self::ATT_ABROAD_ACTIVITY       => '海外活动',
        self::ATTRIBUTE_HOME_ACTIVITY   => '国内活动',
        self::ATT_HAIYOU_APPLY_ACTIVITY => '海优-申报动态',
        self::ATT_HAIYOU_APPLY_DRY      => '海优-申报干货',
        self::ATT_HAIYOU_NEWS           => '海优-资讯',
    ];

    const ATTRIBUTE_LIST = [
        self::ATTRIBUTE_ROLLING        => '滚动',
        self::ATTRIBUTE_RECOMMEND      => '推荐',
        self::ATTRIBUTE_HOME_TOP       => '首页置顶',
        self::ATTRIBUTE_COLUMN_TOP     => '栏目置顶',
        self::ATTRIBUTE_HOME_TOP_ONE   => '首头1',
        self::ATTRIBUTE_HOME_TOP_TWO   => '首头2',
        self::ATTRIBUTE_COLUMN_HEAD    => '栏目头条',
        self::ATTRIBUTE_FOCUS          => '焦点',
        self::ATTRIBUTE_HOME           => '首页',
        self::ATTRIBUTE_HOME_TOP_THREE => '首头3',
        self::ATTRIBUTE_HOT_IMAGE      => '热图',
        self::ATTRIBUTE_HOT_ARTICLE    => '热文',
        self::ATTRIBUTE_JUMP           => '跳转',
        self::ATTRIBUTE_HOME_TOP_FOUR  => '首头4',
        self::ATTRIBUTE_HOME_TOP_FIVE  => '首头5',
        self::ATTRIBUTE_SERIES         => '系列',
        self::ATTRIBUTE_COMPILE        => '编制',
        self::ATTRIBUTE_PI             => 'PI直招',
        self::ATTRIBUTE_DOCTOR_PUSH    => '博后加推',
    ];

    const ANNOUNCEMENT_ATTRIBUTE_ABROAD_LIST = [
        self::ATTRIBUTE_HOME_HAIYOU                      => '首页-海优',
        self::ATTRIBUTE_HOME_ANNOUNCEMENT_TOP            => '首页公告-置顶',
        self::ATTRIBUTE_HOME_HAIYOU_TOP                  => '海优-首头',
        self::ATTRIBUTE_HOME_HAIYOU_SECOND               => '海优-次条',
        self::ATTRIBUTE_HOME_ANNOUNCEMENT_QIUXIAN_TOP    => '求贤公告-首头',
        self::ATTRIBUTE_HOME_ANNOUNCEMENT_QIUXIAN_SECOND => '求贤公告-次条',
        self::ATTRIBUTE_COLUMN_ANNOUNCEMENT_TOP          => '栏目公告-置顶',
        self::ATTRIBUTE_HOME_ACTIVITY                    => '国内活动',
    ];
    const NEWS_ATTRIBUTE_ABROAD_LIST         = [
        self::ATT_ABROAD_ACTIVITY       => '海外活动',
        self::ATTRIBUTE_HOME_ACTIVITY   => '国内活动',
        self::ATT_HAIYOU_APPLY_ACTIVITY => '海优-申报动态',
        self::ATT_HAIYOU_APPLY_DRY      => '海优-申报干货',
        self::ATT_HAIYOU_NEWS           => '海优-资讯',
        self::ATT_HAIYOU_ACTIVITY       => '活动快讯',
    ];
    //时间字段对应的属性类型
    const ATTRIBUTE_TIME_TYPE_LIST = [
        self::ATTRIBUTE_HOME_TOP                => 'indexTopEndTime',
        self::ATTRIBUTE_COLUMN_TOP              => 'columnTopEndTime',
        self::ATTRIBUTE_DOCTOR_PUSH             => 'doctorPushEndTime',
        self::ATTRIBUTE_HOME_ANNOUNCEMENT_TOP   => 'overseasIndexTopEndTime',
        self::ATTRIBUTE_COLUMN_ANNOUNCEMENT_TOP => 'overseasColumnTopEndTime',
    ];

    /**
     * 创建公告属性信息
     * @param int $articlId
     * @return bool
     * @throws Exception
     */
    public static function createAttribute($articlId, $attribute)
    {
        if ($articlId && $attribute) {
            if (is_array($attribute)) {
                //检查是否有相同的文档属性值
                $isUnique = array_column($attribute, 'type');
                if (count($isUnique) != count(array_unique($isUnique))) {
                    throw new Exception('文档属性不得重复，请刷新页面重试');
                }
                //删除原来的新增后面的
                self::deleteAll([
                    'and',
                    [
                        'not in',
                        'type',
                        $isUnique,
                    ],
                    [
                        '=',
                        'article_id',
                        $articlId,
                    ],
                ]);

                //1，2  2，3
                foreach ($attribute as $v) {
                    if (!empty($v['type'])) {
                        $typeVal = self::findOne([
                            'article_id' => $articlId,
                            'type'       => $v['type'],
                        ]);
                        if ($typeVal) {
                            //只更新过期时间，不更新排序时间
                            $typeVal->expire_time = $v['expireTime'] ?: TimeHelper::ZERO_TIME;
                            if (!$typeVal->save()) {
                                throw new Exception($typeVal->getFirstErrorsMessage());
                            }
                        } else {
                            $model              = new self();
                            $model->type        = $v['type'];
                            $model->article_id  = $articlId;
                            $model->sort_time   = CUR_DATETIME;
                            $model->expire_time = $v['expireTime'] ?: TimeHelper::ZERO_TIME;
                            if (!$model->save()) {
                                throw new Exception($model->getFirstErrorsMessage());
                            }
                        }
                    } else {
                        throw new Exception('属性值不得为空，请刷新页面重试');
                    }
                }

                return true;
            } else {
                $type = explode(',', $attribute);
                //删除原来的新增后面的
                self::deleteAll([
                    'and',
                    [
                        'not in',
                        'type',
                        $type,
                    ],
                    [
                        '=',
                        'article_id',
                        $articlId,
                    ],
                ]);
                foreach ($type as $v) {
                    $typeVal = self::findOne([
                        'article_id' => $articlId,
                        'type'       => $v,
                    ]);
                    if ($typeVal) {
                        continue;
                    } else {
                        $model             = new self();
                        $model->type       = $v;
                        $model->article_id = $articlId;
                        $model->sort_time  = CUR_DATETIME;
                        if (!$model->save()) {
                            throw new Exception($model->getFirstErrorsMessage());
                        }
                    }
                }
            }

            return true;
        } elseif ($articlId && !$attribute) {
            // 不选择文档属性，删除文档属性记录
            self::deleteAll(['article_id' => $articlId]);
        } else {
            return false;
        }
    }

    /**
     * 刷新公告属性排序时间
     * @param $articlId
     * @param $attributeId
     * @throws Exception
     */
    public static function refreshSortTime($articlId, $attributeId)
    {
        $articleInfo = BaseArticle::find()
            ->where([
                'id'        => $articlId,
                'status'    => BaseArticle::STATUS_ONLINE,
                'is_show'   => BaseArticle::IS_SHOW_YES,
                'is_delete' => BaseArticle::STATUS_DELETE,
            ])
            ->one();
        if (!$articleInfo) {
            throw new Exception('该公告不支持文档属性刷新');
        }

        $attributeInfo = self::findOne([
            'article_id' => $articlId,
            'type'       => $attributeId,
        ]);
        if (!$attributeInfo) {
            throw new Exception('公告文档属性不存在');
        }

        $attributeInfo->sort_time = CUR_DATETIME;
        if (!$attributeInfo->save()) {
            throw new Exception($attributeInfo->getFirstErrorsMessage());
        }

        return true;
    }

    /**
     * 获取文档属性列表
     * @param $article
     */
    public static function getArticleAttribute($articleId)
    {
        return implode(',', self::find()
            ->select(['type'])
            ->where(['article_id' => $articleId])
            ->column());
    }

    public static function splitAttribute($articleId)
    {
        $allList = self::find()
            ->select(['type'])
            ->where(['article_id' => $articleId])
            ->column();

        $attribute       = [];
        $abroadAttribute = [];
        if ($allList) {
            foreach ($allList as $v) {
                if (self::NEWS_ATTRIBUTE_ABROAD_LIST[$v]) {
                    $abroadAttribute[] = $v;
                } else {
                    $attribute[] = $v;
                }
            }
        }

        return [
            'attribute'       => implode(',', $attribute),
            'abroadAttribute' => implode(',', $abroadAttribute),
        ];
    }

    /**
     * 获取文档属性名称
     * @param $article
     */
    public static function getArticleAttributeName($articleId)
    {
        $list = self::find()
            ->select(['type'])
            ->where(['article_id' => $articleId])
            ->column();

        if (!$list) {
            return '';
        }

        $attributeName = [];
        foreach ($list as $v) {
            $attributeName[] = self::ATTRIBUTE_LIST[$v];
        }

        // 逗号隔开
        return implode(',', $attributeName);
    }

    /**
     * 获取组合文档属性列表
     * @param $article
     */
    public static function getComboAttribute($articleId)
    {
        return self::find()
            ->select('type,expire_time')
            ->where(['article_id' => $articleId])
            ->asArray()
            ->all();
    }

    /**
     * 获取文章文档属性、海外属性、置顶到期时间
     * @param $articleId
     * @return array
     */
    public static function getComboAttributeInfo($articleId)
    {
        $list              = self::find()
            ->select('type,expire_time')
            ->where(['article_id' => $articleId])
            ->asArray()
            ->all();
        $data              = [];
        $comboAttribute    = [];
        $overseasAttribute = [];
        foreach ($list as $item) {
            //获取类型对应的时间字段
            if (self::ATTRIBUTE_TIME_TYPE_LIST[$item['type']]) {
                if ($item['expire_time'] == TimeHelper::ZERO_TIME) {
                    $data[self::ATTRIBUTE_TIME_TYPE_LIST[$item['type']]] = '';
                } else {
                    $data[self::ATTRIBUTE_TIME_TYPE_LIST[$item['type']]] = date('Y-m-d',
                        strtotime($item['expire_time']));
                }
            }
            if (self::ANNOUNCEMENT_ATTRIBUTE_ABROAD_LIST[$item['type']]) {
                $overseasAttribute[] = $item['type'];
            } else {
                $comboAttribute[] = $item['type'];
            }
        }
        $data['comboAttribute']    = $comboAttribute ?: [];
        $data['overseasAttribute'] = $overseasAttribute ?: [];

        return $data;
    }

    /**
     * 删除文档属性
     * @param $article
     */
    public static function deleteAttribute($articleId)
    {
        $attributeList = self::find()
            ->where(['article_id' => $articleId])
            ->select('id')
            ->asArray()
            ->all();
        if (!$attributeList) {
            return false;
        }

        foreach ($attributeList as $item) {
            $model = self::findOne($item['id']);
            $model->delete();
        }

        return true;
    }

    /**
     * 格式化属性数组，用于后续保存
     * @param $data
     * @return array
     */
    public static function formatAttributeList($data)
    {
        if ($data['attribute']) {
            $info = [];
            foreach ($data['attribute'] as $k => $v) {
                $info[$k]['expireTime'] = '';
                if (self::ATTRIBUTE_TIME_TYPE_LIST[$v] && $data[self::ATTRIBUTE_TIME_TYPE_LIST[$v]]) {
                    $info[$k]['expireTime'] = $data[self::ATTRIBUTE_TIME_TYPE_LIST[$v]];
                }

                $info[$k]['type'] = $v;
            }

            return $info;
        }

        return [];
    }
}