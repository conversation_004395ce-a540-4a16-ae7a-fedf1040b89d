<?php

namespace common\base\models;

use common\models\JobApplyRecordExtra;

class BaseJobApplyRecordExtra extends JobApplyRecordExtra
{
    /**
     * 投递数据进行统计汇总
     * @param $jobId
     * @param $resumeId
     * @param $platform
     * @return bool
     */
    public static function JobApplyCount($jobId, $resumeId, $platform)
    {
        //获取职位信息
        $jobInfo = BaseJob::findOne($jobId);
        //职位存在否
        if (!$jobInfo) {
            return true;
        }
        //看下统计表里面有没有数据
        $jobApplyCount = BaseJobApplyRecordExtra::findOne($jobId);
        if (!$jobApplyCount) {
            //初始化一下
            $model         = new BaseJobApplyRecordExtra();
            $model->job_id = $jobId;
            $model->save();
        }
        $update = [];
        //总数加一
        $update['total'] = 1;
        //判断投递方式拿职位还是公告
        if ($jobInfo->delivery_type > 0 && $jobInfo->delivery_way > 0) {
            if ($jobInfo->delivery_type == BaseJob::DELIVERY_TYPE_OUTER) {
                $update['delivery_type_outer'] = 1;
            } elseif ($jobInfo->delivery_type == BaseJob::DELIVERY_TYPE_OUTSIDE) {
                $update['delivery_type_outside'] = 1;
            }
            if ($jobInfo->delivery_way == BaseJob::DELIVERY_WAY_LINK) {
                $update['delivery_way_link'] = 1;
            } elseif ($jobInfo->delivery_way == BaseJob::DELIVERY_WAY_EMAIL) {
                $update['delivery_way_email'] = 1;
            } elseif ($jobInfo->delivery_way == BaseJob::DELIVERY_WAY_PLATFORM) {
                $update['delivery_way_platform'] = 1;
            }
        } else {
            //用公告的去处理
            if ($jobInfo->announcement_id > 0) {
                //获取公告信息
                $announcementInfo = BaseAnnouncement::findOne($jobInfo->announcement_id);
                if ($announcementInfo->delivery_type == BaseAnnouncement::DELIVERY_TYPE_OUTER) {
                    $update['delivery_type_outer'] = 1;
                } elseif ($announcementInfo->delivery_type == BaseAnnouncement::DELIVERY_TYPE_OUTSIDE) {
                    $update['delivery_type_outside'] = 1;
                }
                if ($announcementInfo->delivery_way == BaseAnnouncement::DELIVERY_WAY_LINK) {
                    $update['delivery_way_link'] = 1;
                } elseif ($announcementInfo->delivery_way == BaseAnnouncement::DELIVERY_WAY_EMAIL) {
                    $update['delivery_way_email'] = 1;
                } elseif ($announcementInfo->delivery_way == BaseAnnouncement::DELIVERY_WAY_PLATFORM) {
                    $update['delivery_way_platform'] = 1;
                }
            }
        }
        //判断投递平台
        if ($platform == BaseJobApplyRecord::PLATFORM_PC) {
            $update['platform_pc'] = 1;
        } elseif ($platform == BaseJobApplyRecord::PLATFORM_H5) {
            $update['platform_h5'] = 1;
        } elseif ($platform == BaseJobApplyRecord::PLATFORM_MINI) {
            $update['platform_mini'] = 1;
        } elseif ($platform == BaseJobApplyRecord::PLATFORM_APP) {
            $update['platform_app'] = 1;
        }
        //投递简历的数据
        //获取简历信息
        $resumeInfo = BaseResume::findOne($resumeId);
        //学历
        switch ($resumeInfo->top_education_code) {
            case BaseResumeEducation::EDUCATION_TYPE_OTHER_CODE:
                $update['education_other'] = 1;
                break;
            case BaseResumeEducation::EDUCATION_TYPE_DOCTOR_CODE:
                $update['education_doctor'] = 1;
                break;
            case BaseResumeEducation::EDUCATION_TYPE_MASTER_CODE:
                $update['education_master'] = 1;
                break;
            case BaseResumeEducation::EDUCATION_TYPE_UNDERGRADUATE_CODE:
                $update['education_undergraduate'] = 1;
                break;
            case BaseResumeEducation::EDUCATION_TYPE_JUNIOR_CODE:
                $update['education_junior'] = 1;
                break;
        }
        //进行职位数据分析
        BaseJobApplyRecordExtra::updateAllCounters($update, [
            'job_id' => $jobId,
        ]);

        return true;
    }

    /**
     * 邀面总数
     * @param $jobId
     */
    public static function jobApplyUpdateInterview($jobId)
    {
        BaseJobApplyRecordExtra::updateAllCounters([
            'interview' => 1,
        ], [
            'job_id' => $jobId,
        ]);

        return true;
    }

    /**
     * 获取公告投递相关的统计数量
     * @param $announcementIds
     * @param $field
     * @param $asFieldName
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getAnnouncementAmount($announcementIds, $field)
    {
        $data = self::find()
            ->select([
                'announcement_id',
                'SUM(' . $field . ') AS amount',
            ])
            ->where([
                'in',
                'announcement_id',
                $announcementIds,
            ])
            ->groupBy('announcement_id')
            ->asArray()
            ->all();
        if ($data) {
            $data = array_column($data, 'amount', 'announcement_id');
        }
        return $data;
    }
}