<?php

namespace common\base\models;

use admin\models\Announcement;
use common\helpers\IpHelper;
use common\models\AnnouncementHandleLog;
use yii\base\Exception;

class BaseAnnouncementHandleLog extends AnnouncementHandleLog
{
    // 公告操作类型
    const HANDLE_TYPE_EDIT          = 1;  //编辑
    const HANDLE_TYPE_REFRESH       = 2;  //刷新
    const HANDLE_TYPE_RELEASE       = 3;  //发布
    const HANDLE_TYPE_RELEASE_AGAIN = 4;  ///再发布
    const HANDLE_TYPE_OFFLINE       = 5;  //下线
    const HANDLE_TYPE_AUDIT         = 6;  //审核
    const HANDLE_TYPE_HIDDEN        = 7;  //隐藏
    const HANDLE_TYPE_SHOW          = 8;  //显示
    const HANDLE_TYPE_DELETE        = 9;  //删除
    const HANDLE_TYPE_COPY          = 10; //复制
    const HANDLE_TYPE_CHANGE_ATTR   = 11; //修改属性

    const HANDLER_TYPE_PLAT = 1;
    const HANDLER_TYPE_USER = 2;

    // 公告操作类型
    const HANDLE_TYPE_NAME = [
        self::HANDLE_TYPE_EDIT          => '编辑',
        self::HANDLE_TYPE_REFRESH       => '刷新',
        self::HANDLE_TYPE_RELEASE       => '发布',
        self::HANDLE_TYPE_RELEASE_AGAIN => '再发布',
        self::HANDLE_TYPE_OFFLINE       => '下线',
        self::HANDLE_TYPE_AUDIT         => '审核',
        self::HANDLE_TYPE_HIDDEN        => '隐藏',
        self::HANDLE_TYPE_SHOW          => '显示',
        self::HANDLE_TYPE_DELETE        => '删除',
        self::HANDLE_TYPE_COPY          => '复制',
        self::HANDLE_TYPE_CHANGE_ATTR   => '修改属性',
    ];

    /**
     * 创建公告操作日志
     * @throws Exception
     */
    public static function createInfo($data)
    {
        $handleLog                  = new self();
        $handleLog->add_time        = $data['add_time'] ?: date('Y-m-d H:i:s');
        $handleLog->announcement_id = $data['announcement_id'];
        $handleLog->handle_type     = strval($data['handle_type']);
        $handleLog->editor_type     = $data['editor_type'];
        $handleLog->handler_type    = $data['handler_type'];
        $handleLog->handler_id      = $data['handler_id'];
        $handleLog->handler_name    = $data['handler_name'];
        $handleLog->handle_before   = $data['handle_before'];
        $handleLog->handle_after    = $data['handle_after'];
        $handleLog->ip              = $data['ip'] ?: IpHelper::getIpInt();
        if (!$handleLog->save()) {
            throw new Exception($handleLog->getFirstErrorsMessage());
        }
    }

    /**
     * 公告审核处理历史
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function announcementHandleLog($params): array
    {
        $query     = self::find()
            ->where([
                'announcement_id' => $params['id'],
                'handle_type'     => self::HANDLE_TYPE_AUDIT,
            ])
            ->select([
                'add_time',
                'id',
                'handler_name',
                'handle_after',
            ]);
        $count     = $query->count();
        $pageSize  = $params['pageSize'] ?: \Yii::$app->params['defaultPageSize'];
        $pages     = self::setPage($count, $params['page'], $pageSize);
        $handleLog = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('add_time desc')
            ->asArray()
            ->all();

        $auditStatusList = Announcement::STATUS_AUDIT_LIST;
        foreach ($handleLog as $k => $log) {
            $handleLog[$k]['auditStatus']      = 99;
            $handleLog[$k]['auditStatusTitle'] = "";
            $handleLog[$k]['opinion']          = "";
            $handleAfter                       = json_decode($log['handle_after'], true);
            $handleLog[$k]['handle_after']     = $handleAfter;
            if ($handleAfter['audit_status']) {
                $handleLog[$k]['auditStatus']      = intval($handleAfter['audit_status']);
                $handleLog[$k]['auditStatusTitle'] = $auditStatusList[$handleAfter['audit_status']];
            }
            if ($handleAfter['opinion']) {
                $handleLog[$k]['opinion'] = $handleAfter['opinion'];
            }
        }

        return [
            'list' => $handleLog,
            'page' => [
                'total' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$params['page'],
            ],
        ];
    }
}