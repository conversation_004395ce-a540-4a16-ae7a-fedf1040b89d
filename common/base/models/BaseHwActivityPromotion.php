<?php

namespace common\base\models;

use common\models\HwActivityPromotion;
use yii\base\Exception;

class BaseHwActivityPromotion extends HwActivityPromotion
{
    const PROMOTION_POSITION_INDEX_RECOMMEND          = 1;//首页-推荐活动
    const PROMOTION_POSITION_OVERSEAS_RECRUITMENT     = 2;//首页-出海引才
    const PROMOTION_POSITION_COME_HOME_PART_TOP       = 3;//首页-归国活动-上
    const PROMOTION_POSITION_COME_HOME_PART_BOTTOM    = 4;//首页-归国活动-下
    const PROMOTION_POSITION_BOHOU_ACTIVITY           = 5;//博士后首页-博后活动
    const PROMOTION_POSITION_BOHOU_RECOMMEND_ACTIVITY = 6;//博士后博后活动页-推荐活动
    const PROMOTION_POSITION_MINI_ACTIVITY_PAGE_HOT   = 7;//小程序活动汇总页-热门场次
    const PROMOTION_POSITION_PC_ACTIVITY_PAGE_HOT     = 8;//PC活动汇总页-热门场次

    const PROMOTION_POSITION_TEXT_LIST = [
        self::PROMOTION_POSITION_INDEX_RECOMMEND          => '首页-推荐活动',
        self::PROMOTION_POSITION_OVERSEAS_RECRUITMENT     => '首页-出海引才',
        self::PROMOTION_POSITION_COME_HOME_PART_TOP       => '首页-归国活动-上',
        self::PROMOTION_POSITION_COME_HOME_PART_BOTTOM    => '首页-归国活动-下',
        self::PROMOTION_POSITION_BOHOU_ACTIVITY           => '博士后首页-博后活动',
        self::PROMOTION_POSITION_BOHOU_RECOMMEND_ACTIVITY => '博士后博后活动页-推荐活动',
        self::PROMOTION_POSITION_MINI_ACTIVITY_PAGE_HOT   => '小程序活动汇总页-热门场次',
        self::PROMOTION_POSITION_PC_ACTIVITY_PAGE_HOT     => 'PC活动汇总页-热门场次',
    ];

    const PROMOTION_POSITION_LIST = [
        BaseHwActivity::SERIES_COME_HOME            => self::PROMOTION_POSITION_TEXT_LIST,
        BaseHwActivity::SERIES_OVERSEAS_RECRUITMENT => self::PROMOTION_POSITION_TEXT_LIST,
        BaseHwActivity::SERIES_MORE_ACTIVITY        => self::PROMOTION_POSITION_TEXT_LIST,
        BaseHwActivity::SERIES_BOSHIHOU             => self::PROMOTION_POSITION_TEXT_LIST,
        BaseHwActivity::SERIES_ZHAOPINHUI_OFFLINE   => [
            self::PROMOTION_POSITION_INDEX_RECOMMEND        => '首页-推荐活动',
            self::PROMOTION_POSITION_OVERSEAS_RECRUITMENT   => '首页-出海引才',
            self::PROMOTION_POSITION_COME_HOME_PART_TOP     => '首页-归国活动-上',
            self::PROMOTION_POSITION_COME_HOME_PART_BOTTOM  => '首页-归国活动-下',
            self::PROMOTION_POSITION_MINI_ACTIVITY_PAGE_HOT => '小程序活动汇总页-热门场次',
            self::PROMOTION_POSITION_PC_ACTIVITY_PAGE_HOT   => 'PC活动汇总页-热门场次',
        ],
        BaseHwActivity::SERIES_ZHAOPINHUI_ONLINE    => [
            self::PROMOTION_POSITION_INDEX_RECOMMEND        => '首页-推荐活动',
            self::PROMOTION_POSITION_OVERSEAS_RECRUITMENT   => '首页-出海引才',
            self::PROMOTION_POSITION_COME_HOME_PART_TOP     => '首页-归国活动-上',
            self::PROMOTION_POSITION_COME_HOME_PART_BOTTOM  => '首页-归国活动-下',
            self::PROMOTION_POSITION_MINI_ACTIVITY_PAGE_HOT => '小程序活动汇总页-热门场次',
            self::PROMOTION_POSITION_PC_ACTIVITY_PAGE_HOT   => 'PC活动汇总页-热门场次',
        ],
    ];

    //显示图片
    const SHOW_IMG_TYPE_MAIN        = 1;
    const SHOW_IMG_TYPE_OTHER_ONE   = 2;
    const SHOW_IMG_TYPE_OTHER_TWO   = 3;
    const SHOW_IMG_TYPE_OTHER_THREE = 4;
    const SHOW_IMG_TYPE_MINI_MASTER = 5;
    const SHOW_IMG_TYPE_PC_BANNER   = 6;
    const SHOW_IMG_TYPE_MINI_BANNER = 7;

    //    const SHOW_IMG_TYPE_TEXT_LIST = [
    //        self::SHOW_IMG_TYPE_MAIN        => '活动主图',
    //        self::SHOW_IMG_TYPE_OTHER_ONE   => '其他图片1',
    //        self::SHOW_IMG_TYPE_OTHER_TWO   => '其他图片2',
    //        self::SHOW_IMG_TYPE_OTHER_THREE => '其他图片3',
    //    ];

    const SHOW_IMG_TYPE_TEXT_LIST = [
        BaseHwActivity::SERIES_COME_HOME            => [
            self::SHOW_IMG_TYPE_MAIN        => '活动主图',
            self::SHOW_IMG_TYPE_OTHER_ONE   => '其他图片1',
            self::SHOW_IMG_TYPE_OTHER_TWO   => '其他图片2',
            self::SHOW_IMG_TYPE_OTHER_THREE => '其他图片3',
        ],
        BaseHwActivity::SERIES_OVERSEAS_RECRUITMENT => [
            self::SHOW_IMG_TYPE_MAIN        => '活动主图',
            self::SHOW_IMG_TYPE_OTHER_ONE   => '其他图片1',
            self::SHOW_IMG_TYPE_OTHER_TWO   => '其他图片2',
            self::SHOW_IMG_TYPE_OTHER_THREE => '其他图片3',
        ],
        BaseHwActivity::SERIES_MORE_ACTIVITY        => [
            self::SHOW_IMG_TYPE_MAIN        => '活动主图',
            self::SHOW_IMG_TYPE_OTHER_ONE   => '其他图片1',
            self::SHOW_IMG_TYPE_OTHER_TWO   => '其他图片2',
            self::SHOW_IMG_TYPE_OTHER_THREE => '其他图片3',
        ],
        BaseHwActivity::SERIES_BOSHIHOU             => [
            self::SHOW_IMG_TYPE_MAIN        => '活动主图',
            self::SHOW_IMG_TYPE_OTHER_ONE   => '其他图片1',
            self::SHOW_IMG_TYPE_OTHER_TWO   => '其他图片2',
            self::SHOW_IMG_TYPE_OTHER_THREE => '其他图片3',
        ],
        BaseHwActivity::SERIES_ZHAOPINHUI_OFFLINE   => [
            self::SHOW_IMG_TYPE_MAIN        => '活动主图',
            self::SHOW_IMG_TYPE_MINI_MASTER => '小程序主图',
            self::SHOW_IMG_TYPE_PC_BANNER   => 'PC-banner图',
            self::SHOW_IMG_TYPE_MINI_BANNER => 'MINI-banner图',
            self::SHOW_IMG_TYPE_OTHER_ONE   => '其他图片1',
            self::SHOW_IMG_TYPE_OTHER_TWO   => '其他图片2',
            self::SHOW_IMG_TYPE_OTHER_THREE => '其他图片3',
        ],
        BaseHwActivity::SERIES_ZHAOPINHUI_ONLINE    => [
            self::SHOW_IMG_TYPE_MAIN        => '活动主图',
            self::SHOW_IMG_TYPE_MINI_MASTER => '小程序主图',
            self::SHOW_IMG_TYPE_PC_BANNER   => 'PC-banner图',
            self::SHOW_IMG_TYPE_MINI_BANNER => 'MINI-banner图',
            self::SHOW_IMG_TYPE_OTHER_ONE   => '其他图片1',
            self::SHOW_IMG_TYPE_OTHER_TWO   => '其他图片2',
            self::SHOW_IMG_TYPE_OTHER_THREE => '其他图片3',
        ],
    ];

    //显示图片类型，对应字段
    //    const SHOW_IMG_FILED_RELATE_LIST = [
    //        self::SHOW_IMG_TYPE_MAIN        => 'main_img_file_id',
    //        self::SHOW_IMG_TYPE_OTHER_ONE   => 'other_img_one_file_id',
    //        self::SHOW_IMG_TYPE_OTHER_TWO   => 'other_img_two_file_id',
    //        self::SHOW_IMG_TYPE_OTHER_THREE => 'other_img_three_file_id',
    //    ];

    const SHOW_IMG_FILED_RELATE_LIST = [
        BaseHwActivity::SERIES_COME_HOME            => [
            self::SHOW_IMG_TYPE_MAIN        => 'main_img_file_id',
            self::SHOW_IMG_TYPE_OTHER_ONE   => 'other_img_one_file_id',
            self::SHOW_IMG_TYPE_OTHER_TWO   => 'other_img_two_file_id',
            self::SHOW_IMG_TYPE_OTHER_THREE => 'other_img_three_file_id',
        ],
        BaseHwActivity::SERIES_OVERSEAS_RECRUITMENT => [
            self::SHOW_IMG_TYPE_MAIN        => 'main_img_file_id',
            self::SHOW_IMG_TYPE_OTHER_ONE   => 'other_img_one_file_id',
            self::SHOW_IMG_TYPE_OTHER_TWO   => 'other_img_two_file_id',
            self::SHOW_IMG_TYPE_OTHER_THREE => 'other_img_three_file_id',
        ],
        BaseHwActivity::SERIES_MORE_ACTIVITY        => [
            self::SHOW_IMG_TYPE_MAIN        => 'main_img_file_id',
            self::SHOW_IMG_TYPE_OTHER_ONE   => 'other_img_one_file_id',
            self::SHOW_IMG_TYPE_OTHER_TWO   => 'other_img_two_file_id',
            self::SHOW_IMG_TYPE_OTHER_THREE => 'other_img_three_file_id',
        ],
        BaseHwActivity::SERIES_BOSHIHOU             => [
            self::SHOW_IMG_TYPE_MAIN        => 'main_img_file_id',
            self::SHOW_IMG_TYPE_OTHER_ONE   => 'other_img_one_file_id',
            self::SHOW_IMG_TYPE_OTHER_TWO   => 'other_img_two_file_id',
            self::SHOW_IMG_TYPE_OTHER_THREE => 'other_img_three_file_id',
        ],
        BaseHwActivity::SERIES_ZHAOPINHUI_OFFLINE   => [
            self::SHOW_IMG_TYPE_MAIN        => 'main_img_file_id',
            self::SHOW_IMG_TYPE_MINI_MASTER => 'image_mini_master_id',
            self::SHOW_IMG_TYPE_PC_BANNER   => 'image_pc_banner_id',
            self::SHOW_IMG_TYPE_MINI_BANNER => 'image_mini_banner_id',
            self::SHOW_IMG_TYPE_OTHER_ONE   => 'other_img_one_file_id',
            self::SHOW_IMG_TYPE_OTHER_TWO   => 'other_img_two_file_id',
            self::SHOW_IMG_TYPE_OTHER_THREE => 'other_img_three_file_id',
        ],
        BaseHwActivity::SERIES_ZHAOPINHUI_ONLINE    => [
            self::SHOW_IMG_TYPE_MAIN        => 'main_img_file_id',
            self::SHOW_IMG_TYPE_MINI_MASTER => 'image_mini_master_id',
            self::SHOW_IMG_TYPE_PC_BANNER   => 'image_pc_banner_id',
            self::SHOW_IMG_TYPE_MINI_BANNER => 'image_mini_banner_id',
            self::SHOW_IMG_TYPE_OTHER_ONE   => 'other_img_one_file_id',
            self::SHOW_IMG_TYPE_OTHER_TWO   => 'other_img_two_file_id',
            self::SHOW_IMG_TYPE_OTHER_THREE => 'other_img_three_file_id',
        ],
    ];

    const STATUS_ONLINE  = 1;
    const STATUS_OFFLINE = 2;

    /**
     * 获取推广位置文案列表
     * @param $activityId
     * @return array
     */
    public static function getPromotionTypeTextList($activityId)
    {
        $list = self::find()
            ->where([
                'activity_id' => $activityId,
            ])
            ->select([
                'id',
                'position_type as positionType',
                'sort',
            ])
            ->asArray()
            ->all();
        if (!$list) {
            return [];
        }

        foreach ($list as &$item) {
            $item['positionTypeName'] = self::PROMOTION_POSITION_TEXT_LIST[$item['positionType']];
        }

        return $list;
    }

    public static function getAllPromotionList($activityId)
    {
        $list = self::find()
            ->select([
                'id',
                'position_type',
                'sort',
                'start_date',
                'end_date',
                'img_type',
            ])
            ->where([
                'activity_id' => $activityId,
            ])
            ->orderBy('position_type asc')
            ->asArray()
            ->all();

        return $list;
    }

    /**
     * 设置排序
     * @param $id
     * @param $sort
     * @return bool
     * @throws \yii\db\Exception
     */
    public static function setSort($id, $sort)
    {
        $model       = self::findOne($id);
        $model->sort = $sort;
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }

        return true;
    }
}