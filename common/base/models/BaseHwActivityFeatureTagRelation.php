<?php
/**
 * create user：shannon
 * create time：2025/1/20 下午2:03
 */
namespace common\base\models;

use common\models\HwActivityFeatureTagRelation;

class BaseHwActivityFeatureTagRelation extends HwActivityFeatureTagRelation
{
    //    1=博士专场
    //    2=出站博士后专场
    //    3=硕博综合场
    //    4=海外优青
    //    5=校园场
    //    6=城市综合场
    //    7=海外名校行
    //    8=政府引才专场
    const FEATURE_TAG_DOCTOR_SPECIAL_ACTIVITY     = 1;
    const FEATURE_TAG_OUT_DOCTOR_SPECIAL_ACTIVITY = 2;
    const FEATURE_TAG_COLLEGE_COMBINE_ACTIVITY    = 3;
    const FEATURE_TAG_OVERSEAS_TALENT             = 4;
    const FEATURE_TAG_SCHOOL_ACTIVITY             = 5;
    const FEATURE_TAG_CITY_COMBINE_ACTIVITY       = 6;
    const FEATURE_TAG_ABROAD_COLLEGE_ACTIVITY     = 7;
    const FEATURE_TAG_GOVERNMENT_RECRUIT_ACTIVITY = 8;

    const FEATURE_TAG_LIST      = [
        self::FEATURE_TAG_DOCTOR_SPECIAL_ACTIVITY,
        self::FEATURE_TAG_OUT_DOCTOR_SPECIAL_ACTIVITY,
        self::FEATURE_TAG_COLLEGE_COMBINE_ACTIVITY,
        self::FEATURE_TAG_OVERSEAS_TALENT,
        self::FEATURE_TAG_SCHOOL_ACTIVITY,
        self::FEATURE_TAG_CITY_COMBINE_ACTIVITY,
        self::FEATURE_TAG_ABROAD_COLLEGE_ACTIVITY,
        self::FEATURE_TAG_GOVERNMENT_RECRUIT_ACTIVITY,
    ];
    const FEATURE_TAG_TEXT_LIST = [
        self::FEATURE_TAG_DOCTOR_SPECIAL_ACTIVITY     => '博士专场',
        self::FEATURE_TAG_OUT_DOCTOR_SPECIAL_ACTIVITY => '出站博士后专场',
        self::FEATURE_TAG_COLLEGE_COMBINE_ACTIVITY    => '硕博综合场',
        self::FEATURE_TAG_OVERSEAS_TALENT             => '海外优青',
        self::FEATURE_TAG_SCHOOL_ACTIVITY             => '校园场',
        self::FEATURE_TAG_CITY_COMBINE_ACTIVITY       => '城市综合场',
        self::FEATURE_TAG_ABROAD_COLLEGE_ACTIVITY     => '海外名校行',
        self::FEATURE_TAG_GOVERNMENT_RECRUIT_ACTIVITY => '政府引才专场',
    ];

    /**
     * 根据活动ID获取特色标签
     */
    public static function getFeatureTag($ActivityId)
    {
        return HwActivityFeatureTagRelation::find()
            ->select('feature_tag_id')
            ->where(['activity_id' => $ActivityId])
            ->column();
    }

    /**
     * 根据活动ID获取特色标签名称
     */
    public static function getFeatureTagName($ActivityId)
    {
        $featureTagIds = HwActivityFeatureTagRelation::find()
            ->select('feature_tag_id')
            ->where(['activity_id' => $ActivityId])
            ->column();
        //转成名称
        $featureTagNames = [];
        foreach ($featureTagIds as $featureTagId) {
            $featureTagNames[] = self::FEATURE_TAG_TEXT_LIST[$featureTagId];
        }

        return $featureTagNames;
    }
}