<?php
/**
 * create user：shannon
 * create time：2025/5/14 下午3:57
 */
namespace common\base\models;

use common\models\JobLog;
use Yii;

class BaseJobLog extends JobLog
{
    //类型1=运营；2=单位
    const CREATE_TYPE_ADMIN   = 1;
    const CREATE_TYPE_COMPANY = 2;

    //操作类型(4位数00前面两位代表大操作类型00后面两位代表小操作类型；例如：刷新1001；批量刷新1002)：1001=添加；1002=批量添加；1101=编辑
    const TYPE_ADD                    = 1001;//新增职位
    const TYPE_ADD_BATCH              = 1002;//批量导入新增职位
    const TYPE_EDIT                   = 1101;//编辑职位
    const TYPE_AUDIT_AGREE_FIRST      = 1201;//初次审核通过
    const TYPE_AUDIT_AGREE            = 1202;//审核通过
    const TYPE_AUDIT_REFUSE           = 1203;//审核拒绝
    const TYPE_REFRESH                = 1301;//刷新操作
    const TYPE_REFRESH_BATCH          = 1302;//批量刷新操作
    const TYPE_REPUBLISH              = 1401;//再发布操作
    const TYPE_REPUBLISH_BATCH        = 1402;//批量再发布操作
    const TYPE_OFFLINE                = 1501;//下线操作
    const TYPE_OFFLINE_BATCH          = 1502;//批量下线操作
    const TYPE_DELETE                 = 1601;//删除
    const TYPE_DELETE_BATCH           = 1602;//批量删除
    const TYPE_CONTACT                = 1701;//联系人及协同联系人
    const TYPE_CONTACT_BATCH          = 1702;//批量联系人及协同联系人变更
    const TYPE_ESTABLISHMENT          = 1801;//修改编制
    const TYPE_ESTABLISHMENT_BATCH    = 1802;//批量修改编制
    const TYPE_LIMIT_EDUCATION        = 1901;//设置学历限制
    const TYPE_LIMIT_EDUCATION_BATCH  = 1902;//批量设置学历限制
    const TYPE_LIMIT_ATTACHMENT       = 2001;//设置附件限制
    const TYPE_LIMIT_ATTACHMENT_BATCH = 2002;//批量设置附件限制
    const TYPE_LIMIT_IS_SHOW          = 2101;//设置显示/隐藏
    const TYPE_LIMIT_IS_SHOW_BATCH    = 2102;//批量设置显示/隐藏
    const TYPE_WELFARE                = 2201;//修改福利
    const TYPE_WELFARE_BATCH          = 2202;//批量修改福利

    const TYPE_ADD_NAME                    = '新增职位';
    const TYPE_ADD_BATCH_NAME              = '批量导入新增职位';
    const TYPE_EDIT_NAME                   = '编辑职位';
    const TYPE_AUDIT_AGREE_FIRST_NAME      = '初次审核通过';
    const TYPE_AUDIT_AGREE_NAME            = '审核通过';
    const TYPE_AUDIT_REFUSE_NAME           = '审核拒绝';
    const TYPE_REFRESH_NAME                = '刷新操作';
    const TYPE_REFRESH_BATCH_NAME          = '批量刷新操作';
    const TYPE_REPUBLISH_NAME              = '再发布操作';
    const TYPE_REPUBLISH_BATCH_NAME        = '批量再发布操作';
    const TYPE_OFFLINE_NAME                = '下线操作';
    const TYPE_OFFLINE_BATCH_NAME          = '批量下线操作';
    const TYPE_DELETE_NAME                 = '删除操作';
    const TYPE_DELETE_BATCH_NAME           = '批量删除操作';
    const TYPE_LIMIT_EDUCATION_NAME        = '设置学历限制';
    const TYPE_LIMIT_EDUCATION_BATCH_NAME  = '批量设置学历限制';
    const TYPE_LIMIT_ATTACHMENT_NAME       = '设置附件限制';
    const TYPE_LIMIT_ATTACHMENT_BATCH_NAME = '批量设置附件限制';
    const TYPE_CONTACT_NAME                = '联系人及协同联系人';
    const TYPE_CONTACT_BATCH_NAME          = '批量联系人及协同联系人变更';
    const TYPE_ESTABLISHMENT_NAME          = '修改编制';
    const TYPE_ESTABLISHMENT_BATCH_NAME    = '批量修改编制';
    const TYPE_LIMIT_IS_SHOW_NAME          = '设置显示/隐藏';
    const TYPE_LIMIT_IS_SHOW_BATCH_NAME    = '批量设置显示/隐藏';
    const TYPE_WELFARE_NAME                = '修改福利';
    const TYPE_WELFARE_BATCH_NAME          = '批量修改福利';

    const TYPE_NAME_LIST = [
        self::TYPE_ADD                    => self::TYPE_ADD_NAME,
        self::TYPE_ADD_BATCH              => self::TYPE_ADD_BATCH_NAME,
        self::TYPE_EDIT                   => self::TYPE_EDIT_NAME,
        self::TYPE_AUDIT_AGREE_FIRST      => self::TYPE_AUDIT_AGREE_FIRST_NAME,
        self::TYPE_AUDIT_AGREE            => self::TYPE_AUDIT_AGREE_NAME,
        self::TYPE_AUDIT_REFUSE           => self::TYPE_AUDIT_REFUSE_NAME,
        self::TYPE_REFRESH                => self::TYPE_REFRESH_NAME,
        self::TYPE_REFRESH_BATCH          => self::TYPE_REFRESH_BATCH_NAME,
        self::TYPE_REPUBLISH              => self::TYPE_REPUBLISH_NAME,
        self::TYPE_REPUBLISH_BATCH        => self::TYPE_REPUBLISH_BATCH_NAME,
        self::TYPE_OFFLINE                => self::TYPE_OFFLINE_NAME,
        self::TYPE_OFFLINE_BATCH          => self::TYPE_OFFLINE_BATCH_NAME,
        self::TYPE_DELETE                 => self::TYPE_DELETE_NAME,
        self::TYPE_DELETE_BATCH           => self::TYPE_DELETE_BATCH_NAME,
        self::TYPE_LIMIT_EDUCATION        => self::TYPE_LIMIT_EDUCATION_NAME,
        self::TYPE_LIMIT_EDUCATION_BATCH  => self::TYPE_LIMIT_EDUCATION_BATCH_NAME,
        self::TYPE_LIMIT_ATTACHMENT       => self::TYPE_LIMIT_ATTACHMENT_NAME,
        self::TYPE_LIMIT_ATTACHMENT_BATCH => self::TYPE_LIMIT_ATTACHMENT_BATCH_NAME,
        self::TYPE_CONTACT                => self::TYPE_CONTACT_NAME,
        self::TYPE_CONTACT_BATCH          => self::TYPE_CONTACT_BATCH_NAME,
        self::TYPE_ESTABLISHMENT          => self::TYPE_ESTABLISHMENT_NAME,
        self::TYPE_ESTABLISHMENT_BATCH    => self::TYPE_ESTABLISHMENT_BATCH_NAME,
        self::TYPE_LIMIT_IS_SHOW          => self::TYPE_LIMIT_IS_SHOW_NAME,
        self::TYPE_LIMIT_IS_SHOW_BATCH    => self::TYPE_LIMIT_IS_SHOW_BATCH_NAME,
        self::TYPE_WELFARE                => self::TYPE_WELFARE_NAME,
        self::TYPE_WELFARE_BATCH          => self::TYPE_WELFARE_BATCH_NAME,
    ];

    /**
     * 添加职位日志
     * @return true
     */
    public static function addLog($params)
    {
        $model                  = new self();
        $model->add_time        = CUR_DATETIME;
        $model->update_time     = CUR_DATETIME;
        $model->create_type     = $params['createType'];
        $model->create_id       = $params['createId'];
        $model->create_name     = $params['createName'];
        $model->type            = $params['type'];
        $model->remark          = self::TYPE_NAME_LIST[$params['type']];
        $model->route           = Yii::$app->controller->action->uniqueId;
        $model->job_id          = $params['jobId'];
        $model->announcement_id = $params['announcementId'];
        if (!$model->save()) {
            throw new \Exception($model->getFirstErrorsMessage());
        }

        return true;
    }

}