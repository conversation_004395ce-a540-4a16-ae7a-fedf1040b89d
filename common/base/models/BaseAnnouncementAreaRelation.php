<?php
/**
 * create user：shannon
 * create time：2024/6/27 16:01
 */
namespace common\base\models;

use common\models\AnnouncementAreaRelation;

class BaseAnnouncementAreaRelation extends AnnouncementAreaRelation
{
    /**
     * 获取公告的城市文本
     * @param $announcementId
     * @return string
     */
    public static function getCityTextByAnnouncementId($announcementId)
    {
        $areaIdList = self::find()
            ->where([
                'announcement_id' => $announcementId,
                'level'           => BaseArea::CITY_LEVEL,
            ])
            ->select(['area_id'])
            ->column();
        if (!$areaIdList) {
            return '';
        }
        $areaNameList = [];
        foreach ($areaIdList as $areaId) {
            $areaNameList[] = BaseArea::getAreaName($areaId);
        }

        return implode(',', $areaNameList);
    }

    /**
     * 获取公告的省文本
     * @param $announcementId
     * @return string
     */
    public static function getProvinceTextByAnnouncementId($announcementId)
    {
        $areaIdList = self::find()
            ->where([
                'announcement_id' => $announcementId,
                'level'           => BaseArea::PROVINCE_LEVEL,
            ])
            ->select(['area_id'])
            ->column();
        if (!$areaIdList) {
            return '';
        }
        $areaNameList = [];
        foreach ($areaIdList as $areaId) {
            $areaNameList[] = BaseArea::getAreaName($areaId);
        }

        return implode(',', $areaNameList);
    }
}