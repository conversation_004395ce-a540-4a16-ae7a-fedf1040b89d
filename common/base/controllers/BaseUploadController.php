<?php

namespace common\base\controllers;

use common\base\models\BaseUploadForm;
use common\helpers\FileHelper;
use common\libs\Qiniu;
use common\libs\Word;
use Yii;
use yii\base\Exception;
use yii\web\Response;

trait BaseUploadController
{
    /**
     * 上传图片
     */
    public function actionEditorImage()
    {
        // 一张张来
        $return      = [];
        $transaction = Yii::$app->db->beginTransaction();
        try {
            foreach ($_FILES as $k => $item) {
                $model = new BaseUploadForm();
                $model->setUploadType('image');
                $data     = $model->upload($k);
                $return[] = [
                    'url' => $data['fullUrl'],
                ];
            }

            $rs['errno'] = '0';
            $rs['data']  = $return;

            $response         = Yii::$app->response;
            $response->format = Response::FORMAT_JSON;
            $response->data   = $rs;

            return $response;
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 上传图片new
     */
    public function actionEditorImageNew()
    {
        // 一张张来
        $return      = [];
        $transaction = Yii::$app->db->beginTransaction();
        try {
            foreach ($_FILES as $k => $item) {
                $model = new BaseUploadForm();
                $model->setUploadType('image');
                $data   = $model->upload($k);
                $return = [
                    'url' => $data['fullUrl'],
                ];
            }

            $rs['errno'] = 0;
            $rs['data']  = $return;

            $response         = Yii::$app->response;
            $response->format = Response::FORMAT_JSON;
            $response->data   = $rs;

            return $response;
        } catch (\Exception $e) {
            $transaction->rollBack();
            $error            = [
                'errno'   => 1,
                'message' => $e->getMessage(),
            ];
            $response         = Yii::$app->response;
            $response->format = Response::FORMAT_JSON;
            $response->data   = $error;

            return $response;
        }
    }

    /**
     * 上传图片
     */
    public function actionImage()
    {
        $model = new BaseUploadForm();
        $model->setUploadType('image');

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = $model->upload();

            $transaction->commit();

            return $this->success([
                'id'      => (string)$data['id'],
                'url'     => $data['path'],
                'name'    => $data['name'],
                'fullUrl' => $data['fullUrl'],
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 上传文件
     */
    public function actionFile()
    {
        $model = new BaseUploadForm();
        $model->setUploadType('file');

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $transaction->commit();
            $data = $model->upload();

            return $this->success([
                'id'      => (string)$data['id'],
                'url'     => $data['path'],
                'fullUrl' => $data['fullUrl'],
                'name'    => $data['name'],
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 给应聘材料用的
     * @return \yii\console\Response|Response
     */
    public function actionResumeAttachment()
    {
        $model    = new BaseUploadForm();
        $memberId = Yii::$app->user->id;
        if (!$memberId) {
            return $this->error('上传失败,没有权限');
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = $model->resumeAttachment($memberId);

            $transaction->commit();

            return $this->success([
                'id'   => (string)$data['id'],
                'name' => $data['name'],
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 给活动用的
     * @return \yii\console\Response|Response
     */
    public function actionActivity()
    {
        $model    = new BaseUploadForm();
        $memberId = Yii::$app->user->id;
        if (!$memberId) {
            return $this->error('上传失败,没有权限');
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $model->setUploadType('activity');
            $data = $model->activity($memberId);

            $transaction->commit();

            return $this->success([
                'id'   => (string)$data['id'],
                'name' => $data['name'],
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->error($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public function actionBase64()
    {
        $model = new BaseUploadForm();

        $base64   = Yii::$app->request->post('base64');
        $keyWords = Yii::$app->request->post('keyWords');
        if ($data = $model->saveBase64($base64, $keyWords)) {
            return $this->success([
                'id'      => (string)$data['id'],
                'url'     => $data['path'],
                'fullUrl' => FileHelper::getFullUrl($data['path']),
            ]);
        } else {
            return $this->result($model->getFirstErrorsMessage());
        }
    }

    /**
     * 上传word,读取内容到富文本
     */
    public function actionRecognizeWord()
    {
        $model = new BaseUploadForm();
        $data  = $model->upload();

        try {
            $rs = (new Word())->recognizeToHtml($data['realPath']);

            return $this->success(['html' => $rs]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取token等信息
     */
    public function actionLoadQiniuInfo()
    {
        $qiniu = new Qiniu('img');

        return $this->success([
            'token'     => $qiniu->getToken(),
            'showUrl'   => $qiniu->getShowUrl(),
            'uploadUrl' => $qiniu->getuploadUrl(),
        ]);
    }

    /**
     * 上传单位相关图片
     */
    public function actionCompanyRelevantImage()
    {
        $params = Yii::$app->request->post();
        $model  = new BaseUploadForm();

        $transaction = Yii::$app->db->beginTransaction();
        try {
            // 判断是单位什么类型的图
            switch ($params['uploadType']) {
                case 'logo':
                    $path = 'company_logo';
                    break;
                case 'license':
                    $path = 'company_license';
                    break;
                case 'handler':
                    $path = 'company_person_info';
                    break;
                case 'banner':
                    $path = 'company_banner';
                    break;
                default:
                    throw new Exception('上传非法的单位类型图片');
            }

            $transaction->commit();
            $data = $model->upload('file', $path);

            return $this->success([
                'id'      => (string)$data['id'],
                'url'     => $data['path'],
                'fullUrl' => FileHelper::getFullUrl($data['path']),
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 上传excel导入模版文件
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionTemplateFile()
    {
        $fileName = Yii::$app->request->post('fileName');
        $model    = new BaseUploadForm();

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $transaction->commit();
            $data = $model->temporaryUploadExcel('file', 'template', $fileName);

            return $this->success([
                'url' => \Yii::$app->params['homeUrl'] . '/' . $data['path'],
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 上传图片（单位风采）
     * 返回图片路径跟id
     */
    public function actionSendImage()
    {
        $companyId = Yii::$app->request->post('companyId');
        $path      = "company_style_atlas/" . $companyId;
        $model     = new BaseUploadForm();
        $model->setUploadType('image');

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = $model->upload('file', $path);

            $transaction->commit();

            return $this->success([
                'id'      => (string)$data['id'],
                'url'     => $data['path'],
                'fullUrl' => $data['fullUrl'],
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 上传职位附件文件
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionJobAppendix()
    {
        $model = new BaseUploadForm();
        $model->setUploadType('jobAttachment');
        $model->setSize(20 * 1024 * 1024);
        $transaction = \Yii::$app->db->beginTransaction();

        try {
            $data = $model->upload('file', 'job_appendix');
            $transaction->commit();

            return $this->success([
                'id'     => (string)$data['id'],
                'name'   => $data['name'],
                'suffix' => $data['suffix'],
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }
}