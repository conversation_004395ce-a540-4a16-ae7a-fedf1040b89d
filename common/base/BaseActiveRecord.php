<?php

namespace common\base;

use common\base\models\BaseJob;
use common\base\models\BaseSeoJobWiki;
use common\helpers\DebugHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use yii\base\Exception;
use yii\db\ActiveRecord;
use Yii;
use yii\db\conditions\AndCondition;
use yii\db\conditions\OrCondition;

class BaseActiveRecord extends ActiveRecord
{

    private $successMsg = '';

    const STATUS_DELETE = 2;
    const STATUS_ACTIVE = 1;

    const ORDER_BY_DESC = 1;
    const ORDER_BY_ASC  = 2;

    const SOURCE_TYPE_PC       = 1;
    const SOURCE_TYPE_H5       = 2;
    const SOURCE_TYPE_MINI_APP = 3;

    const SOURCE_TYPE_LIST = [
        self::SOURCE_TYPE_PC       => 'PC',
        self::SOURCE_TYPE_H5       => 'H5',
        self::SOURCE_TYPE_MINI_APP => '小程序',
    ];

    const BASE_TYPE_YES = 1;
    const BASE_TYPE_NO  = 2;

    /**
     * --------------------------------------------------
     * 公共的数据填充，在save之前做的事情，假如有其他的填充，建议继承
     * --------------------------------------------------
     *
     */
    public function beforeSave($insert)
    {
        if (parent::beforeSave($insert)) {
            $attributes = $this->attributes;
            if ($this->isNewRecord) {
                // 如果新增记录
                if (array_key_exists('add_time', $attributes)) {
                    $this->add_time = is_null($this->add_time) ? date('Y-m-d H:i:s') : $this->add_time;
                }

                if (array_key_exists('update_time', $attributes)) {
                    $this->update_time = is_null($this->update_time) ? TimeHelper::ZERO_TIME : $this->update_time;
                }

                if (array_key_exists('status', $attributes)) {
                    $this->status = is_null($this->status) ? 1 : $this->status;
                }
            } else {
                // 如果更新记录
                if (array_key_exists('update_time', $attributes)) {
                    // update_time 不是空也不是0000 并且这次没有更新
                    if (array_key_exists('update_time', $attributes) && !array_key_exists('update_time',
                            $this->dirtyAttributes)) {
                        $this->update_time = date('Y-m-d H:i:s');
                    }
                }
            }

            // 拿出所有的rule
            $rules = $this->rules();

            // 循环找出是整形rule,判断是空的话重置为0
            foreach ($rules as $k => $v) {
                if ($v[1] == 'integer') {
                    // 整形
                    foreach ($v[0] as $param) {
                        if (empty($this->$param)) {
                            $this->$param = 0;
                        }
                    }
                }
            }

            return true;
        } else {
            return false;
        }
    }

    /**
     * --------------------------------------------------
     * 获取使用yii自带rule验证错误的时候，第一个错误信息
     * --------------------------------------------------
     *
     * @return [string]     [错误的信息]
     * @since  2016-12-28
     * <AUTHOR>
     */
    public function getFirstErrorsMessage()
    {
        $error = $this->getFirstErrors();
        if (!empty($error['error'])) {
            return false;
        }

        return current($error);
    }

    public function getSuccessMessage()
    {
        return $this->successMsg;
    }

    public function setSuccessMessage($msg)
    {
        $this->successMsg = $msg;
    }

    /**
     * --------------------------------------------------
     * 检查数据唯一性，只需要在rule配置好
     * --------------------------------------------------
     *
     * 例如在rule加入这个,可以对于新增和修改的时候对于多个字段同时相等的数据无法插入,下面那个就是name相等
     * ['name', 'only', 'params' => ['message'=>'该角色已经存在','field'=>['name']]],
     * @param  [array]     $attribute
     * @param  [array]     $params    [字段等参数]
     * <AUTHOR>
     * @since  2016-12-28
     */
    public function only($attribute, $params)
    {
        $query = $this->find()
            ->select('*')
            ->where("status='1'");
        foreach ($params['field'] as $v) {
            $query->andWhere([$v => $this->$v]);
        }
        if ($this->primaryKey) {
            $query->andWhere([
                '<>',
                'id',
                $this->primaryKey,
            ]);
        }
        if ($query->count() > 0) {
            $this->addError($attribute, $params['message']);
        }
    }

    /**
     * 找某一个值
     * @param $where
     * @param $key
     * @return mixed
     */
    public static function findOneVal($where, $key)
    {
        return self::find()
                   ->select($key)
                   ->where($where)
                   ->asArray()
                   ->one()[$key];
    }

    public static function findIsExist($where)
    {
        $query = self::find();
        if (is_array($where)) {
            $query->where($where);
        } else {
            $query->where(['id' => $where]);
        }

        return $query->exists();
    }

    /**
     * 用于格式化一些分页所需的参数
     *
     * @param int $count    总数量
     * @param int $page     当前页面
     * @param int $pageSize 每页多少条
     *
     * 返回的数组里面
     *  offset offset使用的
     *  limit  limit使用的
     *  total  总的页数
     */
    public static function setPage($count, $page, $pageSize)
    {
        $page = $page ?: 1;
        if ($count > 100) {
            $count = 100;
        }

        return [
            'offset' => ($page - 1) * $pageSize,
            'limit'  => $pageSize,
            'total'  => ceil($count / $pageSize),
            'page'   => $page,
        ];
    }

    public function addError($attribute, $error = '')
    {
        if (!$error) {
            parent::addError('', $attribute);
        } else {
            parent::addError($attribute, $error);
        }
    }

    public function load($data, $formName = '')
    {
        return parent::load($data, $formName);
    }

    public static function ThrowException($message)
    {
        throw new Exception($message);
    }

    /**
     * 格式化多个筛选值数组find_in_set查找条件
     * @param $searchArr
     * @param $field
     * @return string
     */
    public static function formatFindInSetQuery($searchArr, $field)
    {
        if (empty($searchArr)) {
            return '';
        }
        $query = '(';

        foreach ($searchArr as $k => $v) {
            $query .= "find_in_set($v,$field) or ";
        }
        $query = substr($query, 0, -3);
        $query .= ')';

        return $query;
    }

    public static function openDb2()
    {
        // 只有正式或者本地环境
        $environment = Yii::$app->params['environment'];
        if ($environment == 'prod' || $environment == 'local') {
            $key = Cache::ALL_DB1_DATA_KEY;
            try {
                // 更改为db2
                Yii::$app->db->close();
                $data                   = [
                    'dsn'      => Yii::$app->db->dsn,
                    'username' => Yii::$app->db->username,
                    'password' => Yii::$app->db->password,
                ];
                Yii::$app->db->dsn      = Yii::$app->db4->dsn;
                Yii::$app->db->username = Yii::$app->db4->username;
                Yii::$app->db->password = Yii::$app->db4->password;

                Cache::set($key, json_encode($data));
                Yii::$app->db->open();
            } catch (\Exception $e) {
                return false;
            }
        }

        return true;
    }

    public static function openDb3()
    {
        // 只有正式或者本地环境
        $environment = Yii::$app->params['environment'];
        if ($environment == 'prod') {
            $key = Cache::ALL_DB1_DATA_KEY;
            try {
                // 更改为db2
                Yii::$app->db->close();
                $data                   = [
                    'dsn'      => Yii::$app->db->dsn,
                    'username' => Yii::$app->db->username,
                    'password' => Yii::$app->db->password,
                ];
                Yii::$app->db->dsn      = Yii::$app->db3->dsn;
                Yii::$app->db->username = Yii::$app->db3->username;
                Yii::$app->db->password = Yii::$app->db3->password;

                Cache::set($key, json_encode($data));
                Yii::$app->db->open();
            } catch (\Exception $e) {
                return false;
            }
        }

        return true;
    }

    public static function closeDb2()
    {
        $environment = Yii::$app->params['environment'];
        // 更改为回去db1
        if ($environment == 'prod' || $environment == 'local') {
            $key  = Cache::ALL_DB1_DATA_KEY;
            $json = Cache::get($key);
            $data = json_decode($json, true);
            try {
                // 更改为db2
                Yii::$app->db->close();

                // Yii::$app->db->dsn      = Yii::$app->db2->dsn;
                // Yii::$app->db->username = Yii::$app->db2->username;
                // Yii::$app->db->password = Yii::$app->db2->password;
                //
                Yii::$app->db->dsn      = $data['dsn'];
                Yii::$app->db->username = $data['username'];
                Yii::$app->db->password = $data['password'];

                Yii::$app->db->open();
            } catch (\Exception $e) {
                return false;
            }
        }
    }

    /**
     * 根据参数获取查询条件
     * @param $params     array
     * @param $conditions array
     */
    public static function getSearchCondition($params, $conditions, $type = 'and')
    {
        $where = [];
        //        $conditions = [
        //            ['字段', '条件', '数据库字段']
        //        ];

        foreach ($conditions as $v) {
            $condition       = $v[1] ?? '=';
            $selectKey       = $v[2] ?? $v[0];
            $searchCondition = $params[$v[0]] ?? '';
            if ($searchCondition == '') {
                continue;
            }
            if ($v[1] == 'in' || $v[1] == 'not in') {
                $searchCondition = is_string($searchCondition) ? explode(',', $searchCondition) : $searchCondition;
            }
            $where[] = [
                $condition,
                $selectKey,
                $searchCondition,
            ];
        }

        if ($type == 'or') {
            return new OrCondition($where);
        }

        return new AndCondition($where);
    }

    /**
     * 根据参数获取排序
     * @param $conditions
     * @param $params
     */
    public static function getOrderByInParams($params, $conditions)
    {
        $orderArr = [];
        //        $conditions = [
        //            ['前端字段', '数据库对应字段']
        //        ];
        $orderMap = [
            '1' => 'desc',
            '2' => 'asc',
        ];
        foreach ($conditions as $v) {
            if (!isset($orderMap[$params[$v[0]]])) {
                continue;
            }
            $orderArr[] = $v[1] . ' ' . $orderMap[$params[$v[0]]];
        }

        return $orderArr;
    }

    /**
     * 获取随机ID
     * @param $BaseModelClass
     * @param $limit
     * @return array
     */
    public static function getRandIds($BaseModelClass, $limit = 50)
    {
        // 获取总记录数
        $totalCount = $BaseModelClass::find()
            ->count();

        // 如果总记录数小于等于限制数，直接返回所有ID
        if ($totalCount <= $limit) {
            return $BaseModelClass::find()
                ->select('id')
                ->column();
        }

        // 分批获取数据以避免内存溢出
        $batchSize = $limit;

        // 计算需要获取的批次
        $batches = ceil($totalCount / $batchSize);
        // 随机选择起始批次
        $startBatch = rand(0, $batches - 1);

        // 获取该批次的数据
        $currentIds = $BaseModelClass::find()
            ->select('id')
            ->offset($startBatch * $batchSize)
            ->orderBy('id desc')
            ->limit($batchSize)
            ->column();
        // 随机打乱并截取需要的数量
        shuffle($currentIds);

        return array_slice($currentIds, 0, $limit);
    }
}
