<?php

namespace common\service\abroadColumn;

// 有banner、广告位、筛选、列表
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomePosition;
use common\base\models\BaseJob;
use common\base\models\BaseMember;
use common\base\models\BaseShowcase;
use common\helpers\ArrayHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;
use Yii;
use yii\db\conditions\AndCondition;
use yii\db\Query;

class CompanyService extends BaseService
{
    /** @var string 单位列表广告位 */
    const DANWEI_C1 = 'hw_danweidating_C1';
    public $params;
    public $limit = 15;
    public $searchData;

    public function setData($parmas)
    {
        $this->params = $parmas;

        return $this;
    }

    public function getAll($isUpdateCache = false)
    {
        if (!$isUpdateCache) {
            $data = $this->getCache(self::CACHE_KEY_DWDATING);

            if ($data) {
                return $data;
            }
        }

        $banner             = $this->getBanner();
        $topShowcaseList    = $this->getTopShowcaseList();
        $bottomShowcaseList = $this->getBottomShowCase();
        $areaList           = $this->getAreaList();
        $companyTypeList    = $this->getCompanyTypeList();
        $companyList        = $this->getCompanyList();

        $data = [
            'banner'             => $banner,
            'topShowcaseList'    => $topShowcaseList,
            'bottomShowcaseList' => $bottomShowcaseList,
            'areaList'           => $areaList,
            'companyTypeList'    => $companyTypeList,
            'companyList'        => $companyList,
        ];

        $this->setCache(self::CACHE_KEY_DWDATING, $data);

        return $data;
    }

    /**
     * 搜索的接口
     * @return array|\yii\db\ActiveRecord[]
     * @throws \Exception
     */
    public function searchList($searchData)
    {
        $this->searchData = $searchData;

        return $this->getCompanyList();
    }

    /**
     * 获取banner
     * @return array|mixed|\yii\db\ActiveRecord[]
     */
    private function getBanner()
    {
        $key = 'hw_danweidating_HF';

        return $this->getCommonShowcaseList($key);
    }

    /**
     * 广告位-上2
     * @return array
     */
    private function getTopShowcaseList()
    {
        $key = 'hw_danweidating_A1';

        return $this->getCommonShowcaseList($key, 2);
    }

    /**
     * 广告位-下4
     * @return array
     */
    private function getBottomShowCase()
    {
        $key = 'hw_danweidating_B1';

        return $this->getCommonShowcaseList($key);
    }

    /**
     * 获取单位地区列表
     * @return mixed
     */
    private static function getAreaList()
    {
        $areaList = Yii::$app->params['jobAndCompanyListHotArea'];
        $newList  = [];
        foreach ($areaList as $item) {
            $newList[] = [
                'k' => $item['id'],
                'v' => $item['name'],
            ];
        }

        return $newList;
    }

    /**
     * 获取单位列表
     * @return array|\yii\db\ActiveRecord[]
     * @throws \Exception
     */
    private function getCompanyList()
    {
        // 1. 'is_showcase' => new \yii\db\Expression($isShowcase) //
        $showcaseField = $companyField = [
            'c.id',
            'c.logo_url',
            'c.full_name',
            'c.type',
            'c.nature',
            'c.city_id',
        ];

        // 1.广告优先
        $showcaseField['is_showcase'] = new \yii\db\Expression(1);
        $companyField['is_showcase']  = new \yii\db\Expression(2);

        // 2.广告排序
        $showcaseField['showcase_sort'] = 's.sort';    // 拿广告字段排序
        $companyField['showcase_sort']  = new \yii\db\Expression(0); // 没广告

        // 3.默认排序
        $companyField['overseas_sort_point']  = 'c.overseas_sort_point';
        $companyField['last_active_time']     = 'm.last_active_time';
        $showcaseField['overseas_sort_point'] = new \yii\db\Expression(0); // 无需默认
        $showcaseField['last_active_time']    = new \yii\db\Expression(0); // 无需默认

        // 需要显示广告统计
        $companyField['showcase_id']      = new \yii\db\Expression(0);
        $companyField['showcase_number']  = new \yii\db\Expression(0);
        $showcaseField['showcase_id']     = 's.id';
        $showcaseField['showcase_number'] = 'hp.number';

        // 使用union时，要保证先查询出来的广告数据优先，所以广告放第一
        $searchModel = (new Query())->select([
            'id',
            'logo_url',
            'full_name as name',
            'type',
            'nature',
            'city_id',
            'is_showcase',
            'showcase_id',
            'showcase_number',
            'showcase_sort',
            'overseas_sort_point',
            'last_active_time',
        ])
            ->from(['first' => ($this->getSearchShowcaseCompany($showcaseField))->union($this->getSearchCompany($companyField))])
            ->groupBy('id');

        $count = $searchModel->count();

        $pages = BaseCompany::setPage($count, $this->searchData['page'] ?: 1, $this->limit);
        $list  = $searchModel->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('is_showcase asc,showcase_sort desc,showcase_id desc,overseas_sort_point desc,last_active_time desc')
            ->all();

        if ($list) {
            foreach ($list as &$item) {
                //获取单位类型
                $type = BaseDictionary::getCompanyTypeName($item['type']);
                //获取单位性质
                $nature = BaseDictionary::getCompanyNatureName($item['nature']);
                //拼接
                if ($nature) {
                    $item['companyInfo'] = $type . ' | ' . $nature;
                } else {
                    $item['companyInfo'] = $type;
                }
                $item['address']                  = StringHelper::subStr(BaseArea::getAreaName($item['city_id']),
                    6) ?: '';
                $item['companyUrl']               = UrlHelper::createPcCompanyDetailPath($item['id']);
                $item['onlineAnnouncementAmount'] = BaseAnnouncement::getCompanyOnLineAnnouncementAmount($item['id']);
                $item['onlineJobAmount']          = BaseJob::getCompanyJobAmount($item['id']);
                $item['announcementList']         = $this->getAnnouncementList($item['id']);
                $item['logo_url']                 = BaseCompany::getLogoFullUrl($item['logo_url']);
            }
        } else {
            $list = [];
        }

        return [
            'list'  => $list,
            'limit' => $pages['limit'],
            'page'  => $pages['page'],
            'count' => (int)$count,
        ];
    }

    private function getSearchCompany($select)
    {
        $searchModel = BaseCompany::find()
            ->alias('c')
            ->innerJoin(['m' => BaseMember::tableName()], 'm.id=c.member_id')
            ->where([
                'c.status'    => BaseCompany::STATUS_ACTIVE,
                'c.is_hide'   => BaseCompany::IS_HIDE_NO,
                // 上线这里是需要打开了，现在为了多点数据
                'c.is_abroad' => BaseCompany::IS_ABROAD_YES,
            ]);

        if ($this->searchData['areaId']) {
            $area = $this->searchData['areaId'];
            if (count(explode(',', $area)) > 1) {
                $areaIds = explode(',', $area);
            } else {
                $areaIds = [$area];
            }

            $areaIds = BaseArea::getCityIds($areaIds);

            $searchModel->andWhere(['c.city_id' => $areaIds]);
        }

        //单位类型筛选
        if ($this->searchData['companyType']) {
            $companyTypeArr = explode(',', $this->searchData['companyType']);
            $searchModel->andFilterWhere([
                'in',
                'c.type',
                $companyTypeArr,
            ]);
        }
        $searchModel->select($select);

        return $searchModel;
    }

    private function getSearchShowcaseCompany($select)
    {
        $searchModel = BaseShowcase::find();
        $searchModel->alias('s')
            ->select($select)
            ->where(new AndCondition([
                [
                    '=',
                    'hp.number',
                    self::DANWEI_C1,
                ],
                [
                    '=',
                    's.is_show',
                    BaseShowcase::IS_SHOW_YES,
                ],
                [
                    '=',
                    's.status',
                    BaseShowcase::STATUS_ONLINE,
                ],
                [
                    '=',
                    'c.status',
                    BaseCompany::STATUS_ACTIVE,
                ],
                [
                    '=',
                    'c.is_hide',
                    BaseCompany::IS_HIDE_NO,
                ],
            ]))
            ->innerJoin(['hp' => BaseHomePosition::tableName()], 'hp.id = s.home_position_id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'c.id = s.company_id')
            ->orderBy('s.sort desc, s.id desc')
            ->groupBy('c.id')
            ->limit(6);

        if ($this->searchData['areaId']) {
            $area = $this->searchData['areaId'];
            if (count(explode(',', $area)) > 1) {
                $areaIds = explode(',', $area);
            } else {
                $areaIds = [$area];
            }

            $areaIds = BaseArea::getCityIds($areaIds);

            $searchModel->andWhere(['c.city_id' => $areaIds]);
        }

        //单位类型筛选
        if ($this->searchData['companyType']) {
            $companyTypeArr = explode(',', $this->searchData['companyType']);
            $searchModel->andFilterWhere([
                'in',
                'c.type',
                $companyTypeArr,
            ]);
        }

        return $searchModel;
    }

    private function getAnnouncementList($companyId)
    {
        $list = BaseAnnouncement::find()
            ->alias('an')
            ->leftJoin(['ar' => BaseArticle::tableName()], 'an.article_id = ar.id')
            ->select([
                'an.id',
                'ar.title',
                'ar.refresh_date',
            ])
            ->where([
                'an.company_id' => $companyId,
                'ar.is_delete'  => BaseArticle::IS_DELETE_NO,
                'an.status'     => BaseAnnouncement::STATUS_ONLINE,
                'ar.status'     => BaseArticle::STATUS_ONLINE,
                'ar.is_show'    => BaseArticle::IS_SHOW_YES,
            ])
            ->orderBy('refresh_date desc')
            ->limit(2)
            ->asArray()
            ->all();
        foreach ($list as &$item) {
            $item['announcementUrl'] = UrlHelper::createPcAnnouncementDetailPath($item['id']);
            $item['refreshDate']     = TimeHelper::formatDateByYear($item['refresh_date'], '.');
        }

        return $list;
    }
}
