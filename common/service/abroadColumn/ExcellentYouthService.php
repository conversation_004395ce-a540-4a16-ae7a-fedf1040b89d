<?php

namespace common\service\abroadColumn;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementAreaRelation;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleClickLog;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseNews;
use common\base\models\BaseShowcase;
use common\helpers\TimeHelper;
use common\helpers\UrlHelper;

class ExcellentYouthService extends BaseService
{

    public $relyAnnouncement = [];

    const RELY_ANNOUNCEMENT_AREA_LIST = [
        '0' => [
            'text'       => '全部',
            'provinceId' => [],
        ],
        '1' => [
            'text'       => '广东/广西/海南',
            'provinceId' => [
                1964,
                2162,
                2291,
            ],
        ],
        '2' => [
            'text'       => '河南/湖北/湖南/江西',
            'provinceId' => [
                1532,
                1709,
                1827,
                1263,
            ],
        ],
        '3' => [
            'text'       => '北京/天津/河北/山西/内蒙古',
            'provinceId' => [
                1,
                19,
                37,
                220,
                351,
            ],
        ],
        '4' => [
            'text'       => '黑龙江/吉林/辽宁',
            'provinceId' => [
                655,
                585,
                466,
            ],
        ],
        '5' => [
            'text'       => '山东/江苏/安徽',
            'provinceId' => [
                1375,
                820,
                1046,
            ],
        ],
        '6' => [
            'text'       => '上海/浙江/福建',
            'provinceId' => [
                801,
                933,
                1168,
            ],
        ],
        '7' => [
            'text'       => '陕西/宁夏/甘肃/青海/新疆',
            'provinceId' => [
                2898,
                3178,
                3022,
                3126,
                3206,
            ],
        ],
        '8' => [
            'text'       => '四川/重庆/云南/贵州/西藏',
            'provinceId' => [
                2367,
                2323,
                2670,
                2572,
                2816,
            ],
        ],
        '9' => [
            'text'       => '港澳台',
            'provinceId' => [
                3716,
                3738,
                3325,
            ],
        ],
    ];
    public $searchData;
    const ANNOUNCEMENT_LIMIT = 12;

    /**
     * @throws \yii\base\Exception
     */
    public function getAll($isUpdateCache = false)
    {
        if (!$isUpdateCache) {
            $data = $this->getCache(self::CACHE_KEY_HWYOUQ);

            if ($data) {
                return $data;
            }
        }

        $banner                    = $this->getBanner();
        $headlinesList             = $this->getHeadlinesList();
        $projectInfo               = $this->getProjectInfo();
        $recommendAnnouncementList = $this->getRecommendAnnouncement();
        $relyCompanyList           = $this->getRelyCompany();
        $lastActivityList          = $this->getLastActivityList();
        $relyAnnouncementList      = $this->getRelyAnnouncement();
        $relyAnnouncementAreaList  = $this->getRelyAnnouncementAreaList();
        $relateNewsList            = $this->getRelateNewsList();
        $moreActivityList          = $this->getMoreActivityList();
        $cooperationCaseList       = $this->getCooperationCaseList();
        $hotAnnouncement           = $this->getHotAnnouncement();
        $hotCompany                = $this->getHotCompany();

        $data = [
            'banner'                    => $banner,
            'headlinesList'             => $headlinesList,
            'projectInfo'               => $projectInfo,
            'recommendAnnouncementList' => $recommendAnnouncementList,
            'relyAnnouncementAreaList'  => $relyAnnouncementAreaList,
            'relyCompanyList'           => $relyCompanyList,
            'lastActivityList'          => $lastActivityList,
            'cooperationCaseList'       => $cooperationCaseList,
            'relateNewsList'            => $relateNewsList,
            'moreActivityList'          => $moreActivityList,
            'relyAnnouncementList'      => $relyAnnouncementList,
            'hotAnnouncement'           => $hotAnnouncement,
            'hotCompany'                => $hotCompany,
        ];

        $this->setCache(self::CACHE_KEY_HWYOUQ, $data);

        return $data;
    }

    private function getBanner()
    {
        $key = 'hw_haiwaiyouqing_HF';

        return $this->getCommonShowcaseList($key);
    }

    //一、新增公告文档属性：
    //       【海优-首头】、【海优-次条】
    //二、调用规则&排序：
    //展示公告属性勾选了【海优-首头】、【海优-次条】的在线公告（不含已下线、已隐藏），最多7条：
    //1、首头，最多1条——
    //     取已勾选【海优-首头】属性、且发布时间最新的1条公告展示；
    //2、次条，最多6条——
    //     取已勾选【海优-首头】属性的其他公告（非第1点的公告），和已勾选【海优-次条】属性的公告，按发布时间倒序，展示前6条公告（模块默认3行高度）；
    //    若条数≤2/4条，则自适应调整模块高度为1行/2行展示；
    private function getHeadlinesList()
    {
        $topHeadLines = $this->getHeadLines(BaseArticleAttribute::ATTRIBUTE_HOME_HAIYOU_TOP, 1);
        $topHeadId    = '';
        if ($topHeadLines[0]) {
            $topHeadId = $topHeadLines[0]['id'];
        }
        $otherLines = $this->getHeadLines([
            BaseArticleAttribute::ATTRIBUTE_HOME_HAIYOU_TOP,
            BaseArticleAttribute::ATTRIBUTE_HOME_HAIYOU_SECOND,
        ], 6, $topHeadId);

        return [
            'topList'    => $topHeadLines[0] ?: [],
            'otherLines' => $otherLines,
        ];
    }

    /**
     * 获取项目信息
     * @return array
     */
    private function getProjectInfo()
    {
        //写死简介
        $description = '，全称“国家自然科学基金优秀青年科学基金项目（海外）”，是由国家自然科学基金委员会于2021年设立的一个资助项目，旨在吸引和鼓励在自然科学、工程技术等方面已取得较好成绩的海外优秀青年学者（含非华裔外籍人才）回国（来华）工作，自主选择研究方向开展创新性研究，促进青年科学技术人才的快速成长，培养一批有望进入世界科技前沿的优秀学术骨干，为科技强国建设贡献力量。';
        //申报动态
        $trendsList = $this->getNewsList(BaseArticleAttribute::ATT_HAIYOU_APPLY_ACTIVITY, 9, 3);
        //申报干货
        $dryList = $this->getNewsList(BaseArticleAttribute::ATT_HAIYOU_APPLY_DRY, 9, 3);

        return [
            'description' => $description,
            'trendsList'  => $trendsList,
            'dryList'     => $dryList,
        ];
    }

    private function getNewsList($type, $limit, $pageSize)
    {
        $list    = BaseArticle::find()
            ->alias('a')
            ->innerJoin(['b' => BaseArticleAttribute::tableName()], 'a.id=b.article_id')
            ->innerJoin(['c' => BaseNews::tableName()], 'a.id=c.article_id')
            ->where([
                'a.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.type'      => $type,
                'a.status'    => BaseArticle::STATUS_ONLINE,
                'a.is_show'   => BaseArticle::IS_SHOW_YES,
                'a.type'      => BaseArticle::TYPE_NEWS,
            ])
            ->select([
                'title',
                'c.id',
                'a.refresh_date',
                'a.link_url',
            ])
            ->orderBy('a.refresh_time desc')
            ->limit($limit)
            ->asArray()
            ->all();
        $newList = [];

        foreach ($list as $k => &$item) {
            $item['url']         = $item['link_url'] ?: UrlHelper::createPcNewsDetailPath($item['id']);
            $pageKey             = floor($k / $pageSize);
            $newList[$pageKey][] = $item;
        }

        return $newList;
    }

    /**
     * 获取推荐公告模块
     * @return array
     */
    private function getRecommendAnnouncement()
    {
        $topShowcase = BaseShowcase::getByKey('hw_haiwaiyouqing_tuijiangonggao_A1', 2);
        $topList     = [];
        foreach ($topShowcase as $topItem) {
            $companyInfo = BaseCompany::findOne($topItem['company_id']);
            $topList[]   = [
                'companyName' => $companyInfo['full_name'],
                'companyLogo' => BaseCompany::getLogoFullUrl($companyInfo['logo_url']),
                'image'       => $topItem['image_url'],
                'title'       => $topItem['title'],
                'url'         => $topItem['target_link'],
                'id'          => $topItem['id'],
                'number'      => $topItem['number'],
            ];
        }

        $bottomShowcase = BaseShowcase::getByKey('hw_haiwaiyouqing_tuijiangonggao_B1', 4);
        $bottomList     = [];
        foreach ($bottomShowcase as $bottomItem) {
            $companyInfo  = BaseCompany::findOne($bottomItem['company_id']);
            $bottomList[] = [
                'companyName' => $companyInfo['full_name'],
                'image'       => $bottomItem['image_url'],
                'title'       => $bottomItem['title'],
                'url'         => $bottomItem['target_link'],
                'id'          => $bottomItem['id'],
                'number'      => $bottomItem['number'],
            ];
        }

        return [
            'topList'    => $topList,
            'bottomList' => $bottomList,
        ];
    }

    /**
     * 获取推荐单位模块
     * @return array|mixed|\yii\db\ActiveRecord[]
     */
    private function getRelyCompany()
    {
        $list    = BaseShowcase::getByKey('hw_haiwaiyouqing_tuijiandanwei', 8);
        $newList = [];
        foreach ($list as $item) {
            $newList[] = [
                'image'     => $item['image_url'] ?: '',
                'id'        => $item['id'] ?: '',
                'number'    => $item['number'] ?: '',
                'title'     => $item['title'] ?: '',
                'sub_title' => $item['sub_title'] ?: '',
                'url'       => $item['target_link'] ?: '',
            ];
        }

        return $newList;
    }

    /**
     * 获取近期活动模块
     * @return array|mixed|\yii\db\ActiveRecord[]
     */
    private function getLastActivityList()
    {
        $list    = BaseShowcase::getByKey('hw_haiwaiyouqing_jinqihuodong', 4);
        $newList = [];
        foreach ($list as $item) {
            $newList[] = [
                'image'  => $item['image_url'] ?: '',
                'title'  => $item['title'] ?: '',
                'url'    => $item['target_link'] ?: '',
                'id'     => $item['id'] ?: '',
                'number' => $item['number'] ?: '',
            ];
        }

        return $newList;
    }

    /**
     * 依托公告
     * @return array|void
     */
    public function getRelyAnnouncement($searchData = [])
    {
        $searchModel = BaseArticle::find()
            ->alias('a')
            ->innerJoin(['b' => BaseAnnouncement::tableName()], 'a.id = b.article_id')
            ->innerJoin(['ac' => BaseArticleColumn::tableName()], 'ac.article_id=b.article_id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'c.id=b.company_id')
            ->where([
                'a.is_delete'  => BaseArticle::IS_DELETE_NO,
                'a.type'       => BaseArticle::TYPE_ANNOUNCEMENT,
                'a.status'     => [
                    BaseArticle::STATUS_ONLINE,
                    BaseArticle::STATUS_OFFLINE,
                ],
                'ac.column_id' => BaseHomeColumn::ABROAD_YOUQING_ID,
            ]);

        if ($searchData['areaType']) {
            $provinceList = self::RELY_ANNOUNCEMENT_AREA_LIST[$searchData['areaType']]['provinceId'];
            if ($provinceList) {
                $searchModel->innerJoin(['aa' => BaseAnnouncementAreaRelation::tableName()],
                    'aa.announcement_id = b.id')
                    ->andWhere([
                        'aa.area_id' => $provinceList,
                        'level'      => 1,
                    ]);
            }
        }
        $searchModel->groupBy('b.id');
        //获取总数量
        $count = $searchModel->count();

        $pages = BaseAnnouncement::setPage($count, $searchData['page'] ?: 1, self::ANNOUNCEMENT_LIMIT);
        $list  = $searchModel->select([
            'b.title',
            'b.company_id',
            'b.id',
            'a.refresh_date',
            'b.highlights_describe',
            'b.article_id',
        ])
            ->orderBy('a.status,a.refresh_date desc,c.sort desc,b.id desc')
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();
        if (!$list) {
            $list = [];
        }
        if ($list) {
            foreach ($list as &$item) {
                $item['btnText']      = '立即查看';
                $item['url']          = UrlHelper::createPcAnnouncementDetailPath($item['id']);
                $item['refresh_date'] = TimeHelper::formatDateByYear($item['refresh_date'], '.');
                $item['address']      = BaseAnnouncement::getOnlineNewCityNameForMini($item['id']);
                $companyInfo          = BaseCompany::findOne($item['company_id']);
                $item['companyName']  = $companyInfo->full_name;
                $item['companyLogo']  = BaseCompany::getLogoFullUrl($companyInfo->logo_url);
                $item['date']         = $item['refresh_date'];
            }
        }

        $this->relyAnnouncement = $list;

        return [
            'list'  => $list,
            'limit' => $pages['limit'],
            'page'  => $pages['page'],
            'count' => (int)$count,
        ];
    }

    public function getRelyAnnouncementAreaList()
    {
        $config = self::RELY_ANNOUNCEMENT_AREA_LIST;
        $list   = [];
        foreach ($config as $k => $item) {
            $list[] = [
                'k' => $k,
                'v' => $item['text'],
            ];
        }

        return $list;
    }

    /**
     * 热门关注公告
     * 一、调用规则&排序：
     * 1、取调用至【海外优青-更多依托公告】板块的在线&已下线公告（不含已隐藏），按近30天公告浏览量（全端口数据）倒序排列，展示排行前10的公告；
     * 2、排序： 浏览量排行前10的公告（符合第1点），在线公告排前，下线公告排后；其次按浏览量倒序排列；
     * @return array
     */
    public function getHotAnnouncement()
    {
        // $list = BaseArticle::find()
        //     ->alias('a')
        //     ->innerJoin(['b' => BaseAnnouncement::tableName()], 'a.id = b.article_id')
        //     ->innerJoin(['ac' => BaseArticleColumn::tableName()], 'ac.article_id=b.article_id')
        //     ->select([
        //         'b.id',
        //         'a.id as articleId',
        //     ])
        //     ->where([
        //         'a.is_delete'  => BaseArticle::IS_DELETE_NO,
        //         'a.type'       => BaseArticle::TYPE_ANNOUNCEMENT,
        //         'a.status'     => [
        //             BaseArticle::STATUS_ONLINE,
        //             BaseArticle::STATUS_OFFLINE,
        //         ],
        //         'ac.column_id' => BaseHomeColumn::ABROAD_YOUQING_ID,
        //     ])
        //     ->asArray()
        //     ->all();

        $list = $this->relyAnnouncement;

        $_30DaysAgo = date('Y-m-d', strtotime('-30 days'));
        //遍历所有职位，计算30天公告浏览量
        foreach ($list as &$item) {
            $item['clickAmount'] = BaseArticleClickLog::find()
                ->where([
                    'article_id' => $item['article_id'],
                ])
                ->andWhere([
                    '>=',
                    'add_time',
                    $_30DaysAgo,
                ])
                ->count();
        }
        //按热度进行排序
        $sortArr = array_column($list, 'clickAmount');
        array_multisort($sortArr, SORT_DESC, $list);
        //取前8
        $list = array_slice($list, 0, 8);
        //数组分为在线公告、下线公告，再按热度排序
        $onlineList  = [];
        $offlineList = [];
        //遍历补全数据
        foreach ($list as &$announcement) {
            $info                         = BaseArticle::find()
                ->alias('a')
                ->innerJoin(['b' => BaseAnnouncement::tableName()], 'a.id = b.article_id')
                ->where(['b.id' => $announcement['id']])
                ->select([
                    'b.title',
                    'b.id',
                    'a.refresh_date',
                    'a.status',
                ])
                ->asArray()
                ->one();
            $announcement['refresh_date'] = TimeHelper::formatDateByYear($info['refresh_date'], '.');
            $announcement['title']        = $info['title'];
            $announcement['status']       = $info['status'];
            $announcement['url']          = UrlHelper::createPcAnnouncementDetailPath($announcement['id']);
            if ($info['status'] == BaseArticle::STATUS_ONLINE) {
                $onlineList[] = $announcement;
            } else {
                $offlineList[] = $announcement;
            }
        }

        //再次按热度进行排序
        $onlineListSort  = array_column($onlineList, 'clickAmount');
        $offlineListSort = array_column($offlineList, 'clickAmount');
        array_multisort($onlineListSort, SORT_DESC, $list);
        array_multisort($offlineListSort, SORT_DESC, $list);

        //拼接上线、下线列表
        $list = array_merge($onlineList, $offlineList);

        return $list;
    }

    /**
     * 一、调用规则&排序：
     * 1. 取调用至【海外优青-更多依托公告】板块的公告关联单位；
     * 2. 统计单位下发布时间为近3个月非隐藏公告，近15天全站浏览量，按总浏览量倒序取排行前10的单位；
     * 3. 排列单位；合作排前，非合作排后；不含隐藏单位。
     * @return array
     */
    public function getHotCompany()
    {
        $_30DaysAgo = date('Y-m-d', strtotime('-30 days'));

        // $list = BaseArticle::find()
        //     ->alias('a')
        //     ->innerJoin(['b' => BaseAnnouncement::tableName()], 'a.id = b.article_id')
        //     ->innerJoin(['ac' => BaseArticleColumn::tableName()], 'ac.article_id=b.article_id')
        //     ->select([
        //         'b.id',
        //         'a.id as articleId',
        //         'b.company_id',
        //     ])
        //     ->where([
        //         'a.is_show'    => BaseArticle::IS_SHOW_YES,
        //         'a.is_delete'  => BaseArticle::IS_DELETE_NO,
        //         'a.type'       => BaseArticle::TYPE_ANNOUNCEMENT,
        //         'a.status'     => [
        //             BaseArticle::STATUS_ONLINE,
        //             BaseArticle::STATUS_OFFLINE,
        //         ],
        //         'ac.column_id' => BaseHomeColumn::ABROAD_YOUQING_ID,
        //     ])
        //     ->andWhere([
        //         '>=',
        //         'a.refresh_date',
        //         $_30DaysAgo,
        //     ])
        //     ->asArray()
        //     ->all();

        $list = $this->relyAnnouncement;

        $_15DaysAgo = date('Y-m-d', strtotime('-15 days'));
        //遍历所有职位，计算30天公告浏览量
        foreach ($list as &$item) {
            $item['clickAmount'] = BaseArticleClickLog::find()
                ->where([
                    'article_id' => $item['article_id'],
                ])
                ->andWhere([
                    '>=',
                    'add_time',
                    $_15DaysAgo,
                ])
                ->count();
        }
        //按热度进行排序
        $sortArr = array_column($list, 'clickAmount');
        array_multisort($sortArr, SORT_DESC, $list);
        //取前8
        $companyIdList = [];
        foreach ($list as $item1) {
            $isHide = BaseCompany::findOneVal(['id' => $item1['company_id']], 'is_hide');
            if (!in_array($item1['company_id'],
                    $companyIdList) && count($companyIdList) < 8 && $isHide == BaseCompany::IS_HIDE_NO) {
                $companyIdList[] = $item1['company_id'];
            }
        }

        if (!$companyIdList) {
            return [];
        }

        $list = BaseCompany::find()
            ->where(['id' => $companyIdList])
            ->select([
                'full_name as companyName',
                'logo_url',
                'id',
                'city_id',
                'type',
            ])
            ->asArray()
            ->orderBy('is_cooperation asc')
            ->all();
        foreach ($list as &$company) {
            $company['url'] = UrlHelper::createPcCompanyDetailPath($company['id']);
            $type           = BaseDictionary::getCompanyTypeName($company['type']);
            $city           = BaseArea::getAreaName($company['city_id']);
            $infoArray      = [
                $city,
                $type,
            ];

            $infoArray = array_filter($infoArray, function ($item) {
                return !empty($item);
            });

            // 用 | 拼接
            $company['info'] = implode(' | ', $infoArray);
            $company['logo'] = BaseCompany::getLogoFullUrl($company['logo_url']);
        }

        return $list;
    }

    /**
     * 获取相关资讯
     * @return array
     */
    private function getRelateNewsList()
    {
        $list = $this->getNewsList(BaseArticleAttribute::ATT_HAIYOU_NEWS, 15, 10);

        return $list;
    }

    /**
     * 获取更多活动列表
     * @return array
     */
    private function getMoreActivityList()
    {
        $list    = BaseShowcase::getByKey('hw_haiwaiyouqing_gengduohuodong');
        $newList = [];
        foreach ($list as $item) {
            $newList[] = [
                'image'  => $item['image_url'] ?: '',
                'title'  => $item['title'] ?: '',
                'url'    => $item['target_link'] ?: '',
                'id'     => $item['id'] ?: '',
                'number' => $item['number'] ?: '',
            ];
        }

        return $newList;
    }

    /**
     * 获取合作案例列表
     * @return array
     */
    private function getCooperationCaseList()
    {
        $list    = BaseShowcase::getByKey('hw_haiwaiyouqing_hezuoanli', 3);
        $newList = [];
        foreach ($list as $item) {
            $newList[] = [
                'image'         => $item['image_url'] ?: '',
                'title'         => $item['title'] ?: '',
                'subTitle'      => $item['sub_title'] ?: '',
                'url'           => $item['image_link'] ?: '',
                'otherImageUrl' => $item['other_image_url'] ?: '',
                'secondTitle'   => $item['second_title'] ?: '',
                'id'     => $item['id'] ?: '',
                'number' => $item['number'] ?: '',
            ];
        }

        return $newList;
    }

}
