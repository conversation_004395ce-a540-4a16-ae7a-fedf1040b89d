<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\announcement;

use admin\models\ArticleAttribute;
use admin\models\Company;
use admin\models\CompanyInfoAuth;
use admin\models\Dictionary;
use admin\models\HomeColumn;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyInfoAuth;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\helpers\ArrayHelper;
use common\libs\Editor;
use Yii;
use yii\base\Exception;

/**
 * 公告富文本识别
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class IdentityService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 公告富文本识别
     * @throws Exception
     */
    public function run()
    {
        $editorContent = Yii::$app->request->post('content');
        $editor        = new Editor($editorContent);
        $res           = $editor->identify();
        $content       = $res['content'];
        $head          = $res['data'];
        /**
         * 'mainColumn'        => '所属栏目',
         * 'subColumn'         => '副栏目',
         * 'company'           => '合作单位',
         * 'applyType'         => '应聘方式',
         * 'documentAttribute' => '文档属性',
         * 'periodDate'        => '截止日期',
         * 'memberType'        => '合作类型',
         * 'companyName'       => '单位名称',
         * 'companyType'       => '单位类型',
         * 'companyNature'     => '单位性质',
         * 'applyAddress'      => '投递地址',
         * 'tag'               => '特色标签',
         */
        // 首先把内容给识别出来
        if ($head['mainColumn']) {
            // 主栏目,去找主栏目的id
            $columnId = HomeColumn::findOneVal(['name' => $head['mainColumn']], 'id');
        }
        // 副栏目
        if ($head['subColumn']) {
            $subColumnArr    = explode('；', $head['subColumn']);
            $subColumnIds    = ArrayHelper::getColumn(HomeColumn::find()
                ->select('id,name')
                ->where([
                    'name' => $subColumnArr,
                ])
                ->asArray()
                ->all(), 'id');
            $subColumnIdsStr = implode(',', $subColumnIds);
        }

        // 文档属性
        if ($head['documentAttribute']) {
            $documentAttributeArr = explode('；', $head['documentAttribute']);
            $abroadDocument       = [];
            $notAbroadDocument    = [];
            foreach ($documentAttributeArr as $item) {
                if ($key = array_search($item, BaseArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST)) {
                    if (in_array($item, BaseArticleAttribute::ANNOUNCEMENT_ATTRIBUTE_ABROAD_LIST)) {
                        $abroadDocument[] = strval($key);
                    } else {
                        $notAbroadDocument[] = strval($key);
                    }
                }
            }
        }

        if ($head['periodDate']) {
            // 变为年月日
            $periodDate = str_replace([
                '年',
                '月',
            ], '-', $head['periodDate']);
            $periodDate = str_replace('日', '', $periodDate);
            $periodDate = date('Y-m-d', strtotime($periodDate));
        }

        // 然后和数据库查询,拿到合适的数据
        $isCooperation = BaseCompany::COOPERATIVE_UNIT_NO;

        // 单位名称
        if ($head['companyName']) {
            // 如果是合作单位,就只能去找名称,不能新建,如果是非合作,那么如果没有,就新建?
            // 反正最后需要出来一个单位的id

            // 先去找这个公司是否有
            $companyInfo = BaseCompany::find()
                ->andWhere(['full_name' => $head['companyName']])
                ->andWhere(['status' => BaseCompany::STATUS_ACTIVE])
                ->select('id,is_cooperation,type,nature,full_name,delivery_type')
                ->asArray()
                ->one();
            $companyId   = $companyInfo['id'];
            // 判断是否找得到就有合作单位
            if (!$companyId) {
                $companyTypeId = BaseDictionary::getCodeByName(BaseDictionary::TYPE_COMPANY, $head['companyType']);
                if (!$companyTypeId) {
                    throw new Exception('不存在"' . $head['companyType'] . '"的单位类型');
                }
                $companyNatureId = BaseDictionary::getCodeByName(BaseDictionary::TYPE_COMPANY_NATURE,
                    $head['companyNature']);
                if (!$companyNatureId) {
                    throw new Exception('不存在"' . $head['companyNature'] . '"的单位类型');
                }
                // 没有id,得去创建
                $companyData = [
                    'type'     => $companyTypeId,
                    'fullName' => $head['companyName'],
                    'nature'   => $companyNatureId,
                ];
                $companyId   = CompanyInfoAuth::freeCreate($companyData);

                $companyInfo = BaseCompany::find()
                    ->andWhere(['id' => $companyId])
                    ->andWhere(['status' => BaseCompany::STATUS_ACTIVE])
                    ->select('id,is_cooperation,type,nature,full_name,delivery_type')
                    ->asArray()
                    ->one();
            } else {
                $isCooperation = $companyInfo['is_cooperation'];
            }

            $companyInfo['natureTxt'] = BaseDictionary::getCompanyNatureName($companyInfo['nature']);
            $companyInfo['typeTxt']   = BaseDictionary::getCompanyTypeName($companyInfo['type']);
        } else {
            throw new Exception('请检查单位名称，单位名称未识别到内容！');
        }

        $deliveryWay        = '';
        $extraNotifyAddress = '';
        if ($head['applyType']) {
            // 应聘方式
            $applyTypeArr = explode('；', $head['applyType']);
            if (in_array('平台投递', $applyTypeArr)) {
                if ($companyInfo['is_cooperation'] == BaseCompany::COMPANY_SYN_CONTACT_YES) {
                    if (count($applyTypeArr) == 1) {
                        $deliveryWay        = BaseJob::DELIVERY_WAY_PLATFORM;
                        $extraNotifyAddress = $head['applyAddress'];
                        $applyAddress       = '';
                    } else {
                        throw new Exception('报名方式填写了平台投递则不应该填写其他');
                    }
                } else {
                    throw new Exception('报名方式填写错误');
                }
            } else {
                $applyTypeIds = [];
                $signUpList   = BaseDictionary::getSignUpList();
                foreach ($applyTypeArr as $item) {
                    if ($key = array_search($item, $signUpList)) {
                        $applyTypeIds[] = (string)$key;
                    }
                }
                if (count($applyTypeIds) <= 0) {
                    throw new Exception('报名方式填写错误');
                }
                $applyTypeIdsStr = implode(',', $applyTypeIds);
                if ($companyInfo['is_cooperation'] == BaseCompany::COOPERATIVE_UNIT_YES) {
                    $deliveryWay = BaseJob::DELIVERY_WAY_EMAIL_LINK;
                }
                if ($head['applyAddress']) {
                    // 应聘方式
                    $applyAddress = $head['applyAddress'];
                }
            }
        }

        // 特色标签
        if ($head['tag']) {
            // 应聘方式
            $tagArr         = explode('；', $head['tag']);
            $featuresTagIds = [];
            foreach ($tagArr as $item) {
                if ($key = array_search($item, BaseArticle::ATTRIBUTE_TAG_LIST)) {
                    $featuresTagIds[]  = (string)$key;
                    $featuresTagIdsStr = implode(',', $featuresTagIds);
                }
            }
        }

        if ($head['attachmentNotice'] == 1) {
            $isAttachmentNotice = '1';
        } else {
            $isAttachmentNotice = '2';
        }
        if ($head['addressHideStatus'] == 1) {
            $addressHideStatus = BaseAnnouncement::ADDRESS_HIDE_STATUS_YES;
        } else {
            $addressHideStatus = BaseAnnouncement::ADDRESS_HIDE_STATUS_NO;
        }

        // 然后和数据库查询,拿到合适的数据

        return [
            'homeColumnId'           => $columnId,
            'homeSubColumnIds'       => $subColumnIdsStr ?: '',
            //'comboAttribute'         => $attributeDocument ?: [],
            'comboAttribute'         => $notAbroadDocument ?: [],
            'overseasAttribute'      => $abroadDocument ?: [],
            'periodDate'             => $periodDate ?: '',
            'isCooperation'          => (string)$isCooperation,
            'companyId'              => $companyId,
            'companyDeliveryType'    => $companyInfo['delivery_type'],
            'companyDeliveryTypeTxt' => BaseCompany::DELIVERY_TYPE_NAME[$companyInfo['delivery_type']],
            'companyTxt'             => $companyInfo['full_name'],
            'companyNatureTxt'       => $companyInfo['natureTxt'],
            'companyTypeTxt'         => $companyInfo['typeTxt'],
            'deliveryWay'            => (string)$deliveryWay,
            'extraNotifyAddress'     => $extraNotifyAddress,
            'applyAddress'           => $applyAddress,
            'applyType'              => $applyTypeIdsStr ?: '',
            'tagIds'                 => $featuresTagIdsStr ?: '',
            'isAttachmentNotice'     => $isAttachmentNotice,
            'content'                => $content,
            'title'                  => $head['title'],
            'addressHideStatus'      => $addressHideStatus,
        ];
    }
}