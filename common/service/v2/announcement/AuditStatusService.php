<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\announcement;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementEdit;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseAnnouncementLog;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyCollect;
use common\base\models\BaseJob;
use common\base\models\BaseJobEdit;
use common\base\models\BaseMemberMessage;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\IpHelper;
use Yii;
use yii\base\Exception;

/**
 * 公告审核
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class AuditStatusService extends BaseService
{
    /** @var BaseAnnouncementEdit */
    private $announcementEditInfo;
    /** @var BaseAnnouncementHandleLog */
    private $announcementHandleLogInfo;
    const TYPE_AUDIT_PASS   = 1;  //审核通过
    const TYPE_AUDIT_REFUSE = 2;  //审核拒绝并编辑

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 公告审核执行
     * @throws Exception
     * @throws MessageException
     */
    public function run()
    {
        $this->params = Yii::$app->request->post();
        if (!$this->params['id'] || !$this->params['auditStatus'] || !in_array($this->params['auditStatus'], [
                self::TYPE_AUDIT_PASS,
                self::TYPE_AUDIT_REFUSE,
            ])) {
            throw new Exception('参数错误');
        }
        //获取公告信息
        $this->setAnnouncement($this->params['id']);
        $this->setCompany($this->announcementInfo->company_id);
        $this->initInfo();
        //判断是否有权限审核
        if ($this->announcementInfo->audit_status !== BaseAnnouncement::STATUS_AUDIT_AWAIT) {
            throw new Exception('当前公告不在审核状态，不允许审核！');
        }
        if ($this->params['auditStatus'] == self::TYPE_AUDIT_REFUSE && !$this->params['opinion']) {
            throw new Exception('审核拒绝时，审核处理意见不能为空！');
        }
        //这里有一些审核之外的编辑信息我们需要提前验证一下合法性
        //只有没有审核通过历史才能修改
        if ($this->announcementInfo->status == BaseAnnouncement::STATUS_STAGING) {
            $this->checkData();
            $this->editData();
        }
        if ($this->params['auditStatus'] == self::TYPE_AUDIT_PASS) {
            //审核通过
            $this->pass();
        } else {
            //审核拒绝
            $this->refuse();
        }
        //写日志
        $this->handleLog();
        //无论审核通过还是拒绝--删除对应审核记录
        //删除公告
        BaseAnnouncementEdit::deleteAll(['announcement_id' => $this->announcementId]);
        //删除职位
        BaseJobEdit::deleteAll(['announcement_id' => $this->announcementId]);
        //后置处理
        $this->after();

        return true;
    }

    /**
     * 后置处理
     */
    private function after()
    {
        $this->runAutoColumnAfter();
    }

    /**
     * 审核通过
     * @throws Exception
     */
    private function pass()
    {
        //是否有无审核通过历史
        if ($this->announcementInfo->status == BaseAnnouncement::STATUS_STAGING) {
            // 没有审核通过历史，即首次发布审核通过公告
            $this->announcementInfo->is_first_release = $this->companyInfo->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES ? BaseAnnouncement::IS_FIRST_RELEASE_YES : BaseAnnouncement::IS_FIRST_RELEASE_NO;

            $this->articleInfo->first_release_time = CUR_DATETIME; //首次发布时间
            $this->articleInfo->refresh_time       = CUR_DATETIME; //刷新时间
            $this->articleInfo->refresh_date       = CUR_DATE; //刷新时间

            //设置公告下的职位审核状态-是本公告职位且是待审核的职位
            BaseJob::updateAll(['audit_status' => BaseJob::AUDIT_STATUS_REFUSE_AUDIT], [
                'announcement_id' => $this->announcementId,
                'audit_status'    => BaseJob::AUDIT_STATUS_WAIT_AUDIT,
            ]);
            $title = '【新职位发布】您关注的单位“' . $this->companyInfo->full_name . '”发布了新公告！';

            $link_key    = BaseMemberMessage::LINK_TYPE_ANNOUNCEMENT_DETAIL;
            $link_params = [
                'id' => $this->announcementInfo->id,
            ];
            $content     = '您关注的单位“' . $this->companyInfo->full_name . '”发布了一条新公告：“' . $this->announcementInfo->title . '”，快去看看吧～';
            BaseMemberMessage::send(BaseCompanyCollect::collectMemberIds($this->companyInfo->id),
                BaseMemberMessage::TYPE_RESUME_SYSTEM, $title, $content, $link_key, $link_params);

            //写日志
            $this->log(BaseAnnouncementLog::TYPE_AUDIT_AGREE_FIRST);
        } else {
            //只要公告+职位审核那说明announcement_edit里面必有一条数据
            $this->announcementHandleLogInfo = BaseAnnouncementHandleLog::findOne([
                'announcement_id' => $this->announcementId,
                'handle_type'     => BaseAnnouncementHandleLog::HANDLE_TYPE_EDIT,
            ]);
            //只要公告+职位审核那说明announcement_edit里面必有一条数据
            $this->announcementEditInfo = BaseAnnouncementEdit::findOne(['announcement_id' => $this->announcementId]);
            if (!$this->announcementHandleLogInfo || !$this->announcementEditInfo) {
                throw new Exception('审核记录不存在，请刷新或者联系管理员');
            }
            $isAuditAnnouncement = false;
            $isAuditAddJob       = false;
            $isAuditEditJob      = false;
            // 有通过历史
            switch ($this->announcementHandleLogInfo->editor_type) {
                //===仅修改公告===
                case BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT:
                    $isAuditAnnouncement = true;
                    break;
                //===仅新增职位===
                case BaseAnnouncement::TYPE_ADD_JOB:
                    $isAuditAddJob = true;
                    break;
                //===仅编辑职位===
                case BaseAnnouncement::TYPE_EDITOR_JOB:
                    $isAuditEditJob = true;
                    break;
                //===编辑职位+新增职位===
                case BaseAnnouncement::TYPE_EDITOR_ADD_JOB:
                    $isAuditAddJob  = true;
                    $isAuditEditJob = true;
                    break;
                //===编辑公告+编辑职位===
                case BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT_JOB:
                    $isAuditAnnouncement = true;
                    $isAuditEditJob      = true;
                    break;
                //===编辑公告+新增职位===
                case BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT_ADD_JOB:
                    $isAuditAnnouncement = true;
                    $isAuditAddJob       = true;
                    break;
                //===编辑公告+编辑职位+新增职位===
                case BaseAnnouncement::TYPE_EDITOR_ANNOUNCEMENT_JOB_ADD_JOB:
                    $isAuditAnnouncement = true;
                    $isAuditAddJob       = true;
                    $isAuditEditJob      = true;
                    break;
                default:
                    throw new Exception('修改类型非法');
            }
            if ($isAuditAnnouncement) {
                $this->announcementEdit();
            }
            //这两个updateEditJob、updateAddJob不要保持修改在前逻辑
            if ($isAuditEditJob) {
                $this->updateEditJob();
            }
            if ($isAuditAddJob) {
                $this->updateAddJob();
            }
            $this->log(BaseAnnouncementLog::TYPE_AUDIT_AGREE);
        }

        $this->announcementInfo->audit_status     = BaseAnnouncement::STATUS_AUDIT_PASS;
        $this->announcementInfo->status           = BaseAnnouncement::STATUS_ONLINE;
        $this->announcementInfo->audit_admin_id   = $this->params['userId'];
        $this->announcementInfo->audit_admin_name = $this->params['username'];
        if (!$this->announcementInfo->save()) {
            throw new Exception($this->announcementInfo->getFirstErrorsMessage());
        }

        $this->articleInfo->status       = BaseArticle::STATUS_ONLINE;
        $this->articleInfo->release_time = CUR_DATETIME; //发布时间
        if (!$this->articleInfo->save()) {
            throw new Exception($this->articleInfo->getFirstErrorsMessage());
        }
    }

    /**
     * 审核拒绝
     */
    private function refuse()
    {
        $announcementModel                   = BaseAnnouncement::findOne($this->announcementId);
        $announcementModel->audit_status     = BaseAnnouncement::STATUS_AUDIT_REFUSE;
        $announcementModel->audit_admin_id   = $this->params['userId'];
        $announcementModel->audit_admin_name = $this->params['username'];
        if (!$announcementModel->save()) {
            throw new Exception($announcementModel->getFirstErrorsMessage());
        }

        //设置公告下的职位审核状态-是本公告职位且是待审核的职位
        BaseJob::updateAll(['audit_status' => BaseJob::AUDIT_STATUS_REFUSE_AUDIT], [
            'announcement_id' => $this->announcementId,
            'audit_status'    => BaseJob::AUDIT_STATUS_WAIT_AUDIT,
            'is_show'         => BaseJob::IS_SHOW_YES,
        ]);

        $this->log(BaseAnnouncementLog::TYPE_AUDIT_REFUSE);
    }

    /**
     * 没有审核通过历史的时候，检查数据合法性
     * @throws Exception
     */
    private function checkData()
    {
        if (!$this->params['homeColumnId']) {
            throw new Exception('请选择所属栏目');
        }
        //这里判断不严谨 理论上要对应到对应模板字段
        if ($this->params['backgroundImgFileType'] == BaseAnnouncement::BACKGROUND_IMG_FILE_TYPE_CUSTOM && empty($this->params['backgroundImgFileId']) && empty($this->params['backgroundImgFileId2']) && empty($this->params['backgroundImgFileId3'])) {
            throw new Exception('请上传自定图片');
        }
    }

    /**
     * 没有审核通过历史，提交的信息变更
     * @throws Exception
     */
    private function editData()
    {
        if ($this->params['templateId'] > 0) {
            $announcementModel                           = BaseAnnouncement::findOne($this->announcementId);
            $announcementModel->template_id              = $this->params['templateId'];
            $announcementModel->background_img_file_id   = $this->params['backgroundImgFileId'] ?: 0;
            $announcementModel->background_img_file_id_2 = $this->params['backgroundImgFileId2'] ?: 0;
            $announcementModel->background_img_file_id_3 = $this->params['backgroundImgFileId3'] ?: 0;
            $announcementModel->background_img_file_type = $this->params['backgroundImgFileType'] ?: BaseAnnouncement::BACKGROUND_IMG_FILE_TYPE_DEFAULT;

            if (!$announcementModel->save()) {
                throw new Exception($announcementModel->getFirstErrorsMessage());
            }
        }

        $articleModel                 = BaseArticle::findOne($this->articleId);
        $articleModel->home_column_id = $this->params['homeColumnId'];
        if (strlen($this->params['homeSubColumnIds']) > 0) {
            $articleModel->home_sub_column_ids = $this->params['homeSubColumnIds'];
        }
        if (strlen($this->params['tagIds']) > 0) {
            $articleModel->tag_ids = $this->params['tagIds'];
        }
        if (!$articleModel->save()) {
            throw new Exception($articleModel->getFirstErrorsMessage());
        }
        if ($this->params['comboAttribute'] || $this->params['overseasAttribute']) {
            $attribute     = array_unique(ArrayHelper::merge($this->params['comboAttribute'],
                $this->params['overseasAttribute']));
            $attributeData = [
                'attribute'                => $attribute,
                'indexTopEndTime'          => $this->params['indexTopEndTime'] ?: '',
                'columnTopEndTime'         => $this->params['columnTopEndTime'] ?: '',
                'doctorPushEndTime'        => $this->params['doctorPushEndTime'] ?: '',
                'overseasIndexTopEndTime'  => $this->params['overseasIndexTopEndTime'] ?: '',
                'overseasColumnTopEndTime' => $this->params['overseasColumnTopEndTime'] ?: '',
            ];
            $attributeList = BaseArticleAttribute::formatAttributeList($attributeData);
            // 审核操作属性
            BaseArticleAttribute::createAttribute($this->articleId, $attributeList);
        }
    }

    /**
     * 编辑类型-仅修改公告
     * @throws Exception
     */
    private function announcementEdit()
    {
        $handleAfterMessage = json_decode($this->announcementEditInfo->edit_content, true);
        //将编辑表内容更新到文章表
        if ($handleAfterMessage['content'] && $handleAfterMessage['file_ids']) {
            $this->articleInfo->content       = $handleAfterMessage['content'];
            $this->announcementInfo->file_ids = $handleAfterMessage['file_ids'];
        } elseif ($handleAfterMessage['content']) {
            $this->articleInfo->content = $handleAfterMessage['content'];
        } else {
            $this->announcementInfo->file_ids = $handleAfterMessage['file_ids'] ?: '';
        }
    }

    /**
     * 修改职位的状态-初始通过
     * @throws Exception
     */
    private function updateAddJob()
    {
        BaseJob::updateAll([
            'status'             => BaseJob::STATUS_ONLINE,
            'audit_status'       => BaseJob::AUDIT_STATUS_PASS_AUDIT,
            'release_time'       => CUR_DATETIME,
            'first_release_time' => CUR_DATETIME,
            'refresh_time'       => CUR_DATETIME,
            'refresh_date'       => CUR_DATE,
            'is_first_release'   => $this->companyInfo->is_cooperation == BaseCompany::COOPERATIVE_UNIT_YES ? BaseJob::IS_FIRST_RELEASE_YES : BaseJob::IS_FIRST_RELEASE_NO,
        ], [
            'announcement_id' => $this->announcementId,
            'audit_status'    => BaseJob::AUDIT_STATUS_WAIT_AUDIT,
            'is_show'         => BaseJob::IS_SHOW_YES,
        ]);
    }

    /**
     * 编辑类型-仅修改职位
     * @throws Exception
     */
    private function updateEditJob()
    {
        // 获取开启编辑的职位id
        $jobEditData = BaseJobEdit::find()
            ->select('job_id,edit_content')
            ->where([
                'announcement_id' => $this->announcementId,
                'status'          => BaseJobEdit::STATUS_ONLINE,
            ])
            ->orderBy('id desc')
            ->asArray()
            ->all();
        foreach ($jobEditData as $item) {
            $editContent = json_decode($item['edit_content'], true);
            foreach ($editContent as $v) {
                $jobUpdateData = BaseJob::findOne($item['job_id']);
                if (array_key_exists('duty', $v)) {
                    $jobUpdateData->duty = $v['duty'];
                }
                if (array_key_exists('requirement', $v)) {
                    $jobUpdateData->requirement = $v['requirement'];
                }
                if (array_key_exists('remark', $v)) {
                    $jobUpdateData->remark = $v['remark'];
                }
                if (array_key_exists('file_ids', $v)) {
                    $jobUpdateData->file_ids = $v['file_ids'];
                }
                $jobUpdateData->status       = BaseJob::STATUS_ONLINE;
                $jobUpdateData->audit_status = BaseJob::AUDIT_STATUS_PASS_AUDIT;
                $jobUpdateData->release_time = CUR_DATETIME; // 发布时间
                //更新到正式职位表
                if (!$jobUpdateData->save()) {
                    throw new Exception($jobUpdateData->getFirstErrorsMessage());
                }
            }
        }
    }

    /**
     * 公告审核日志
     * @throws Exception
     * @throws \yii\base\NotSupportedException
     */
    protected function handleLog()
    {
        //操作动作入表
        $handleBefore = [
            'audit_status' => BaseJob::AUDIT_STATUS_WAIT_AUDIT,
        ];
        $handleAfter  = [
            'audit_status' => $this->params['auditStatus'] == self::TYPE_AUDIT_PASS ? BaseAnnouncement::STATUS_AUDIT_PASS : BaseAnnouncement::STATUS_AUDIT_REFUSE,
            'opinion'      => $this->params['opinion'],
        ];
        $handleLogArr = [
            'add_time'        => CUR_DATETIME,
            'announcement_id' => $this->announcementId,
            'handle_type'     => (string)BaseAnnouncementHandleLog::HANDLE_TYPE_AUDIT,
            'handler_type'    => $this->params['platformType'],
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode($handleBefore),
            'handle_after'    => json_encode($handleAfter),
            'ip'              => IpHelper::getIpInt(),
        ];
        BaseAnnouncementHandleLog::createInfo($handleLogArr);
    }
}