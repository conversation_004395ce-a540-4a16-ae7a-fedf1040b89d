<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use admin\models\Dictionary;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberConfig;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseMajor;
use common\base\models\BaseWelfareLabel;
use common\helpers\ArrayHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use Yii;
use yii\base\Exception;

/**
 * 职位详情
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class DetailService extends BaseService
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 职位详情
     * @return array|\yii\db\ActiveRecord
     * @throws Exception
     */
    public function run()
    {
        $id = Yii::$app->request->get('id');
        if ($id < 0) {
            throw new Exception('参数错误');
        }
        $this->jobId  = $id;
        $this->detail = $this->getJobDetail();
        $this->processBasicInfo();

        return $this->detail;
    }

    /**
     * 获取职位基本信息
     * @throws Exception
     */
    private function getJobDetail(): array
    {
        if (!$this->jobId) {
            throw new Exception('职位ID不能为空，$this->jobId未赋值');
        }

        return BaseJob::find()
            ->select([
                'id as jobId',
                'status',
                'company_id as companyId',
                'name',
                'period_date as periodDate',
                'code',
                'job_category_id as jobCategoryId',
                'education_type as educationType',
                'major_id as majorId',
                'nature_type as natureType',
                'is_negotiable as isNegotiable',
                'wage_type as wageType',
                'min_wage as minWage',
                'max_wage as maxWage',
                'experience_type as experienceType',
                'age_type as ageType',
                'min_age as minAge',
                'max_age as maxAge',
                'title_type as titleType',
                'political_type as politicalType',
                'abroad_type as abroadType',
                'amount',
                'department',
                'province_id as provinceId',
                'city_id as cityId',
                'district_id as districtId',
                'address',
                'welfare_tag as welfareTag',
                'duty',
                'requirement',
                'remark',
                'audit_status as auditStatus',
                'is_show as isShow',
                'apply_type as applyType',
                'apply_address as applyAddress',
                'release_time as releaseTime',
                'add_time as addTime',
                'file_ids as fileIds',
                'delivery_limit_type as deliveryLimitType',
                'delivery_type as deliveryType',
                'extra_notify_address as extraNotifyAddress',
                'delivery_way as deliveryWay',
                'establishment_type as establishmentType',
                'announcement_id as announcementId',
                'is_establishment as isEstablishment',
            ])
            ->where(['id' => $this->jobId])
            ->asArray()
            ->one();
    }
}