<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseJobLog;
use common\base\models\BaseJobTemp;
use Yii;
use yii\base\Exception;

/**
 * 编辑临时职位
 * 基础建设服务类
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class TempEditService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 执行编辑职位
     * @throws Exception
     */
    public function run()
    {
        //获取表单参数
        $this->params = Yii::$app->request->post();
        //1=临时 2=不是
        if ($this->params['isTemp'] == BaseJobTemp::IS_TEMP_YES) {
            //临时加编辑这就是真的编辑临时职位
            //初始化一些数据
            $this->initInfo();
            //检验数据合法性
            $this->dataVerify();
            //调用写表逻辑写入职位表-新增临时职位
            $this->saveTempTable(false);
            //返回数据
            $data = $this->tempItem();
        } else {
            //这时候
            // 不是临时职位，又走编辑逻辑这时候其实是新增临时职位
            $data = (new TempAddService())->setPlatform($this->operationPlatform)
                ->run();
        }

        //完成返回
        return $data;
    }
}