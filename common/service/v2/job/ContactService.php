<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseJob;
use common\base\models\BaseJobContact;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseJobLog;
use common\helpers\IpHelper;
use Yii;
use yii\base\Exception;

/**
 * 职位联系人及协同联系人
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class ContactService extends BaseService
{
    /** @var bool 是否批量 */
    private $isBatch = false;
    /** @var string 联系人名称 */
    private $jobContactName = '';
    /** @var string 协同联系人名称 */
    private $jobContactSynergyName = '';
    /** 单位账号列表 */
    private $companyMemberList = [];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 设置是否是批量
     * @return $this
     */
    public function setBatch()
    {
        $this->isBatch = true;

        return $this;
    }

    /**
     * 职位联系人及协同联系人变更
     * @throws Exception
     */
    public function run()
    {
        $this->params = Yii::$app->request->post();
        if ((!isset($this->params['jobId']) || empty($this->params['jobId'])) || (!isset($this->params['companyId']) || empty($this->params['companyId']))) {
            throw new Exception('参数错误');
        }
        if (!$this->params['jobContactId']) {
            throw new Exception('联系人必需选择');
        }
        $this->params['jobContactSynergyIds'] = $this->params['jobContactSynergyIds'] ? explode(',',
            $this->params['jobContactSynergyIds']) : []; //这里参数里面含有companyId
        $this->initInfo();
        //判断单位是否是合作单位
        if ($this->companyInfo->is_cooperation != BaseJob::IS_CONSUME_RELEASE_YES) {
            throw new Exception('该单位不是合作单位');
        }
        $ids          = explode(',', $this->params['jobId']);
        $companyCount = BaseJob::find()
            ->where(['id' => $ids])
            ->groupBy('company_id')
            ->count();
        if ($companyCount != 1) {
            throw new Exception('职位不属于同一家单位');
        }

        //获取单位下的所有账号
        $accountList             = BaseCompanyMemberInfo::find()
            ->select([
                'id',
                'contact',
                'department',
            ])
            ->where(['company_id' => $this->params['companyId']])
            ->indexBy('id')
            ->asArray()
            ->all();
        $this->companyMemberList = $accountList;
        $accountIds              = count($accountList) > 0 ? array_column($accountList, 'id') : [];
        $this->jobContactName    = isset($accountList[$this->params['jobContactId']]) ? $accountList[$this->params['jobContactId']]['contact'] . '(' . $accountList[$this->params['jobContactId']]['department'] . ')' : '';
        //联系人判断
        if ($this->params['jobContactId'] && !in_array($this->params['jobContactId'], $accountIds)) {
            throw new Exception('联系人不属于该单位');
        }
        //协同账号判断
        $jobContactSynergyIdsCount = count($this->params['jobContactSynergyIds']);
        if ($jobContactSynergyIdsCount > 0) {
            if (count(array_intersect($this->params['jobContactSynergyIds'],
                    $accountIds)) != $jobContactSynergyIdsCount) {
                throw new Exception('协同账号不属于该单位');
            }
            $jobContactSynergyNameArr = [];
            foreach ($this->params['jobContactSynergyIds'] as $jobContactSynergyId) {
                if (isset($accountList[$this->params['jobContactSynergyIds']])) {
                    $jobContactSynergyNameArr[] = $accountList[$jobContactSynergyId]['contact'] . '(' . $accountList[$jobContactSynergyId]['department'] . ')';
                }
            }
            $this->jobContactSynergyName = implode(',', $jobContactSynergyNameArr);
        }
        //获取单位主账号信息
        $companyMemberInfo = BaseCompanyMemberInfo::findOne([
            'company_id'          => $this->params['companyId'],
            'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
        ]);
        if (!in_array($this->params['jobContactId'],
                array_unique($this->params['jobContactSynergyIds'])) && $this->params['jobContactId'] != $companyMemberInfo->id) {
            throw new Exception('职位联系人必须是协同账号或者是单位主账号');
        }
        if (count($ids) > 0 && $this->isBatch) {
            //批量
            foreach ($ids as $idItem) {
                $this->contactOne($idItem);
            }
        } else {
            //单个
            $this->contactOne($this->params['jobId']);
        }

        return true;
    }

    /**
     * 单个职位联系人及协同联系人变更
     * @throws Exception
     */
    private function contactOne($id)
    {
        $jobModel = BaseJob::findOne($id);
        if (!$jobModel) {
            throw new Exception('职位不存在');
        }
        //初始化一写数据
        $this->jobId          = $id;
        $this->jobInfo        = $jobModel;
        $this->oldJobInfo     = $jobModel;
        $jobContactInfo       = BaseJobContact::findOne(['job_id' => $id]);
        $jobContactSynergyIds = BaseJobContact::find()
            ->select('company_member_info_id')
            ->where(['job_id' => $id])
            ->asArray()
            ->column();

        $jobContactNameOld        = isset($this->companyMemberList[$jobContactInfo->company_member_info_id]) ? $this->companyMemberList[$jobContactInfo->company_member_info_id]['contact'] . '(' . $this->companyMemberList[$jobContactInfo->company_member_info_id]['department'] . ')' : '';
        $jobContactSynergyNameOld = '';
        foreach ($jobContactSynergyIds as $jobContactSynergyId) {
            if (isset($this->companyMemberList[$jobContactSynergyId])) {
                $jobContactSynergyNameOld .= $this->companyMemberList[$jobContactSynergyId]['contact'] . '(' . $this->companyMemberList[$jobContactSynergyId]['department'] . ')' . ',';
            }
        }
        $jobContactSynergyNameOld = rtrim($jobContactSynergyNameOld, ',');
        //更新职位的联系人和协同账号
        $insert = [
            'job_id'                 => $id,
            'company_id'             => $this->params['companyId'],
            'company_member_info_id' => $this->params['jobContactId'],
            'announcement_id'        => $this->jobInfo->announcement_id,
        ];
        //联系人更新
        BaseJobContact::add($insert);
        //协同账号更新
        $insert_synergy = [
            'job_id'                 => $id,
            'company_id'             => $this->params['companyId'],
            'company_member_info_id' => $this->params['jobContactSynergyIds'],
            'announcement_id'        => $this->jobInfo->announcement_id,
        ];
        BaseJobContactSynergy::addBatch($insert_synergy);

        //记录操作日志
        BaseJobHandleLog::createInfo([
            'add_time'        => date('Y-m-d H:i:s'),
            'job_id'          => $id,
            'handle_type'     => BaseJobHandleLog::HANDLE_TYPE_CONTACT,
            'handler_type'    => $this->params['platformType'],
            'handler_id'      => $this->params['userId'],
            'handler_name'    => $this->params['username'],
            'handle_before'   => json_encode([
                '联系人设置'     => $jobContactNameOld,
                '协同联系人设置' => $jobContactSynergyNameOld,
            ]),
            'handle_after'    => json_encode([
                '联系人设置'     => $this->jobContactName,
                '协同联系人设置' => $this->jobContactSynergyName,
            ]),
            'ip'              => IpHelper::getIpInt(),
            'announcement_id' => $this->jobInfo->announcement_id ?: 0,
        ]);
        //写一下日志
        $this->log($this->isBatch ? BaseJobLog::TYPE_CONTACT_BATCH : BaseJobLog::TYPE_CONTACT);

        return true;
    }
}