<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseJob;
use common\components\MessageException;
use common\helpers\FormatConverter;
use common\libs\JobBatchImport;
use common\service\announcement\BatchJobService;
use Yii;
use yii\base\Exception;

/**
 * 批量导入新增临时职位
 * 基础建设服务类
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class TempAddBatchImportService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 导入批量新增临时职位
     * @throws Exception
     * @throws MessageException
     */
    public function run()
    {
        $request = Yii::$app->request->post();

        if (!$request['filePath'] || $request['companyId'] <= 0) {
            throw new Exception('参数错误');
        }
        $filePath = Yii::getAlias('@frontendPc') . '/web/' . $request['filePath'];
        // 判断一下文件是否存在
        if (!file_exists($filePath)) {
            throw new Exception('文件不存在');
        }
        $this->setCompany($request['companyId']);
        // 读取数据
        $model = new JobBatchImport();
        $model->identify($filePath,
            $this->operationPlatform == self::PLATFORM_ADMIN ? JobBatchImport::PLATFORM_ADMIN : BaseCompany::getDeliveryTypeCate($this->companyInfo->delivery_type));
        $data = $model->clearData;
        // 插入数据
        $resultData = [];
        foreach ($data as $item) {
            $jobData                    = array_column($item, 'value', 'key');
            $jobData['announcement_id'] = $request['announcementId'];
            $jobData['company_id']      = $request['companyId'];
            $dataItem                   = (new TempAddService())->setPlatform($this->operationPlatform)
                ->setBatch($jobData)
                ->run();

            $resultData[] = $dataItem;
        }

        //删除文件
        unlink($filePath);

        return $resultData;
    }
}