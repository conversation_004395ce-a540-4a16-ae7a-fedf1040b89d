<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use common\base\models\BaseJobTemp;
use Yii;
use yii\base\Exception;

/**
 * 删除临时职位
 * 基础建设服务类
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class TempDeleteService extends BaseService
{

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 设置数据
     * @return $this
     */
    public function setParams($params)
    {
        $this->params = $params;

        return $this;
    }

    /**
     * 执行
     * @throws Exception
     */
    public function run()
    {
        //获取表单参数
        if (!$this->params) {
            $this->params = Yii::$app->request->post();
        }

        BaseJobTemp::deleteAll(['id' => $this->params['id']]);

        return true;
    }
}