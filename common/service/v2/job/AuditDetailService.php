<?php
/**
 * create user：shannon
 * create time：2025/4/22 上午9:09
 */
namespace common\service\v2\job;

use admin\models\Announcement;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseCompany;
use common\base\models\BaseFile;
use common\base\models\BaseJob;
use common\base\models\BaseJobHandleLog;
use common\helpers\FileHelper;
use common\helpers\StringHelper;
use Faker\Provider\Base;
use yii\base\Exception;
use Yii;

/**
 * 职位审核详情
 * 基础建设服务类
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 功能：处理所有【运营相关】数据处理
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class AuditDetailService extends BaseService
{
    private $historyStatus;
    const HISTORY_STATUS_TITLE = [
        1 => '有审核通过历史',
        2 => '无审核通过历史',
    ];

    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 获取职位审核详情
     * @return array
     * @throws Exception
     */
    public function run()
    {
        $jobId = Yii::$app->request->get('jobId');
        if (!$jobId) {
            throw new Exception('参数错误');
        }
        $this->jobId = $jobId;

        $this->jobInfo = $this->getJobInfo();
        if (!$this->jobInfo) {
            throw new Exception('当前职位不存在');
        }
        $this->historyStatus = $this->jobInfo['status'] == BaseJob::STATUS_WAIT ? 2 : 1;
        $data                = $this->historyStatus == 2 ? $this->processCurrentJobData() : $this->processHistoryJobData();

        return [
            'data'               => $data,
            'historyStatus'      => $this->historyStatus,
            'historyStatusTitle' => self::HISTORY_STATUS_TITLE[$this->historyStatus],
        ];
    }

    /**
     * 获取职位基本信息
     * @return array|null
     */
    private function getJobInfo()
    {
        return BaseJob::find()
            ->select([
                'id as jobId',
                'status',
                'audit_status as auditStatus',
                'name',
                'job_category_id as jobCategoryId',
                'education_type as educationType',
                'major_id as majorId',
                'nature_type as natureType',
                'experience_type as experienceType',
                'age_type as ageType',
                'political_type as politicalType',
                'abroad_type as abroadType',
                'amount',
                'department as department',
                'district_id as districtId',
                'province_id as provinceId',
                'city_id as cityId',
                'address',
                'duty',
                'requirement',
                'remark',
                'gender_type as genderType',
                'title_type as titleType',
                'announcement_id as announcementId',
                'period_date as periodDate',
                'is_negotiable as isNegotiable',
                'wage_type as wageType',
                'min_wage as minWage',
                'max_wage as maxWage',
                'welfare_tag as welfareTag',
                'apply_type as applyType',
                'apply_address as applyAddress',
                'file_ids as fileIds',
                'delivery_limit_type as deliveryLimitType',
                'delivery_type as deliveryType',
                'delivery_way as deliveryWay',
                'extra_notify_address as extraNotifyAddress',
                'company_id as companyId',
                'is_establishment as isEstablishment',
                'establishment_type as establishmentType',
            ])
            ->where(['id' => $this->jobId])
            ->asArray()
            ->one();
    }

    /**
     * 处理历史职位数据
     * @return array
     */
    private function processHistoryJobData()
    {
        $handleLog = $this->getHandleLog();
        $data      = [
            'announcementTitle' => $this->getAnnouncementTitle(),
            'handleBefore'      => $this->processHandleData($handleLog['handle_before'] ?? ''),
            'handleAfter'       => $this->processHandleData($handleLog['handle_after'] ?? ''),
        ];

        return $data;
    }

    /**
     * 处理当前职位数据
     */
    private function processCurrentJobData()
    {
        $this->detail = $this->jobInfo;
        $this->processBasicInfo();

        return $this->detail;
    }

    /**
     * 获取处理日志
     * @return array|null
     */
    private function getHandleLog()
    {
        return BaseJobHandleLog::find()
            ->where([
                'and',
                ['job_id' => $this->jobId],
                ['handle_type' => BaseJobHandleLog::HANDLE_TYPE_EDIT],
            ])
            ->select([
                'id',
                'handle_before',
                'handle_after',
            ])
            ->limit(1)
            ->orderBy('id desc')
            ->asArray()
            ->one();
    }

    /**
     * 获取公告标题
     * @return string
     */
    private function getAnnouncementTitle()
    {
        return $this->jobInfo['announcementId'] > 0 ? BaseAnnouncement::findOneVal(['id' => $this->jobInfo['announcementId']],
            'title') : '';
    }

    /**
     * 处理处理数据
     * @param string $handleData
     * @return array
     */
    private function processHandleData($handleData)
    {
        $data = json_decode($handleData, true) ?: '';
        if (!empty($data) && isset($data[BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS_KEY])) {
            $data['fileList'] = $this->processFileList($data[BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS_KEY]);
            unset($data[BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS_KEY]);
        }

        return $data;
    }

    /**
     * 处理文件列表
     * @param string $fileIds
     * @return array
     */
    private function processFileList($fileIds)
    {
        if (empty($fileIds)) {
            return [];
        }

        $fileIdsArray = explode(',', $fileIds);
        $fileData     = BaseFile::getIdsList($fileIdsArray);
        $fileArr      = [];

        foreach ($fileData as $value) {
            if (!empty($value['path'])) {
                $fileArr[] = [
                    'path'   => FileHelper::getFullUrl($value['path']),
                    'name'   => $value['name'],
                    'suffix' => FileHelper::getFileSuffixClassName($value['suffix']),
                    'id'     => $value['id'],
                ];
            }
        }

        return $fileArr;
    }
}