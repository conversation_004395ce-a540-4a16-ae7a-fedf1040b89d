<?php
/**
 * create user：shannon
 * create time：2025/5/12 下午5:10
 */
namespace common\service\v2\job;

use common\base\models\BaseJob;
use common\helpers\ArrayHelper;
use common\helpers\UUIDHelper;
use Yii;
use yii\base\Exception;

/**
 * 职位模板列表
 * 基础建设服务类
 * 运营相关：数据管理的列表展示、新增、编辑、删除、审核、上下架等等运营相关性质的操作
 * 注意事项：1、所有提供属性、方法注意访问权限的处理
 *         2、主要业务请注明逻辑注释及方法注释
 *         3、注意代码标准、PHP规范与IDE的规范
 *         4、所有方法、属性、变量尽量简短、易于理解、易于维护
 *         5、所有方法、属性、变量命名尽量采用大小驼峰、数据库出来数据统一使用字段名小驼峰别名
 */
class TemplateListService extends BaseService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 职位模板列表获取
     * @throws \yii\base\Exception
     */
    public function run()
    {
        $params = Yii::$app->request->get();
        if (!$params['companyId']) {
            throw new Exception('没有选择单位');
        }
        $query = BaseJob::find()
            ->select([
                'id',
                'name',
            ])
            ->andWhere([
                'status'     => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_WAIT,
                    BaseJob::STATUS_OFFLINE,
                ],
                'is_show'    => BaseJob::IS_SHOW_YES,
                'company_id' => $params['companyId'],
            ]);

        if ($params['name']) {
            if (is_numeric($params['name'])) {
                if (strlen($params['name']) == 8) {
                    $nameChangeUid = UUIDHelper::decryption($params['name']);
                    $query->andFilterCompare('id', (int)$nameChangeUid);
                } else {
                    $query->andFilterCompare('id', $params['name']);
                }
            } else {
                $query->andFilterCompare('name', $params['name'], 'like');
            }
        }
        $list = $query->limit(20)
            ->asArray()
            ->all();

        return ArrayHelper::arr2KV($list, 'id', 'name');
    }
}