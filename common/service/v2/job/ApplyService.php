<?php
/**
 * admin的站内投递逻辑
 * create user：伍彦川
 * create time：2025/5/14 18:58
 */
namespace common\service\v2\job;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAttachment;
use common\components\MessageException;
use common\helpers\DebugHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use Mpdf\Tag\P;

class ApplyService extends BaseApplyService
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 根据职位id获取投递列表
     * @param $jobIds
     * @param $params
     * @return void
     * @throws MessageException
     */
    public function run($params)
    {
        if (empty($params['jobId']) && empty($params['announcementId'])) {
            throw new MessageException('必传参数错误');
        }

        // 获取站内投递
        $query = $this->buildBaseQuery([
            'jobId'          => $params['jobId'],
            'announcementId' => $params['announcementId'],
            'deliveryType'   => BaseJobApplyRecord::DELIVERY_TYPE_OUTSIDE,
        ], [
            // 职位编号
            'a.job_id',
            'a.resume_id',
            'j.uuid as job_uuid',
            // 人才编号
            'r.uuid as resume_uuid',
            // 职位名称
            'a.job_name',
            // 人才名称
            'a.resume_name',
            // 附带简历文件id
            'a.resume_attachment_id',
            // 投递时间
            'a.add_time',
            // 投递方式
            'jar.delivery_way',
            // 投递进度
            'a.status',
        ]);

        $count        = $query->count();
        $pageSize     = $params['limit'] ?: \Yii::$app->params['defaultPageSize'];
        $pages        = self::setPage($count, $params['page'], $pageSize);
        $jobApplyList = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('a.add_time desc')
            ->asArray()
            ->all();

        DebugHelper::writeLog($query->createCommand()
            ->getRawSql(), '根据职位或者公告获取站内投递sql');

        $result = [
            'list' => $this->formatInStationApplyList($jobApplyList),
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$params['page'],
            ],
        ];

        return $result;
    }

    private function formatInStationApplyList($applyList)
    {
        $list       = [];
        $statusList = BaseJobApply::STATUS_LIST;
        foreach ($applyList as $applyInfo) {
            // 附件名称
            $resumeAttachmentTitle = '';
            if (intval($applyInfo['resume_attachment_id']) > 0) {
                $fileName = BaseResumeAttachment::findOneVal(['id' => $applyInfo['resume_attachment_id']], 'file_name');
                if ($fileName) {
                    $resumeAttachmentTitle = $fileName;
                }
            }

            $list[] = [
                'jobId'                 => $applyInfo['job_id'],
                'jobUuid'               => $applyInfo['job_uuid'],
                'jobName'               => $applyInfo['job_name'],
                'resumeId'              => $applyInfo['resume_id'],
                'resumeUuid'            => $applyInfo['resume_uuid'],
                'resumeName'            => $applyInfo['resume_name'],
                'resumeAttachmentTitle' => $resumeAttachmentTitle,
                'addTime'               => $applyInfo['add_time'],
                'deliveryWay'           => BaseJobApplyRecord::DELIVERY_WAY_NAME[$applyInfo['delivery_way']] ?? '',
                'status'                => $statusList[$applyInfo['status']],
            ];
        }

        return $list;
    }

    /**
     * 根据职位id获取投递人数
     * @param $jobIds
     * @param $params
     * @throws MessageException
     */
    public function runStat($params = [])
    {
        $query         = $this->buildBaseQuery($params);
        $allApplyCount = $query->count();
        $allJobCount   = $query->groupBy('a.job_id')
            ->count();

        return [
            'allApplyCount' => $allApplyCount,
            'allJobCount'   => $allJobCount,
        ];
    }

    /**
     * 获取查询的Query
     * @param $jobIds
     * @param $params
     * @return \yii\db\ActiveQuery
     * @throws MessageException
     */
    private function buildBaseQuery($params = [], $select = ['a.job_id'])
    {
        $where = [
            'and',
        ];

        if (isset($params['jobId']) && $params['jobId'] != '') {
            $where[] = [
                'in',
                'a.job_id',
                StringHelper::changeStrToFilterArr($params['jobId']),
            ];
        }

        if (isset($params['announcementId']) && $params['announcementId'] != '') {
            $jobIds  = BaseAnnouncement::getHasAuditAnnouncementJobIds($params['announcementId']);
            $where[] = [
                'in',
                'a.job_id',
                StringHelper::changeStrToFilterArr($jobIds),
            ];
        }

        // 站内站外投递
        if (isset($params['deliveryType']) && $params['deliveryType'] != '') {
            $where[] = [
                'in',
                'jar.delivery_type',
                StringHelper::changeStrToFilterArr($params['deliveryType']),
            ];
        }

        // 人才姓名/人才编号/职位编号
        if (isset($params['resumeName']) && $params['resumeName'] != '') {
            $where[] = [
                'or',
                [
                    'like',
                    'r.uuid',
                    $params['resumeName'],
                ],
                [
                    'like',
                    'a.resume_name',
                    $params['resumeName'],
                ],
                [
                    'like',
                    'j.uuid',
                    $params['resumeName'],
                ],
            ];
        }

        // 投递时间
        if (!empty($params['addTimeStart']) && !empty($params['addTimeEnd'])) {
            $where[] = [
                '>=',
                'a.add_time',
                TimeHelper::dayToBeginTime($params['addTimeStart']),
            ];

            $where[] = [
                '<=',
                'a.add_time',
                TimeHelper::dayToEndTime($params['addTimeEnd']),
            ];
        }

        // 投递方式
        if (!isset($params['deliveryWay']) && $params['deliveryWay'] != '') {
            $where[] = [
                'in',
                'jar.delivery_way',
                StringHelper::changeStrToFilterArr($params['deliveryWay']),
            ];
        }

        if (count($where) < 1) {
            throw new MessageException('查询条件不能为空');
        }

        return BaseJobApply::find()
            ->select($select)
            ->alias('a')
            ->leftJoin(['jar' => BaseJobApplyRecord::tableName()], 'a.id = jar.apply_id')
            ->innerJoin(['j' => BaseJob::tableName()], 'j.id = a.job_id')
            ->innerJoin(['r' => BaseResume::tableName()], 'r.id = a.resume_id')
            ->where($where);
    }
}