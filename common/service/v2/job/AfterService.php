<?php
/**
 * create user：shannon
 * create time：2025/5/15 上午11:08
 */
namespace common\service\v2\job;

use admin\models\RuleJob;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseJob;
use common\base\models\BaseJobCategoryRelation;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseJobWelfareRelation;
use common\base\models\BaseMajor;
use common\libs\ColumnAuto\JobAutoClassify;
use yii\base\Exception;

class AfterService extends BaseService
{
    public function __construct()
    {
        var_dump($this->jobId);
        parent::__construct();

        var_dump($this->jobId);
    }

}