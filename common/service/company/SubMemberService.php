<?php
/**
 * create user：shannon
 * create time：2025/5/15 上午10:45
 */
namespace common\service\company;

use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseMember;
use common\service\CommonService;
use Yii;
use yii\base\Exception;

class SubMemberService extends CommonService
{
    /**
     * 获取单位写的账号列表
     * @throws Exception
     */
    public function getCompanySubMemberList()
    {
        //获取单位ID
        $companyId = Yii::$app->request->get('companyId');
        $contact   = Yii::$app->request->get('contact');
        $limit     = Yii::$app->request->get('limit');
        if ($companyId <= 0) {
            throw new Exception('单位ID不为空');
        }
        //获取单位下的所有账号
        $query = BaseCompanyMemberInfo::find()
            ->alias('cmi')
            ->leftJoin(['m' => BaseMember::tableName()], 'm.id=cmi.member_id')
            ->andWhere(['cmi.company_id' => $companyId]);
        if ($contact) {
            $query->andWhere([
                'or',
                [
                    'like',
                    'm.email',
                    $contact,
                ],
                [
                    'like',
                    'cmi.contact',
                    $contact,
                ],
                [
                    'like',
                    'cmi.department',
                    $contact,
                ],
            ]);
        }
        if ($limit) {
            $query->limit($limit);
        }

        $list = $query->select([
            'cmi.id',
            'm.email',
            'm.mobile',
            'cmi.contact',
            'cmi.department',
            'cmi.company_member_type',
        ])
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['contact_text'] = $item['contact'];
            if ($item['department']) {
                $item['contact_text'] .= ' /' . $item['department'];
            }
        }

        return $list;
    }
}