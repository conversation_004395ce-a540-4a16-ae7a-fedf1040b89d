<?php

namespace common\service\regionList;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyClickLog;
use common\base\models\BaseCompanyStatData;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyTotal;
use common\base\models\BaseJobTopConfig;
use common\libs\Cache;

/**
 * https://lanhuapp.com/web/#/item/project/product?tid=8d951de4-aefb-40f7-954e-366683d68331&pid=ed399913-689d-49ea-95d8-c416f633cc61&versionId=28fae269-055a-47e2-b626-3993a2e6d333&docId=68a92b05-b8f3-4628-a352-a8cf84604435&docType=axure&pageId=18d03555dd684f3286c089253c05989c&image_id=68a92b05-b8f3-4628-a352-a8cf84604435&parentId=b50bdb87-b00f-431c-b2b6-489807f44543
 */
class SetService extends BaseService
{

    public $areaId;
    public $date;
    public $allCityIdArray = [];

    /**
     * 1、职位榜页面最多展示20条职位；去重展示；
     * 2、职位展示及排序规则：
     *
     * （1）优先展示所选中地区 当前置顶中的职位；最多展示5个置顶职位；并展示“急”标识（排序非前5的置顶职位，不展示“急”标识）；若当日所选中地区置顶职位超过5个，则：
     * ① 若选中地区为城市，则优先展示配置城市置顶的职位，再展示配置省份置顶的职位，最后展示其他置顶职位；置顶条件相同，随机排序。
     *
     * ② 若选中地区为省份，则优先展示配置省份置顶的职位，再展示配置城市置顶的职位，最后展示其他置顶职位；置顶条件相同，随机排序。
     *
     * 置顶职位数据实时更新。
     * （2）其次展示合作单位近15天发布（指职位的首次发布时间；不包含当前日）的、工作地点（或所属省份）为当前地区的在线职位；按投递量（该职位所有端口投递数据；含网址投递）倒序展示；投递量相同的职位，按职位详情页浏览量（全端口数据）倒序排，页面浏览量也相同的情况下，随机排。
     * （3）最后展示非合作单位近15天发布的、工作地点（或所属省份）为当前地区的在线职位，按投递量（该职位所有端口数据；含网址投递）倒序展示；投递量相同的职位，按职位详情页（全端口数据）浏览量倒序排，页面浏览量也相同的情况下，随机排。
     * 3、若职位配置“编制”/“平台投递”，职位卡片正常展示“编”/“反馈快“标签；
     *
     * 点击职位卡片，打开该职位的职位详情页面
     */
    public function job($areaId)
    {
        // 近15天,不包含当前天
        $dateBegin    = date('Y-m-d', strtotime("-" . self::LAST_DAY . " day"));
        $dateEnd      = CUR_DATE;
        $this->type   = self::TYPE_JOB;
        $this->areaId = $areaId;
        $this->setAllCity();

        // 先找到当前地区的信息(需要区别一下省份和城市
        $area = BaseArea::findOne($areaId);
        // 如果是城市就比较简单,不需要找省份,但是如果是省份,就需要找城市
        if ($area->level == 1) {
            // 找到所有的城市
            $orderSelect = ['s' => 'IFNULL(a.level,3)'];
            $oderBy      = 's asc';
        } else {
            // 找到所有的省份
            $orderSelect = ['s' => 'IFNULL(a.level,0)'];
            $oderBy      = 's desc';
        }

        // 到置顶表里面找当前置顶的职位和职位关联来找对应的职位
        $topList = BaseJobTopConfig::find()
            ->select('t.*')
            ->addSelect($orderSelect)
            ->alias('t')
            ->innerJoin(['j' => BaseJob::tableName()], 'j.id = t.job_id')
            ->leftJoin(['a' => BaseArea::tableName()], 'a.id = t.area_id')
            ->where([
                'j.city_id' => $this->allCityIdArray,
                'j.status'  => BaseJob::STATUS_ONLINE,
                't.status'  => BaseJobTopConfig::STATUS_TOP_ING,
            ])
            ->orderBy($oderBy)
            ->asArray()
            ->all();

        $companyTotal = [];
        // 接下来就是排序了,但是就是需要找出五个非重复的就可以了
        $topFiveList = [];
        foreach ($topList as $key => $value) {
            if (count($topFiveList) >= 5) {
                break;
            }
            if (isset($companyTotal[$value['company_id']]) && $companyTotal[$value['company_id']] >= 2) {
                continue;
            }
            if (count($topFiveList) >= self::LIST_MAX_NUM) {
                break;
            }
            // 如果找到的置顶职位不在置顶表里面,就可以加入到置顶表里面
            if (!in_array($value['job_id'], $topFiveList)) {
                $topFiveList[] = $value['job_id'];
            } else {
                continue;
            }
            if (isset($companyTotal[$value['company_id']])) {
                $companyTotal[$value['company_id']]++;
            } else {
                $companyTotal[$value['company_id']] = 1;
            }
        }

        // 接下来就是找其余职位的了
        $surplusAmount     = self::LIST_MAX_NUM - count($topFiveList);
        $cooperationList   = BaseJob::find()
            ->alias('j')
            ->select('j.id,j.company_id')
            ->innerJoin(['c' => BaseCompany::tableName()], 'c.id = j.company_id')
            ->leftJoin(['a' => BaseJobApplyTotal::tableName()], 'a.job_id = j.id')
            ->where([
                'j.city_id'        => $this->allCityIdArray,
                'j.status'         => BaseJob::STATUS_ONLINE,
                'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
            ])
            ->andWhere([
                'not in',
                'j.id',
                $topFiveList,
            ])
            ->andWhere([
                '>=',
                'j.first_release_time',
                $dateBegin,
            ])
            ->andWhere([
                '<',
                'j.first_release_time',
                $dateEnd,
            ])
            ->orderBy('a.total desc,j.click desc')
            ->limit($surplusAmount * 10)
            ->asArray()
            ->all();
        $cooperationJobIds = [];
        foreach ($cooperationList as $value) {
            if (isset($companyTotal[$value['company_id']]) && $companyTotal[$value['company_id']] >= 2) {
                continue;
            }
            if (count($cooperationJobIds) >= $surplusAmount) {
                break;
            }
            $cooperationJobIds[] = $value['id'];
            if (isset($companyTotal[$value['company_id']])) {
                $companyTotal[$value['company_id']]++;
            } else {
                $companyTotal[$value['company_id']] = 1;
            }
        }
        $list = array_merge($topFiveList, $cooperationJobIds);
        // 如果还是不够20,就找非合作单位
        if (count($list) < self::LIST_MAX_NUM) {
            $unSurplusAmount     = self::LIST_MAX_NUM - count($list);
            $unCooperationList   = BaseJob::find()
                ->alias('j')
                ->select('j.id,j.company_id')
                ->innerJoin(['c' => BaseCompany::tableName()], 'c.id = j.company_id')
                ->leftJoin(['a' => BaseJobApplyTotal::tableName()], 'a.job_id = j.id')
                ->where([
                    'j.city_id'        => $this->allCityIdArray,
                    'j.status'         => BaseJob::STATUS_ONLINE,
                    'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_NO,
                ])
                ->andWhere([
                    'not in',
                    'j.id',
                    $list,
                ])
                ->andWhere([
                    '>=',
                    'j.first_release_time',
                    $dateBegin,
                ])
                ->andWhere([
                    '<',
                    'j.first_release_time',
                    $dateEnd,
                ])
                ->orderBy('a.total desc,j.click desc')
                ->limit($unSurplusAmount * 5)
                ->asArray()
                ->all();
            $unCooperationJobIds = [];
            foreach ($unCooperationList as $value) {
                if (isset($companyTotal[$value['company_id']]) && $companyTotal[$value['company_id']] >= 2) {
                    continue;
                }
                if (count($unCooperationJobIds) >= $unSurplusAmount) {
                    break;
                }
                $unCooperationJobIds[] = $value['id'];
                if (isset($companyTotal[$value['company_id']])) {
                    $companyTotal[$value['company_id']]++;
                } else {
                    $companyTotal[$value['company_id']] = 1;
                }
            }

            $list = array_merge($list, $unCooperationJobIds);
        }

        // 最后丢到缓存里面
        $key = $this->getCacheKey();
        Cache::set($key, json_encode($list));

        return $list;
    }

    /**
     * 1、最多展示20条公告；
     * 2、公告展示及排序规则：
     *
     * （1）优先展示近15天发布的（指公告的首次发布时间；不包含含当前日）所选地区公告属性标记“首页置顶”/“栏目置顶”属性的公告；按公告发布时间倒序展示，最多取5条公告；发布时间相同的公告，则随机取。
     * （2）其次展示合作单位近15天发布的（指公告的首次发布时间；不包含当前日）、工作地点（或所属省份）包含当前地区的在线公告；按公告浏览量倒序（全端口数据）展示；公告浏览量相同，随机排；
     * （3）最后展示非合作单位近15天发布的（指公告的首次发布时间；不包含当前日）、工作地点（或所属省份）包含当前地区的在线公告；按公告浏览量倒序（全端口数据）展示；公告浏览量相同，随机排；
     * 3、点击公告卡片，打开该公告的详情页面；
     */
    public function announcement($areaId)
    {
        // 近15天,不包含当前天
        $dateBegin    = date('Y-m-d', strtotime("-" . self::LAST_DAY . " day"));
        $dateEnd      = CUR_DATE;
        $this->areaId = $areaId;
        $this->type   = self::TYPE_ANNOUNCEMENT;
        $this->setAllCity();

        $list            = BaseAnnouncement::find()
            ->select('a.id,a.company_id')
            ->alias('a')
            ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id = b.id')
            ->innerJoin(['c' => BaseArticleAttribute::tableName()], 'c.article_id = b.id')
            ->innerJoin(['d' => BaseJob::tableName()], 'd.announcement_id = a.id')
            ->where([
                'd.city_id'   => $this->allCityIdArray,
                'a.status'    => BaseAnnouncement::STATUS_ONLINE,
                'd.status'    => BaseJob::STATUS_ONLINE,
                'c.type'      => self::ATT_ANNOUNCEMENT_LIST,
                'b.is_delete' => BaseArticle::IS_DELETE_NO,
                'b.status'    => BaseJob::STATUS_ONLINE,
            ])
            ->andWhere([
                '>=',
                'b.first_release_time',
                $dateBegin,
            ])
            ->andWhere([
                '<',
                'b.first_release_time',
                $dateEnd,
            ])
            ->groupBy('a.id')
            ->asArray()
            ->all();
        $companyTotal    = [];
        $announcementIds = [];
        foreach ($list as $value) {
            if (isset($companyTotal[$value['company_id']]) && $companyTotal[$value['company_id']] >= 2) {
                continue;
            }
            if (count($announcementIds) >= self::LIST_MAX_NUM) {
                break;
            }
            $announcementIds[] = $value['id'];
            if (isset($companyTotal[$value['company_id']])) {
                $companyTotal[$value['company_id']]++;
            } else {
                $companyTotal[$value['company_id']] = 1;
            }
        }
        if (count($announcementIds) < self::LIST_MAX_NUM) {
            $unSurplusAmount = self::LIST_MAX_NUM - count($announcementIds);
            $unList          = BaseAnnouncement::find()
                ->select('a.id,a.company_id')
                ->alias('a')
                ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id = b.id')
                ->innerJoin(['d' => BaseJob::tableName()], 'd.announcement_id = a.id')
                ->innerJoin(['e' => BaseCompany::tableName()], 'e.id = d.company_id')
                ->where([
                    'd.city_id'        => $this->allCityIdArray,
                    'a.status'         => BaseAnnouncement::STATUS_ONLINE,
                    'd.status'         => BaseJob::STATUS_ONLINE,
                    'b.is_delete'      => BaseArticle::IS_DELETE_NO,
                    'e.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
                ])
                ->andWhere([
                    'not in',
                    'a.id',
                    $list,
                ])
                ->andWhere([
                    '>=',
                    'b.first_release_time',
                    $dateBegin,
                ])
                ->andWhere([
                    '<',
                    'b.first_release_time',
                    $dateEnd,
                ])
                ->groupBy('a.id')
                ->orderBy('b.click desc')
                ->limit($unSurplusAmount * 10)
                ->asArray()
                ->all();
            foreach ($unList as $value) {
                if (isset($companyTotal[$value['company_id']]) && $companyTotal[$value['company_id']] >= 2) {
                    continue;
                }
                if (count($announcementIds) >= self::LIST_MAX_NUM) {
                    break;
                }
                $announcementIds[] = $value['id'];
                if (isset($companyTotal[$value['company_id']])) {
                    $companyTotal[$value['company_id']]++;
                } else {
                    $companyTotal[$value['company_id']] = 1;
                }
            }
        }

        if (count($announcementIds) < self::LIST_MAX_NUM) {
            $surplusAmount = self::LIST_MAX_NUM - count($announcementIds);
            $surplusList   = BaseAnnouncement::find()
                ->select('a.id,a.company_id')
                ->alias('a')
                ->innerJoin(['b' => BaseArticle::tableName()], 'a.article_id = b.id')
                ->innerJoin(['d' => BaseJob::tableName()], 'd.announcement_id = a.id')
                ->innerJoin(['e' => BaseCompany::tableName()], 'e.id = d.company_id')
                ->where([
                    'd.city_id'        => $this->allCityIdArray,
                    'a.status'         => BaseAnnouncement::STATUS_ONLINE,
                    'd.status'         => BaseJob::STATUS_ONLINE,
                    'b.is_delete'      => BaseArticle::IS_DELETE_NO,
                    'e.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_NO,
                ])
                ->andWhere([
                    'not in',
                    'a.id',
                    $list,
                ])
                ->andWhere([
                    '>=',
                    'b.first_release_time',
                    $dateBegin,
                ])
                ->andWhere([
                    '<',
                    'b.first_release_time',
                    $dateEnd,
                ])
                ->groupBy('a.id')
                ->orderBy('b.click desc')
                ->limit($surplusAmount * 10)
                ->asArray()
                ->all();

            foreach ($surplusList as $value) {
                if (isset($companyTotal[$value['company_id']]) && $companyTotal[$value['company_id']] >= 2) {
                    continue;
                }
                if (count($announcementIds) >= self::LIST_MAX_NUM) {
                    break;
                }
                $announcementIds[] = $value['id'];
                if (isset($companyTotal[$value['company_id']])) {
                    $companyTotal[$value['company_id']]++;
                } else {
                    $companyTotal[$value['company_id']] = 1;
                }
            }
        }
        // 最后丢到缓存里面
        $key = $this->getCacheKey();
        Cache::set($key, json_encode($announcementIds));

        return $announcementIds;
    }

    /**
     * 热门单位榜：
     * 1、最多展示20家单位；
     * 2、展示规则：
     * （1）优先展示所选地区合作单位信息；按单位主页全端口近15日（不包含当前日）浏览量倒序排；
     * (2)  其次展示所选地区非合作单位信息；按单位主页全端口近15日（不包含当前日）浏览量倒序排；
     * 3、点击单位卡片，打开该单位详情页面；
     * 4、可操作“关注”/“取消关注”；操作成功toast提示3s：“关注成功”/“取消关注成功”
     */
    public function company($areaId)
    {
        $this->type   = self::TYPE_COMPANY;
        $this->areaId = $areaId;
        $this->setAllCity();

        $list = BaseCompanyClickLog::find()
            ->alias('a')
            ->innerJoin(['b' => BaseCompany::tableName()], 'a.company_id = b.id')
            ->innerJoin(['c' => BaseCompanyStatData::tableName()], 'c.company_id = b.id')
            ->select('b.id,count(*) as total')
            ->where([
                'city_id'          => $this->allCityIdArray,
                'b.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
            ])
            ->andWhere([
                '>=',
                'a.add_time',
                date('Y-m-d', strtotime("-" . 300 . " day")),
            ])
            ->andWhere([
                '<',
                'a.add_time',
                CUR_DATE,
            ])
            ->groupBy('b.id')
            ->orderBy('total desc,heat desc')
            ->limit(self::LIST_MAX_NUM)
            ->asArray()
            ->column();

        if (count($list) < self::LIST_MAX_NUM) {
            $surplusAmount = self::LIST_MAX_NUM - count($list);
            $list          = array_merge($list, BaseCompanyClickLog::find()
                ->alias('a')
                ->innerJoin(['b' => BaseCompany::tableName()], 'a.company_id = b.id')
                ->innerJoin(['c' => BaseCompanyStatData::tableName()], 'c.company_id = b.id')
                ->select('b.id,count(*) as total')
                ->where([
                    'city_id'          => $this->allCityIdArray,
                    'b.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_NO,
                ])
                ->andWhere([
                    '>=',
                    'a.add_time',
                    date('Y-m-d', strtotime("-" . 300 . " day")),
                ])
                ->andWhere([
                    '<',
                    'a.add_time',
                    CUR_DATE,
                ])
                ->groupBy('b.id')
                ->orderBy('total desc,heat desc')
                ->limit($surplusAmount)
                ->asArray()
                ->column());
        }

        // 最后丢到缓存里面
        $key = $this->getCacheKey();
        Cache::set($key, json_encode($list));

        return $list;
    }

    public function setAllCity()
    {
        $areaId = $this->areaId;
        $area   = BaseArea::findOne($areaId);
        // 如果是城市就比较简单,不需要找省份,但是如果是省份,就需要找城市
        if ($area->level == 1) {
            // 找到所有的城市
            $areaArray = BaseArea::find()
                ->select('id')
                ->where([
                    'parent_id' => $areaId,
                ])
                ->asArray()
                ->column();
        } else {
            // 找到所有的省份
            $areaArray = [$area->id];
        }

        $this->allCityIdArray = $areaArray;
    }

}