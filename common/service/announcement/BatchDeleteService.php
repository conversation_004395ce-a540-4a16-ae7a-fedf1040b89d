<?php

namespace common\service\announcement;

use common\base\models\BaseAdmin;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseArticle;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseUser;
use common\helpers\IpHelper;
use yii\base\Exception;

class BatchDeleteService extends BaseService
{
    /**
     * 执行删除
     */
    public function run()
    {
        $this->actionType = self::ACTION_TYPE_BATCH_DELETE;
        $this->beforeRun();
        $this->delete();
        $this->afterRun();
    }

    /**
     * 删除
     * @throws Exception
     */
    public function delete()
    {
        foreach ($this->jobList as $item) {
            $jobApply = BaseJobApply::findOne(['job_id' => $item['id']]);
            if ($jobApply) {
                return true;
            }
        }
        $this->deleteRun();
    }

    /**
     * 设置好要处理的数据
     * @param $data
     * @return $this
     * @throws Exception
     */
    public function setData($params): BatchDeleteService
    {
        $data = BaseAnnouncement::find()
            ->select('id,article_id')
            ->where(['id' => $params['id']])
            ->asArray()
            ->one();

        if (!$data) {
            throw new Exception('非法请求');
        }

        $this->articleId      = BaseArticle::findOneVal(['id' => $data['article_id']], 'id');
        $this->announcementId = $data['id'];
        $this->jobList        = BaseJob::find()
            ->select('id,status')
            ->where(['announcement_id' => $data['id']])
            ->asArray()
            ->all();

        foreach ($this->jobList as $item) {
            $jobIds[]       = $item['id'];
            $statusBefore[] = $item['status'];
        }
        $this->jobIds       = $jobIds;
        $this->statusBefore = $statusBefore;

        $this->jobModel = BaseJob::find()
            ->select('status')
            ->where([
                'in',
                'id',
                $this->jobIds,
            ]);

        return $this;
    }

    /**
     * 删除公告
     * @throws Exception
     */
    private function deleteRun()
    {
        $this->announcementModel = BaseAnnouncement::findOne(['id' => $this->announcementId]);
        $articleModel            = BaseArticle::findOne(['id' => $this->articleId]);

        $articleModel->is_delete = BaseArticle::STATUS_ACTIVE;
        if (!$articleModel->save()) {
            throw new Exception($articleModel->getFirstErrorsMessage());
        }
        // 删除公告的同时一并删除公告下的所有职位
        foreach ($this->jobList as $item) {
            $this->jobModel         = BaseJob::findOne(['id' => $item['id']]);
            $this->jobModel->status = BaseJob::STATUS_DELETE;
            if (!$this->jobModel->save()) {
                throw new Exception('职位删除失败');
            }
        }

        $this->operationRecord = '删除公告id:' . $this->announcementId . '，职位id:' . explode(',', $this->jobIds);

    }

    /**
     * 公告删除操作日志
     * @param
     * @throws Exception
     * @throws \yii\base\NotSupportedException
     */
    protected function batchDeleteLog()
    {
        //操作动作入表
        $handleBefore = [
            'operation_record' => '-',
        ];

        $handleAfter  = [
            'operation_record' => $this->operationRecord,
        ];
        $handleLogArr = [
            'add_time'        => CUR_DATETIME,
            'announcement_id' => $this->announcementId,
            'handle_type'     => (string)BaseAnnouncementHandleLog::HANDLE_TYPE_DELETE,
            'handler_type'    => $this->operatorType,
            'handler_id'      => $this->operatorId,
            'handler_name'    => $this->operatorUserName ?: '',
            'handle_before'   => json_encode($handleBefore),
            'handle_after'    => json_encode($handleAfter),
            'ip'              => IpHelper::getIpInt(),
        ];
        BaseAnnouncementHandleLog::createInfo($handleLogArr);
    }

}
