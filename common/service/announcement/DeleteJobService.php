<?php

namespace common\service\announcement;

use common\base\models\BaseAdmin;
use common\base\models\BaseArticle;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseJobTemp;
use common\helpers\IpHelper;
use common\helpers\TimeHelper;
use common\libs\ColumnAuto\AnnouncementAutoClassify;
use yii\base\Exception;

class DeleteJobService extends BaseService
{
    public $isTemp = 0;

    /**
     * 执行删除
     */
    public function run()
    {
        switch ($this->actionType) {
            case self::ACTION_TYPE_DELETE_JOB:
                $this->beforeRun();
                $this->delete();
                break;
            case self::ACTION_TYPE_REDUCTION_JOB:
                $this->beforeRun();
                $this->reduction();
                break;
            default:
                throw new Exception('操作类型错误');
        }

        $this->afterRun();
    }

    /**
     * 设置删除
     * @return $this
     */
    public function setDelete(): DeleteJobService
    {
        $this->actionType = self::ACTION_TYPE_DELETE_JOB;

        return $this;
    }

    /**
     * 设置还原
     * @return $this
     */
    public function setReduction(): DeleteJobService
    {
        $this->actionType = self::ACTION_TYPE_REDUCTION_JOB;

        return $this;
    }

    /**
     * 删除
     * @throws Exception
     */
    private function delete()
    {
        $this->deleteRun();
    }

    /**
     * 还原
     * @throws Exception
     */
    private function reduction()
    {
        $this->reductionRun();
    }

    /**
     * 设置好要处理的数据
     * @param $data
     * @return $this
     * @throws Exception
     */
    public function setData($params): DeleteJobService
    {
        if ($params['isTemp'] == BaseJobTemp::IS_TEMP_YES) {
            $jobModel = BaseJobTemp::findOne([
                'id'      => $params['id'],
                'is_temp' => BaseJobTemp::IS_TEMP_YES,
            ]);
        } else {
            $jobModel = BaseJob::findOne(['id' => $params['id']]);
        }

        if (!$jobModel) {
            throw new Exception('非法请求');
        }

        $this->setAnnouncement($jobModel->announcement_id);

        $this->jobTempId = $jobModel->id;
        $this->isTemp    = $params['isTemp'];

        return $this;
    }

    /**
     * 删除公告职位
     */
    private function deleteRun()
    {
        if ($this->isTemp == BaseJobTemp::IS_TEMP_YES) {
            BaseJobTemp::findOne([
                'id'      => $this->jobTempId,
                'is_temp' => $this->isTemp,
            ])
                ->delete();
        } else {
            $model    = BaseJob::findOne(['id' => $this->jobTempId]);
            $jobApply = BaseJobApply::findOne(['job_id' => $this->jobTempId]);
            if ($jobApply) {
                throw new Exception('该职位存在投递数据，不可删除');
            }
            // 公告剩余最后一个在线职位不可删除
            if ($this->articleModel->status == BaseArticle::STATUS_ONLINE) {
                // 统计在线的公告职位
                $oldJobCount = BaseJob::find()
                    ->where([
                        'announcement_id' => $this->announcementId,
                        'is_show'         => BaseJob::IS_SHOW_YES,
                        'status'          => BaseJob::STATUS_ONLINE,
                    ])
                    ->count();
                if ($oldJobCount < 2) {
                    throw new Exception('公告剩余最后一个在线职位，不支持删除');
                }
            }

            $model->status = BaseJob::STATUS_DELETE;
            if (!$model->save()) {
                throw new Exception($model->getFirstErrorsMessage());
            }

            $this->operationRecord = '删除职位id:' . $this->jobTempId;
        }
    }

    /**
     * 设置还原的数据
     * @param $params
     * @return $this
     * @throws Exception
     */
    public function setReductionData($params): DeleteJobService
    {
        $jobModel = BaseJob::findOne(['id' => $params['id']]);
        if (!$jobModel) {
            throw new Exception('非法请求');
        }

        $this->setAnnouncement($jobModel['announcement_id']);
        if ($this->articleModel->is_delete == BaseArticle::STATUS_ACTIVE) {
            throw new Exception('该职位id:' . $jobModel->id . '的公告已被删除，不支持还原');
        }

        $this->jobId    = $jobModel->id;
        $this->jobModel = $jobModel;

        return $this;
    }

    /**
     * 还原公告职位
     */
    private function reductionRun()
    {
        $jobModel     = $this->jobModel;
        $articleModel = $this->articleModel;

        if ($articleModel['status'] == BaseArticle::STATUS_ONLINE) {//在线的公告
            switch ($jobModel['audit_status']) {
                // 审核拒绝的职位
                case BaseJob::STATUS_REFUSE_AUDIT:
                    // 有审核通过历史
                    if ($jobModel['first_release_time'] != TimeHelper::ZERO_TIME) {
                        $jobModel->audit_status = BaseJob::AUDIT_STATUS_REFUSE_AUDIT;
                    } else {
                        $jobModel->status = BaseJob::STATUS_OFFLINE;
                    }
                    break;
                // 审核通过的职位
                case BaseJob::STATUS_PASS_AUDIT:
                    // 审核通过的职位
                    $jobModel->status = BaseJob::STATUS_OFFLINE;
                    break;
            }
        } elseif ($articleModel['status'] == BaseArticle::STATUS_STAGING) {//待发布的公告
            switch ($jobModel['audit_status']) {
                // 编辑中的职位
                case BaseJob::AUDIT_STATUS_WAIT:
                    $jobModel->status       = BaseJob::STATUS_WAIT;
                    $jobModel->audit_status = BaseJob::AUDIT_STATUS_WAIT;
                    break;
                // 待审核的职位
                case BaseJob::STATUS_WAIT_AUDIT:
                    // 审核拒绝的职位
                case BaseJob::STATUS_REFUSE_AUDIT:
                    $jobModel->status       = BaseJob::STATUS_WAIT;
                    $jobModel->audit_status = BaseJob::STATUS_REFUSE_AUDIT;
                    break;
            }
        } elseif ($articleModel['status'] == BaseArticle::STATUS_OFFLINE) {//下线的公告
            // 有审核通过历史
            if ($jobModel['first_release_time'] != TimeHelper::ZERO_TIME) {
                $jobModel->status = BaseJob::STATUS_OFFLINE;
            } else {
                $jobModel->audit_status = BaseJob::AUDIT_STATUS_REFUSE_AUDIT;
            }
        } else {
            throw new Exception('不支持还原操作');
        }

        if (!$jobModel->save()) {
            throw new Exception($jobModel->getFirstErrorsMessage());
        }

        $this->operationRecord = '还原职位id:' . $this->jobId;
        // 自动归属公告栏目规则
        $this->autoClassifyRun();
    }

    /**
     * 操作日志
     * @param
     * @throws Exception
     * @throws \yii\base\NotSupportedException
     */
    protected function deleteJobLog()
    {
        //操作动作入表
        $handleBefore = [
            'job_id'           => $this->jobTempId,
            'operation_record' => '-',
        ];

        $handleAfter  = [
            'job_id'           => $this->jobTempId,
            'operation_record' => $this->operationRecord,
        ];
        $handleLogArr = [
            'add_time'        => CUR_DATETIME,
            'announcement_id' => $this->announcementId,
            'job_id'          => $this->jobTempId,
            'handle_type'     => (string)BaseJobHandleLog::HANDLE_TYPE_DELETE,
            'handler_type'    => $this->operatorType,
            'handler_id'      => $this->operatorId,
            'handler_name'    => $this->operatorUserName ?: '',
            'handle_before'   => json_encode($handleBefore),
            'handle_after'    => json_encode($handleAfter),
            'ip'              => IpHelper::getIpInt(),
        ];
        BaseJobHandleLog::createInfo($handleLogArr);
    }

}
