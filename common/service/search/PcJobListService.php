<?php

namespace common\service\search;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyGroupScoreSystem;
use common\base\models\BaseDictionary;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobCollect;
use common\base\models\BaseJobKeywordsPreprocess;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseJobTopConfig;
use common\base\models\BaseJobWelfareRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseResume;
use common\base\models\BaseTrade;
use common\base\models\BaseWelfareLabel;
use common\components\MessageException;
use common\helpers\ArrayHelper;
use common\helpers\DebugHelper;
use common\helpers\IpHelper;
use common\helpers\TimeHelper;
use common\libs\Cache;
use common\service\job\JobService;
use common\service\jobTop\DailyServer;
use common\service\jobTop\JobTopApplication;
use common\service\meilisearch\job\SearchService;
use Yii;
use yii\base\Exception;
use yii\db\Expression;

class PcJobListService extends BaseService
{
    private $isNeedOrder   = true; // 是否需要排序
    private $isOnlyJobList = false; // 只需返回列表数据，无需其余修饰操作

    private $checkOnlyKeyword = false; // 查询是否只有关键字

    private $originParams;
    private $key;

    private $jobList;
    private $sort;
    private $total;
    private $limit;
    private $offset;
    private $allTopIdList;
    private $publicSelectArr = [];
    private $absoluteTopList = [];
    private $otherTopList    = [];

    const             CACHE_TIME                = 300;
    const             DEFAULT_PAGE_SIZE         = 20;
    const             NO_PARAMS_CACHE_PAGE      = 40;
    const             NO_PARAMS_CACHE_PAGE_SIZE = self::DEFAULT_PAGE_SIZE * self::NO_PARAMS_CACHE_PAGE;
    const             NO_PARAMS_CACHE_KEY       = Cache::ALL_JOB_LIST_NO_PARAMS_LIST_KEY;
    const             NO_PARAMS_MINI_CACHE_KEY  = Cache::ALL_JOB_LIST_MINI_NO_PARAMS_LIST_KEY;
    const             DEFAULT_PAGE_TOTAL        = 400;
    // const             TEST_COMPANY_ID           = Yii::$app->params['testCompanyId'];

    // public $testCompanyId;

    // 用于临时存放一些字典的名字,不用每次都去查
    private $tmpDictionaryNameList = [];

    const PARAMS_KEY_LIST = [
        'page'             => [],
        'wageId'           => [],
        'majorId'          => [],
        'keyword'          => [],
        'keyword_b'        => [],
        'areaId'           => [],
        'companyNature'    => [],
        'companyType'      => [],
        'educationType'    => [],
        'natureType'       => [],
        'companyScaleType' => [],
        'industryId'       => [],
        'companyId'        => [],
        'jobCategoryId'    => [],
        'titleType'        => [],
        'jobType'          => [],
        'welfareLabelId'   => [],
        'experienceType'   => [],
        'releaseTimeType'  => [],
        'isEstablishment'  => '',
        'applyHeat'        => '',
        // 这个属性的前置判断需要在控制器里面,这里不做权限的判断
        'isFast'           => [],
        'isFresh'          => [],
        'isCooperation'    => [],
        'isMini'           => [],
        'pageSize'         => [],
        'sort'             => [],
    ];

    public function run($params, $type)
    {
        $this->type = $type;

        unset($params['page']);
        if ($params['p']) {
            $params['page'] = $params['p'];
        }
        // 首先去掉page,小程序不用
        if ($this->type == self::TYPE_MINI_JOB_LIST) {
            $params = $this->groupParamsToRealParams($params);
        }

        // page 最多到40
        if ($params['page'] > self::NO_PARAMS_CACHE_PAGE) {
            $params['page'] = self::NO_PARAMS_CACHE_PAGE;
        }
        
        $this->originParams = $params;

        $this->filter();
        $this->setKey();
        $this->check();

        // 测试数据库性能
        BaseJob::openDb2();
        $isDB2 = true;
        $this->getData();

        if ($isDB2) {
            BaseJob::closeDb2();
        }

        $this->afterRun();

        return [
            'list'     => $this->jobList,
            'totalNum' => $this->total,
        ];
    }

    // 里面的参数全部都是 key_value,现在需要把key相同的放在一起,value用逗号隔开
    private function groupParamsToRealParams($params)
    {
        $configKey = [
            'wageId',
            'majorId',
            'keyword',
            'areaId',
            'companyNature',
            'companyType',
            'educationType',
            'natureType',
            'companyScaleType',
            'industryId',
            'companyId',
            'jobCategoryId',
            'titleType',
            'jobType',
            'welfareLabelId',
            'experienceType',
            'releaseTimeType',
            'isFast',
            'isFresh',
            'isCooperation',
            'isEstablishment',
            'applyHeat',
        ];

        return ArrayHelper::groupParamsToRealParams($params, $configKey);
    }

    /**
     * 这个方法是用于保存一下职位列表的无参数缓存(暂时考虑缓存40页,定时更新一下,这样可以避免用户在单纯翻页的时候也需要实时获取)
     */
    public function setNoParamsListCache()
    {
        $this->pageSize = self::NO_PARAMS_CACHE_PAGE_SIZE;
        // sort保留两种就好了
        $this->sort               = 'new';
        $this->params['pageSize'] = $this->pageSize;
        $this->params['page']     = 1;
        $key                      = self::NO_PARAMS_CACHE_KEY . ':' . $this->sort;
        $list                     = $this->search();

        // 20为一个page
        for ($i = 0; $i < self::NO_PARAMS_CACHE_PAGE; $i++) {
            $pageList = array_slice($list, $i * self::DEFAULT_PAGE_SIZE, self::DEFAULT_PAGE_SIZE);
            $page     = $i + 1;
            $total    = count($list);
            $data     = [
                'list'  => $pageList,
                'total' => $total,
            ];
            $cacheKey = $key . ':' . $page;
            Cache::set($cacheKey, json_encode($data));
        }

        $this->sort = 'default';
        $key        = self::NO_PARAMS_CACHE_KEY . ':' . $this->sort;

        $list = $this->search();

        // 20为一个page
        for ($i = 0; $i < self::NO_PARAMS_CACHE_PAGE; $i++) {
            $pageList = array_slice($list, $i * self::DEFAULT_PAGE_SIZE, self::DEFAULT_PAGE_SIZE);
            $page     = $i + 1;
            $total    = count($list);
            $data     = [
                'list'  => $pageList,
                'total' => $total,
            ];
            Cache::set($key . ':' . $page, json_encode($data));
        }
    }

    public function setNoParamsMiniListCache()
    {
        $this->pageSize = self::NO_PARAMS_CACHE_PAGE_SIZE;
        $this->type     = self::TYPE_MINI_JOB_LIST;
        // sort保留两种就好了
        $this->sort               = 'new';
        $this->params['pageSize'] = $this->pageSize;
        $this->params['page']     = 1;
        $key                      = self::NO_PARAMS_MINI_CACHE_KEY . ':' . $this->sort;
        $list                     = $this->search();

        // 20为一个page
        for ($i = 0; $i < self::NO_PARAMS_CACHE_PAGE; $i++) {
            $pageList = array_slice($list, $i * self::DEFAULT_PAGE_SIZE, self::DEFAULT_PAGE_SIZE);
            $page     = $i + 1;
            $total    = count($list);
            $data     = [
                'list'  => $pageList,
                'total' => $total,
            ];
            $cacheKey = $key . ':' . $page;
            Cache::set($cacheKey, json_encode($data));
        }

        $this->sort = 'default';
        $key        = self::NO_PARAMS_CACHE_KEY . ':' . $this->sort;

        $list = $this->search();

        // 20为一个page
        for ($i = 0; $i < self::NO_PARAMS_CACHE_PAGE; $i++) {
            $pageList = array_slice($list, $i * self::DEFAULT_PAGE_SIZE, self::DEFAULT_PAGE_SIZE);
            $page     = $i + 1;
            $total    = count($list);
            $data     = [
                'list'  => $pageList,
                'total' => $total,
            ];
            Cache::set($key . ':' . $page, json_encode($data));
        }
    }

    private function setKey()
    {
        $params = $this->params;
        if (count($params) > 0) {
            ksort($params);
            $stringKey = ArrayHelper::arrayToStringKey($params);
            $this->key = $stringKey;
        } else {
            $this->key = '0';
        }
    }

    /**
     * 其实我们做了两套缓存方案,一套是用于无参数的,一套是用于有参数的
     * 无参数的,我们只保留了800条数据,所以,如果用户在翻页的时候,我们就不需要去查数据库了,直接从缓存中取就好了,但是这个页数应该是只有前40页
     */
    private function getCacheData()
    {
        $params = $this->params;
        $sort   = $params['sort'];
        $isMini = $params['isMini'];

        if ($isMini) {
            // 小程序需要有小程序的处理
            if ($params['page']) {
                $page = $params['page'];
                unset($params['page']);
                unset($params['pageSize']);
            }

            unset($params['isMini']);
            unset($params['sort']);

            if (count($params) <= 0) {
                // 有参数的话,就不用缓存了,看看page
                if ($page <= self::NO_PARAMS_CACHE_PAGE) {
                    // 拿缓存,
                    $key      = self::NO_PARAMS_MINI_CACHE_KEY . ':' . $sort . ':' . $page;
                    $dataJson = Cache::get($key);
                    $data     = json_decode($dataJson, true);
                    $allList  = $data['list'];
                    $total    = $data['total'];

                    return [
                        'list'  => $allList,
                        'total' => $total,
                    ];
                }

                return null;
            }
        } else {
            if ($params['page'] || $params['p']) {
                $page = $params['page'] ?: $params['p'];
                unset($params['page']);
                unset($params['pageSize']);
                unset($params['sort']);
                // 这个时候看看params里面还有没有参数
                if (count($params) <= 0) {
                    // 有参数的话,就不用缓存了,看看page
                    if ($page <= self::NO_PARAMS_CACHE_PAGE) {
                        // 拿缓存,
                        $key      = self::NO_PARAMS_CACHE_KEY . ':' . $sort . ':' . $page;
                        $dataJson = Cache::get($key);
                        $data     = json_decode($dataJson, true);
                        $allList  = $data['list'];
                        $total    = $data['total'];
                        // 根据page来拿实际所需数据
                        $start = ($page - 1) * $this->pageSize;
                        $list  = array_slice($allList, $start, $this->pageSize);

                        return [
                            'list'  => $allList,
                            'total' => $total,
                        ];
                    }

                    return null;
                }
            }
        }

        $cacheKey  = Cache::ALL_NEW_JOB_LIST_PARAMS_LIST_KEY . '_' . $this->key;
        $cacheData = Cache::get($cacheKey);

        if ($cacheData) {
            $cacheData = json_decode($cacheData, true);
        } else {
            $cacheData = [
                'list'  => [],
                'total' => 0,
            ];
        }

        return $cacheData;
    }

    private function setCacheData($data)
    {
        if ($this->page > 1) {
            return null;
        }
        if ($this->sort && $this->sort != 'new') {
            return null;
        }
        $cacheKey = Cache::ALL_NEW_JOB_LIST_PARAMS_LIST_KEY . '_' . $this->key;

        Cache::set($cacheKey, json_encode($data), self::CACHE_TIME);
    }

    private function setPublicSelectArr()
    {
        $this->publicSelectArr = [
            'j.id as jobId',
            'j.status',
            'j.name as jobName',
            'j.company_id as companyId',
            'j.min_wage as minWage',
            'j.max_wage as maxWage',
            'j.wage_type as wageType',
            'j.experience_type as experienceType',
            'j.education_type as educationType',
            'j.amount',
            'j.job_category_id as jobCategoryId',
            'j.welfare_tag as welfareTag',
            'j.province_id as provinceId',
            'j.city_id as cityId',
            'c.full_name as companyName',
            'c.type as companyType',
            'c.nature as companyNature',
            'j.release_time as releaseTime',
            'j.refresh_time as refreshTime',
            'j.apply_type as applyType',
            'j.apply_address as applyAddress',
            'j.announcement_id as announcementId',
            'j.delivery_way',
            'j.major_id as majorId',
            'j.is_miniapp as isMiniapp',
            'j.is_establishment as isEstablishment',
            'c.is_cooperation as isCooperation',
            'c.logo_url as companyLogo',
            'c.sort as companySort',
        ];
    }

    private function setTopInfo($data)
    {
        //获取置顶的职位列表
        $jobTopApp = JobTopApplication::getInstance();
        $info      = $jobTopApp->getJobTopList($data);

        //获取下面职位列表查询的分页信息
        if (!empty($info)) {
            $this->limit  = $data['pageSize'] - $info['currentPageAmount'];
            $this->offset = $info['offsetAmount'];
        } else {
            $this->limit  = $data['pageSize'] ?: self::DEFAULT_PAGE_SIZE;
            $this->offset = $data['pageSize'] * ($data['page'] - 1) ?: 0;
        }

        //所有的置顶职位idList，用于下面职位列表查询进行not in
        $this->allTopIdList = $info['offsetJobList'];
        //总的置顶数据
        $currentPageTopList = $info['currentPageList'];

        if (!empty($currentPageTopList)) {
            //拆分查询绝对置顶和其他置顶列表
            if (!empty($currentPageTopList['topList'])) {
                foreach ($currentPageTopList['topList'] as $info) {
                    $job = BaseJob::find()
                        ->alias('j')
                        ->leftJoin(['c' => BaseCompany::tableName()], 'c.id=j.company_id')
                        ->where(['j.id' => $info['jobId']])
                        ->select($this->publicSelectArr)
                        ->asArray()
                        ->one();

                    $job['topType']          = $info['topType'];
                    $job['setTime']          = $info['addTime'];
                    $this->absoluteTopList[] = $job;
                }
            }

            if (!empty($currentPageTopList['otherList'])) {
                foreach ($currentPageTopList['otherList'] as $info) {
                    $job                  = BaseJob::find()
                        ->alias('j')
                        ->leftJoin(['c' => BaseCompany::tableName()], 'c.id=j.company_id')
                        ->where(['j.id' => $info['jobId']])
                        ->select($this->publicSelectArr)
                        ->asArray()
                        ->one();
                    $job['topType']       = $info['topType'];
                    $job['setTime']       = $info['addTime'];
                    $this->otherTopList[] = $job;
                }
            }
        }
    }

    /**
     * 对绝对置顶、其他置顶、普通职位进行排序
     * @param $list
     * @return array
     */
    private function sortList($list)
    {
        //此时list有20条数据，包括绝对置顶（可能）、搜索置顶、其他职位
        $topList      = [];
        $otherTopList = [];
        $otherJobList = [];
        foreach ($list as $item) {
            $topType = $item['topType'];
            if (!empty($topType)) {
                if ($topType == BaseJobTopConfig::TOP_TYPE_ABSOLUTE) {
                    //绝对置顶
                    $topList[] = $item;
                } elseif ($topType == BaseJobTopConfig::TOP_TYPE_SEARCH) {
                    $otherTopList[] = $item;
                }
            } else {
                $otherJobList[] = $item;
            }
        }
        $newList = [];
        //判断是否要穿插
        if (!empty($otherTopList)) {
            //间隔条数
            $spaceAmount = DailyServer::SPACE_AMOUNT;
            //总条数
            $allOtherAmount = count($otherTopList) + count($otherJobList);
            $otherJobNum    = 0;
            $otherTopJobNum = 0;
            for ($i = 0; $i < $allOtherAmount; $i++) {
                if ($this->page > 1) {
                    //取余数，如果为0，则是倍数或者0
                    $spaceQuotient = ($i) % $spaceAmount;
                    //取当前倍数，是数组的第几个元素
                    $spaceTimes = $i / $spaceAmount;
                } else {
                    //如果是第一页，要特殊处理，因为绝对置顶占位了，要变成普普普顶
                    //取余数，如果为0，则是倍数或者0
                    $spaceQuotient = ($i + 1) % $spaceAmount;
                    //取当前倍数，是数组的第几个元素
                    $spaceTimes = ($i + 1) / $spaceAmount - 1;
                }

                if ($this->page > 1 && $i == 0) {
                    if (!empty($otherTopList[$otherTopJobNum])) {
                        //如果是间隔的倍数，插入置顶
                        $newList[$i]    = $otherTopList[$otherTopJobNum];
                        $otherTopJobNum += 1;
                    }
                } elseif (($spaceQuotient == 0) && $i > 0) {
                    if (!empty($otherTopList[$spaceTimes])) {
                        //如果是间隔的倍数，插入置顶
                        $newList[$i]    = $otherTopList[$spaceTimes];
                        $otherTopJobNum += 1;
                    } else {
                        $newList[$i] = $otherJobList[$otherJobNum];
                        $otherJobNum++;
                    }
                } else {
                    if (!empty($otherJobList[$otherJobNum])) {
                        //其他情况，放入其他职位
                        $newList[$i] = $otherJobList[$otherJobNum];
                        $otherJobNum++;
                    } elseif (!empty($otherTopList[$otherTopJobNum])) {
                        //如果没有其他职位了，放入其他置顶职位
                        $newList[$i] = $otherTopList[$otherTopJobNum];
                    }
                }
            }
        } else {
            $newList = $otherJobList;
        }

        return array_merge($topList, $newList);
    }

    private function getData()
    {
        $cacheData = $this->getCacheData();
        if ($cacheData['list']) {
            $list = $cacheData['list'];
        } else {
            $this->total    = self::DEFAULT_PAGE_TOTAL;
            $this->pageSize = self::DEFAULT_PAGE_SIZE;
            $list           = $this->search();
        }
        if (count($list) < 20) {
            // 证明已经是最后一页了
            // $this->total = ($this->page - 1) * self::DEFAULT_PAGE_SIZE + count($list);
            $this->total = $this->page * self::DEFAULT_PAGE_SIZE;
        } else {
            // 证明已经是最后一页了
            // 商
            $val = intval($this->page / self::DEFAULT_PAGE_SIZE);

            $this->total = ($val + 1) * self::DEFAULT_PAGE_TOTAL;

            // 最多2000
            if ($this->total > 800) {
                $this->total = 800;
            }
        }

        if (!$cacheData['list']) {
            $this->setCacheData([
                'total' => $this->total,
                'list'  => $list,
            ]);
        }

        if ($this->memberId) {
            $resume_info = BaseResume::findOne(['member_id' => $this->memberId]);
        }
        foreach ($list as &$item) {
            $item['url']             = BaseJob::getDetailUrl($item['jobId']);
            $item['companyUrl']      = BaseCompany::getDetailUrl($item['companyId']);
            $item['announcementUrl'] = BaseAnnouncement::getDetailUrl($item['announcementId']);
            $item['companyLogo']     = $item['companyLogo'] ?: Yii::$app->params['defaultCompanyLogo'];
            $item['isFast']          = BaseJob::isFast($item['jobId']) ? '1' : '2';
            $item['isTop']           = $item['topType'] ? '1' : '2';
            if ($this->memberId) {
                $item['isCollect']   = (BaseJobCollect::checkIsCollect($this->memberId, $item['jobId'])) ? '1' : '2';
                $item['applyStatus'] = BaseJobApplyRecord::checkJobApplyStatus($resume_info->id, $item['jobId']);
                //                if ($item['isCooperation'] == 'true') {
                //                    //如果用户已经登录了，获取用户投递信息
                //                    //获取用户对该职位投递情况
                //                    if ($item['delivery_way'] > 0) {
                //                        if ($item['delivery_way'] == BaseJob::DELIVERY_WAY_LINK) {
                //                            $item['applyStatus'] = BaseOffSiteJobApply::checkJobApplyStatus($this->memberId,
                //                                $item['jobId']);
                //                        } else {
                //                            //获取用户对该职位投递情况
                //                            $item['applyStatus'] = BaseJobApply::checkJobApplyStatus($this->memberId, $item['jobId']);
                //                        }
                //                    } elseif ($item['announcementId'] > 0) {
                //                        $announcementDeliveryWay = BaseAnnouncement::findOneVal(['id' => $item['announcementId']],
                //                            'delivery_way');
                //                        if ($announcementDeliveryWay > 0) {
                //                            if ($announcementDeliveryWay == BaseAnnouncement::DELIVERY_WAY_LINK) {
                //                                $item['applyStatus'] = BaseOffSiteJobApply::checkJobApplyStatus($this->memberId,
                //                                    $item['jobId']);
                //                            } else {
                //                                //获取用户对该职位投递情况
                //                                $item['applyStatus'] = BaseJobApply::checkJobApplyStatus($this->memberId,
                //                                    $item['jobId']);
                //                            }
                //                        }
                //                    }
                //                    //$item['applyStatus'] = BaseJobApply::checkJobApplyStatus($this->memberId, $item['jobId']);
                //                } else {
                //                    //获取用户对该职位投递情况
                //                    $item['applyStatus'] = BaseOffSiteJobApply::checkJobApplyStatus($this->memberId, $item['jobId']);
                //
                //                    $item['userEmail'] = BaseMember::findOneVal(['id' => $this->memberId], 'email');
                //                }

            }

            // 方便前端调用
            $item['isCooperation'] = $item['isCooperation'] == 'true' ? '1' : '2';
        }

        $this->jobList = $list;
    }

    /**
     * 设置只查询关键字
     */
    private function setCheckOnlyKeyword()
    {
        $this->checkOnlyKeyword = ArrayHelper::filterAndValidateKeys([
            'page',
            'sort',
            'pageSize',
            'keyword',
        ], $this->params);
    }

    /**
     * 置顶查询和职位列表查询统一放到这里
     * @param $params
     * @param $topJobIds
     * @return \yii\db\ActiveQuery
     */
    public function setPublicQuery($params = [], $topJobIds = [])
    {
        if (empty($this->params) && !empty($topJobIds)) {
            //从职位置顶那边过来的
            $this->params = $params;
        }

        $jobIds    = [];
        $mainQuery = BaseJob::find()
            ->alias('j')
            ->innerJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
            ->innerJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'cgss.id = c.group_score_system_id')
            ->where(['j.is_show' => BaseJob::IS_SHOW_YES]);

        if ($this->params['applyHeatType']) {
            $mainQuery->andWhere([
                'j.status' => BaseJob::STATUS_ONLINE,
            ]);
        } else {
            $mainQuery->andWhere([
                'in',
                'j.status',
                [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ]);
        }

        if (Yii::$app->params['testCompanyId']) {
            $mainQuery->andWhere([
                'not in',
                'j.company_id',
                Yii::$app->params['testCompanyId'],
            ]);
        }

        // 地区(已经变为城市了)
        $mainQuery->andFilterWhere(['j.city_id' => $this->params['areaId']]);
        // 职位类型
        $mainQuery->andFilterWhere(['j.job_category_id' => $this->params['jobType']]);
        // 单位性质companyNature
        $mainQuery->andFilterWhere(['c.nature' => $this->params['companyNature']]);
        // 单位的类型
        $mainQuery->andFilterWhere(['c.type' => $this->params['companyType']]);
        // 小程序
        $mainQuery->andFilterWhere(['j.is_miniapp' => $this->params['isMini']]);
        // 合作单位
        if ($this->params['isCooperation']) {
            $mainQuery->andFilterWhere(['c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES]);
        }
        // 有编制
        $mainQuery->andFilterWhere(['j.is_establishment' => $this->params['isEstablishment']]);

        //投递热度查询
        $mainQuery->andFilterWhere(['j.apply_heat_type' => $this->params['applyHeatType']]);

        if ($this->params['isFresh']) {
            // 应届生
            $mainQuery->andFilterWhere([
                'j.experience_type' => [
                    BaseDictionary::EXPERIENCE_FRESH_Id,
                    -1,
                ],

            ]);
        }

        $majorId = $this->params['majorId'];
        if ($majorId) {
            // 这里就比较麻烦了,
            $majorJobIds = $this->getJobIdsByMajorId($majorId);
            if (count($majorJobIds) > 0) {
                $jobIds = array_merge($jobIds, $majorJobIds);
            }
        }

        // 职业福利
        if ($this->params['welfareLabelId']) {
            $welfareLabelJobIds = $this->getJobIdsByWelfareLabelId($this->params['welfareLabelId']);
            if (count($welfareLabelJobIds) > 0) {
                $jobIds = array_merge($jobIds, $welfareLabelJobIds);
            }
        }

        // 学历要求
        $mainQuery->andFilterWhere(['j.education_type' => $this->params['educationType']]);
        // 行业类别
        $mainQuery->andFilterWhere(['c.industry_id' => $this->params['industryId']]);
        // 工作经验
        $mainQuery->andFilterWhere(['j.experience_type' => $this->params['experienceType']]);

        // 发布时间
        $mainQuery->andFilterWhere([
            '>=',
            'j.refresh_time',
            $this->params['releaseTimeBegin'],
        ]);
        if ($this->params['wageBegin'] == 0 && $this->params['wageEnd'] == 0 && !empty($this->params['wageId'])) {
            $mainQuery->andFilterWhere([
                'j.wage_type' => 0,
            ]);
        } else {
            // 最小薪资wageBegin
            $mainQuery->andFilterWhere([
                '>=',
                'j.min_wage',
                $this->params['wageBegin'],
            ]);

            // 最大薪资wageEnd
            if ($this->params['wageEnd']) {
                $mainQuery->andFilterWhere([
                    '<=',
                    'j.max_wage',
                    $this->params['wageEnd'],
                ]);
                $mainQuery->andFilterWhere([
                    '>',
                    'j.max_wage',
                    0,
                ]);
            }
        }

        // 单位规模
        $mainQuery->andFilterWhere(['c.scale' => $this->params['companyScaleType']]);
        // 职位性质
        $mainQuery->andFilterWhere(['j.nature_type' => $this->params['natureType']]);
        // 职称类型
        $mainQuery->andFilterWhere(['j.title_type' => $this->params['titleType']]);

        if (!empty($topJobIds)) {
            if (!empty($jobIds)) {
                //如果有其他ids，需要取交集
                $topJobIds = array_intersect($topJobIds, $jobIds);
            }
            //必须加进去查询
            $mainQuery->andWhere([
                'j.id' => $topJobIds,
            ]);
        } else {
            // 专业
            if ($jobIds) {
                $mainQuery->andWhere([
                    'j.id' => $jobIds,
                ]);
            }
        }

        if ($this->params['isFast']) {
            $mainQuery->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = j.announcement_id');
            if ($this->params['isFast']) {
                $mainQuery->andWhere([
                    'or',
                    [
                        'j.delivery_type' => BaseJob::DELIVERY_TYPE_OUTSIDE,
                        'j.delivery_way'  => BaseJob::DELIVERY_WAY_PLATFORM,
                    ],
                    [
                        'j.delivery_type' => BaseJob::DELIVERY_TYPE_UP_ANNOUNCEMENT,
                        'a.delivery_type' => BaseAnnouncement::DELIVERY_TYPE_OUTSIDE,
                        'a.delivery_way'  => BaseAnnouncement::DELIVERY_WAY_PLATFORM,
                    ],
                ]);
            }
        }

        // 验证是否只有keyword的时候、
        $needUseKeyword = true;
        if ($this->params['keyword']) {
            $where              = [
                [
                    '=',
                    'keywords',
                    $this->params['keyword'],
                ],
                [
                    '<>',
                    'job_ids',
                    '',
                ],
            ];
            $keywordsPreprocess = BaseJobKeywordsPreprocess::getOneByWhere($where, [
                'id',
                'job_ids',
            ]);
            if ($keywordsPreprocess) {
                $jobIds = explode(',', $keywordsPreprocess['job_ids']);
                // 当结果只有800条以下，或者只有关键字查询，可直接查询制定职位
                if (count($jobIds) < 800 || $this->checkOnlyKeyword) {
                    $mainQuery->andFilterWhere([
                        'j.id' => $jobIds,
                    ]);
                    $needUseKeyword    = false;
                    $this->isNeedOrder = false;
                    $mainQuery->orderBy(new \yii\db\Expression('FIELD(j.id, ' . $keywordsPreprocess['job_ids'] . ')'));
                }
            }
        }

        //  本地直接使用模糊查询
        if ($this->params['keyword'] && Yii::$app->params['environment'] == 'local') {
            $mainQuery->andWhere([
                'like',
                'j.search_name',
                $this->params['keyword'],
            ]);
            $needUseKeyword = false;
        }

        if ($this->params['keyword'] && $needUseKeyword) {
            if (isset($this->originParams['keyword_b']) && $this->originParams['keyword_b'] == 1) {
                //职位中心B页面过来的-走meilisearch
                $jobIds = (new SearchService())->setKeyword($this->params['keyword'])
                    ->run();
                if (!empty($jobIds)) {
                    $mainQuery->andWhere([
                        'j.id' => $jobIds,
                    ]);
                } else {
                    $mainQuery->andWhere([
                        'j.id' => 0,
                    ]);
                }
            } else {
                $jobIds = (new SearchService())->setKeyword($this->params['keyword'])
                    ->run();
                if (count($jobIds) >= 2000) {
                    // 证明多了,重新走匹配
                    // $mainQuery->andFilterWhere([
                    //     'or',
                    //     [
                    //         'like',
                    //         'j.name',
                    //         $this->params['keyword'],
                    //     ],
                    //     [
                    //         'like',
                    //         'c.full_name',
                    //         $this->params['keyword'],
                    //     ],
                    //     [
                    //         'like',
                    //         'j.department',
                    //         $this->params['keyword'],
                    //     ],
                    //     [
                    //         'like',
                    //         'a.title',
                    //         $this->params['keyword'],
                    //     ],
                    // ]);

                    $mainQuery->andWhere([
                        'like',
                        'j.search_name',
                        $this->params['keyword'],
                    ]);
                } else {
                    if (!empty($jobIds)) {
                        $mainQuery->andWhere([
                            'j.id' => $jobIds,
                        ]);
                    } else {
                        $mainQuery->andWhere([
                            'j.id' => 0,
                        ]);
                    }
                }
                // 关键字
            }
        }

        return $mainQuery;
    }

    /**
     * 根据关键字获取列表
     * @param $keyword
     * @return array|\yii\db\ActiveRecord[]
     */
    public function getListByKeywords($keyword)
    {
        $this->params['keyword'] = $keyword;
        $this->isOnlyJobList     = true;

        return $this->search();
    }

    private function search()
    {
        $this->setCheckOnlyKeyword();
        $mainQuery = $this->setPublicQuery();
        $sort      = 'j.status desc,j.refresh_date desc,j.is_first_release asc,cgss.score desc,j.id desc';

        //        switch ($this->sort) {
        //            case 'new':
        //                // 最新排序
        //                //                $sort = 'j.status desc,j.refresh_date desc,j.company_sort desc,j.id desc';
        //                $sort = 'j.status desc,j.refresh_date desc,j.is_first_release asc,cgss.score desc,j.id desc';
        //                break;
        //            case 'default':
        //                //综合排序
        //                $sort = 'j.status desc,j.click desc,j.company_sort desc,j.id desc';
        //                break;
        //            default:
        //                $sort = 'j.status desc,j.refresh_date desc,j.company_sort desc,j.id desc';
        //                break;
        //        }

        //        $page     = $this->page ?? 1;
        //        $pageSize = $this->pageSize ?? self::DEFAULT_PAGE_SIZE;

        //        $getTopParams = [
        //            'jobCateGoryId' => $this->params['jobType'],
        //            //这里要未处理过的areaId，无需下沉，因为配置那边是省、市分开
        //            'areaId'        => $this->originParams['areaId'],
        //            'page'          => $page,
        //            'pageSize'      => $pageSize,
        //        ];

        //设置公告的查询字段
        $this->setPublicSelectArr();
        //获取置顶职位列表
        $this->setTopInfo($this->params);
        // 计算偏移量
        //        $offset = ($page - 1) * $pageSize;
        //排除置顶的职位
        $mainQuery->andFilterWhere([
            'not in',
            'j.id',
            $this->allTopIdList,
        ]);

        // 用于全量查询
        if ($this->isOnlyJobList) {
            $this->offset = 0;
            $this->limit  = 800;
        }

        $startTime = microtime(true);
        if ($this->isNeedOrder) {
            $mainQuery->orderBy($sort);
        }

        $list = $mainQuery->select($this->publicSelectArr)
            ->asArray()
            ->offset($this->offset)
            ->limit($this->limit)
            ->all();

        if ($this->isOnlyJobList) {
            return $list;
        }

        // 结束时间
        $endTime = microtime(true);

        // 计算执行时间
        $executionTime = $endTime - $startTime;

        // 如果是关键字查询，且是第一页，就需要记录一下关键字（这里因为测试环境很少有超过1秒，所以测试环境都记录一下，生产就大于1秒才记录）
        if ((Yii::$app->params['environment'] !== 'prod' || $executionTime > 1) && $this->checkOnlyKeyword && $this->params['page'] == 1) {
            // 使用 ZADD 命令记录搜索次数
            $key = Cache::JOB_SEARCH_KEYWORDS;
            // 使用 ZADD 命令记录搜索次数
            Cache::zIncrBy($key, 1, $this->params['keyword']);
            Cache::expire($key, 30 * 24 * 60 * 60);
        }
        // $this->realTotal = $mainQuery->count();

        //合并起来,补齐其他信息
        $list = array_merge($list, $this->absoluteTopList, $this->otherTopList);
        foreach ($list as $k => &$jobRecord) {
            // 有些职位在添加的时候加上了一些换行,这里去掉
            $jobRecord['jobName'] = str_replace(PHP_EOL, '', $jobRecord['jobName']);
            //拼接工资
            if ($jobRecord['minWage'] == 0 && $jobRecord['maxWage'] == 0) {
                $jobRecord['wage'] = '面议';
            } else {
                $jobRecord['wage'] = BaseJob::formatWage($jobRecord['minWage'], $jobRecord['maxWage'],
                    $jobRecord['wageType']);
            }

            // 找到公告的信息
            if ($jobRecord['announcementId']) {
                $announcement = BaseAnnouncement::find()
                    ->select([
                        'title',
                        'template_id',
                    ])
                    ->where([
                        'id' => $jobRecord['announcementId'],
                    ])
                    ->asArray()
                    ->one();

                if ($announcement) {
                    $jobRecord['announcementName'] = $announcement['title'];
                    $jobRecord['templateId']       = $announcement['template_id'];
                }
            } else {
                $jobRecord['announcementName'] = '';
            }

            //获取经验要求
            $jobRecord['experience'] = $this->getTmpDictionaryNameList('experience', $jobRecord['experienceType']);
            // 获取学历要求
            $jobRecord['education'] = $this->getTmpDictionaryNameList('education', $jobRecord['educationType']);
            //获取意向职能
            $jobRecord['jobCategory'] = $this->getTmpDictionaryNameList('jobCategory', $jobRecord['jobCategoryId']);
            //获取福利标签
            $jobRecord['welfareTagArr'] = $this->getTmpDictionaryNameList('welfareTag', $jobRecord['welfareTag']);
            // 获取地区名称
            $jobRecord['areaName'] = $this->getTmpDictionaryNameList('area',
                    $jobRecord['provinceId']) . '-' . $this->getTmpDictionaryNameList('area', $jobRecord['cityId']);
            // 获取城市名称
            $jobRecord['city'] = $this->getTmpDictionaryNameList('area', $jobRecord['cityId']);
            //获取单位类型
            $jobRecord['companyTypeName'] = $this->getTmpDictionaryNameList('companyType', $jobRecord['companyType']);
            // 单位类型
            $jobRecord['companyNatureName'] = $this->getTmpDictionaryNameList('companyNature',
                $jobRecord['companyNature']);
            //获取职位需求专业
            $jobRecord['jobRecord'] = $this->getTmpDictionaryNameList('major', $jobRecord['majorId']);

            //处理发布时间
            $thisYearTime = mktime(0, 0, 0, 1, 1, date('Y'));
            $releaseTime  = strtotime($jobRecord['releaseTime']);
            if ($releaseTime > $thisYearTime) {
                $jobRecord['releaseTime'] = date('m-d', $releaseTime);
            } else {
                $jobRecord['releaseTime'] = date('Y-m-d', $releaseTime);
            }
            $refreshTime = $jobRecord['refreshTime'];
            //判断职位是否是合作单位的职位
            $jobRecord['applyStatus']      = BaseJob::JOB_APPLY_STATUS_NO;
            $jobRecord['userEmail']        = '';
            $jobRecord['isEmailApply']     = "false";
            $jobRecord['shortRefreshTime'] = date('m-d', strtotime($refreshTime));
            $jobRecord['refreshDate']      = TimeHelper::formatDateByYear($refreshTime);

            $cooperationInfo = $jobRecord['isCooperation'];

            if ($cooperationInfo == BaseCompany::COOPERATIVE_UNIT_YES) {
                //如果是合作单位，站内投递
                $jobRecord['isCooperation'] = "true";
            } else {
                //站外投递
                $jobRecord['isCooperation'] = "false";

                //判断职位投递方式是否是邮箱投递
                $applyTypeArr = explode(',', $jobRecord['applyType']);

                if (in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr)) {
                    $jobRecord['isEmailApply'] = "true";
                }

                foreach ($applyTypeArr as $v) {
                    $jobRecord['applyTypeText'] .= BaseDictionary::getSignUpName($v) . ';';
                }
                if (!empty($jobRecord['applyTypeText'])) {
                    //去除结尾分号
                    $jobRecord['applyTypeText'] = substr($jobRecord['applyTypeText'], 0, -1);
                }
            }
        }

        //        开始进行排序，插入
        return $this->sortList($list);
    }

    private function getJobIdsByMajorId(array $majorId)
    {
        // 这里传过来的专业有可能是1级也有可能是1级,得把所有二级都找出来
        $levelIdArr = BaseMajor::find()
            ->where([
                'level'     => 2,
                'parent_id' => $majorId,
            ])
            ->asArray()
            ->column();

        $majorId = array_merge($majorId, $levelIdArr);

        $jobIds = BaseJobMajorRelation::find()
            ->select('job_id')
            // 使用find_in_set来处理多个
            ->where(['major_id' => $majorId])
            ->asArray()
            ->column();

        // 去重
        $jobIds = array_unique($jobIds);

        return $jobIds;
    }


    // private function getJobIdsByMajorId(array $majorId)
    // {
    //     $findInSetArray = ['or'];
    //     foreach ($majorId as $id) {
    //         $findInSetArray[] = new Expression("FIND_IN_SET(:{$id}, major_id)", [":{$id}" => $id]);
    //     }
    //
    //     $jobIds = BaseJob::find()
    //         ->select('id')
    //         ->where(['is_show' => BaseJob::IS_SHOW_YES])
    //         ->andWhere([
    //             'in',
    //             'status',
    //             [
    //                 BaseJob::STATUS_ONLINE,
    //                 BaseJob::STATUS_OFFLINE,
    //             ],
    //         ])
    //         // 使用find_in_set来处理多个
    //         ->andWhere($findInSetArray)
    //         ->asArray()
    //         ->column();
    //
    //
    //     return $jobIds;
    // }

    private function getJobIdsByWelfareLabelId(array $welfareId)
    {
        $jobIds = BaseJobWelfareRelation::find()
            ->select('job_id')
            // 使用find_in_set来处理多个
            ->where(['welfare_id' => $welfareId])
            ->asArray()
            ->column();

        $jobIds = array_unique($jobIds);

        return $jobIds;
    }

    // private function getJobIdsByWelfareLabelId(array $welfareLabelId)
    // {
    //     return [];
    //     // 晚上11点到早上6点不显示福利标签
    //     $nowHour = date('H');
    //     if ($nowHour >= 23 || $nowHour <= 6) {
    //         return [];
    //     }
    //     $findInSetArray = ['or'];
    //     foreach ($welfareLabelId as $id) {
    //         $findInSetArray[] = new Expression("FIND_IN_SET(:{$id}, welfare_tag)", [":{$id}" => $id]);
    //     }
    //
    //     $jobIds = BaseJob::find()
    //         ->select('id')
    //         ->where(['is_show' => BaseJob::IS_SHOW_YES])
    //         ->andWhere([
    //             'in',
    //             'status',
    //             [
    //                 BaseJob::STATUS_ONLINE,
    //                 BaseJob::STATUS_OFFLINE,
    //             ],
    //         ])
    //         // 使用find_in_set来处理多个
    //         ->andWhere($findInSetArray)
    //         ->asArray()
    //         ->column();
    //
    //     return $jobIds;
    // }

    private function check()
    {
        // 这里主要是对于ip做一个限制,避免恶意刷,一分钟内,同一个ip,只能查询10次,超过就不允许了
        // $ip = $this->ip;

        // 搜索引擎的,就不需要
        // 找到当前执行的路由
        $url = Yii::$app->controller->action->uniqueId;

        if ($url == 'engine/index') {
            return true;
        }

        try {
            $ip = IpHelper::getIp();
        } catch (\Exception $e) {
            $ip = '';
        }

        $paramIpList = \Yii::$app->params['ipWhiteList'];

        // 非正式环境就直接返回true就可以了
        if (Yii::$app->params['environment'] != 'prod') {
            return true;
        }

        if (in_array($ip, $paramIpList)) {
            return true;
        }

        $key       = Cache::ALL_IP_WHITE_LIST_JOB_SEARCH_KEY . ':' . $ip;
        $minute    = date('YmdHi');
        $countData = json_decode(Cache::get($key), true);
        // 同一分钟不允许超过10次

        $value = $countData[$minute] + 1;

        if ($value > 60) {
            throw new MessageException('您搜索的太频繁了');
            // try {
            //     $ua = Yii::$app->request->getUserAgent();
            //     Cache::lPush('TMP:JOB_SEARCH_UA', json_encode([
            //         'ua'     => $ua,
            //         'ip'     => IpHelper::getIp(),
            //         'userId' => Yii::$app->user->id,
            //         'time'   => CUR_DATETIME,
            //         'url'    => \Yii::$app->request->url,
            //     ]));
            // } catch (\Exception $e) {
            //     // do nothing
            // }

        }
        $countData[$minute] = $value;
        Cache::set($key, json_encode($countData), 3600);

        return true;
    }

    public function filter()
    {
        $originParams             = $this->originParams;
        $this->page               = $originParams['page'] ?? 1;
        $this->pageSize           = $originParams['pageSize'] ?? self::DEFAULT_PAGE_SIZE;
        $this->sort               = $originParams['sort'] ?? 'new';//看代码没有排序默认是走new
        $this->params['page']     = $this->page;
        $this->params['sort']     = $this->sort;
        $this->params['pageSize'] = $this->pageSize;

        // 这里做一个小程序的
        if ($this->type == self::TYPE_MINI_JOB_LIST) {
            $this->params['isMini'] = 1;
        }

        // 首先去掉一些不符合的搜索参数
        // 在这里重新组装一下参数,因为不是所有参数都是合法的
        // page
        // wageId
        // majorId
        // keyword
        // areaId
        // companyNature
        // companyType
        // educationType
        // natureType
        // scale
        // industryId
        // companyId
        // jobCategoryId
        // jobType
        // releaseTimeType
        // pageSize
        // sort

        // 把需要的参数塞到params里面去
        foreach (self::PARAMS_KEY_LIST as $key => $item) {
            // 一个一个过滤数据
            switch ($key) {
                case 'keyword':
                    $keyword = $originParams['keyword'];
                    if (!empty($keyword)) {
                        $this->handelKeyword($keyword);
                    }
                    break;
                case 'areaId':
                    $areaId = $originParams['areaId'];
                    if (!empty($areaId)) {
                        $this->handelAreaId($areaId);
                    }
                    break;
                case 'companyNature':
                    $companyNature = $originParams['companyNature'];
                    if (!empty($companyNature)) {
                        $this->handelCompanyNature($companyNature);
                    }
                    break;
                case 'companyType':
                    $companyType = $originParams['companyType'];
                    if (!empty($companyType)) {
                        $this->handelCompanyType($companyType);
                    }
                    break;
                case 'majorId':
                    $majorId = $originParams['majorId'];
                    if (!empty($majorId)) {
                        $this->handelMajorId($majorId);
                    }
                    break;
                case 'educationType':
                    $educationType = $originParams['educationType'];
                    if (!empty($educationType)) {
                        $this->handelEducationType($educationType);
                    }
                    break;
                case 'industryId':
                    $industryId = $originParams['industryId'];
                    if (!empty($industryId)) {
                        $this->handelIndustryId($industryId);
                    }
                    break;
                case 'experienceType':
                    $experienceType = $originParams['experienceType'];
                    if (!empty($experienceType)) {
                        $this->handelExperienceType($experienceType);
                    }
                    break;
                case 'releaseTimeType':
                    $releaseTimeType = $originParams['releaseTimeType'];
                    if (!empty($releaseTimeType)) {
                        // 这里需要考虑一个从小程序过来的情况
                        if (PLATFORM == 'MINI') {
                            $this->handelMiniReleaseTimeType($releaseTimeType);
                        } else {
                            $this->handelReleaseTimeType($releaseTimeType);
                        }
                    }
                    break;
                case 'wageId':
                    $wageId = $originParams['wageId'];
                    if (!empty($wageId)) {
                        $this->handelWageId($wageId);
                    }
                    break;
                case 'companyScaleType':
                    $scale = $originParams['companyScaleType'];
                    if (!empty($scale)) {
                        $this->handelScale($scale);
                    }
                    break;
                case 'natureType':
                    $natureType = $originParams['natureType'];
                    if (!empty($natureType)) {
                        $this->handelNatureType($natureType);
                    }
                    break;
                case 'titleType':
                    $titleType = $originParams['titleType'];
                    if (!empty($titleType)) {
                        $this->handelTitleType($titleType);
                    }
                    break;
                case 'welfareLabelId':
                    $welfareLabelId = $originParams['welfareLabelId'];
                    if (!empty($welfareLabelId)) {
                        $this->handelWelfareLabelId($welfareLabelId);
                    }
                    break;
                case 'jobType':
                    $jobType = $originParams['jobType'];
                    if (!empty($jobType)) {
                        $this->handelJobType($jobType);
                    }
                    break;
                case 'isFast':
                    $isFast = $originParams['isFast'];
                    if (!empty($isFast)) {
                        $this->handelIsFast();
                    }
                    break;
                case 'isFresh':
                    $isFresh = $originParams['isFresh'];
                    if (!empty($isFresh)) {
                        $this->handelIsFresh();
                    }
                    break;
                case 'isCooperation':
                    $isCooperation = $originParams['isCooperation'];
                    if (!empty($isCooperation)) {
                        $this->handelIsCooperation();
                    }
                    break;
                case 'isEstablishment':
                    $isEstablishment = $originParams['isEstablishment'];
                    if (!empty($isEstablishment)) {
                        $this->handelIsEstablishment();
                    }
                    break;
                case 'applyHeat':
                    $applyHeat = $originParams['applyHeat'];
                    if (!empty($applyHeat)) {
                        $this->handelApplyHeat($applyHeat);
                    }
                    break;
                case 'page':
                    $page = $originParams['page'];
                    break;
                case 'memberId':
                    $memberId = $originParams['memberId'];
                    break;
            }
        }
    }

    // 获取和设置一些临时的字典名称
    private function getTmpDictionaryNameList($key, $name)
    {
        $value = $this->tmpDictionaryNameList[$key][$name];
        if ($value) {
            return $value;
        }

        switch ($key) {
            case 'experience':
                $value = BaseDictionary::getExperienceName($name);
                break;
            case 'education':
                $value = BaseDictionary::getEducationName($name);
                break;
            case 'jobCategory':
                $value = BaseCategoryJob::getName($name);
                break;
            case 'welfareTag':
                if (PLATFORM == 'MINI') {
                    $value = BaseWelfareLabel::getWelfareLabelNameList($name);
                } else {
                    $value = array_slice(BaseWelfareLabel::getWelfareLabelNameList($name), 0, 2);
                }

                break;
            case 'area':
                $value = BaseArea::getAreaName($name);
                break;
            case 'companyType':
                $value = BaseDictionary::getCompanyTypeName($name);
                break;
            case 'companyNature':
                $value = BaseDictionary::getCompanyNatureName($name);
                break;
            case 'major':
                $value       = '';
                $jobMajorIds = explode(',', $name);
                if (count($jobMajorIds) > 0) {
                    $value = BaseMajor::getAllMajorName($jobMajorIds);
                }
                break;
        }

        // aa($value);

        $this->tmpDictionaryNameList[$key][$name] = $value;

        return $value;
    }

    private function handelKeyword($keyword)
    {
        // 只能是数字或者字母或者中文字符?
        $keyword = trim($keyword);
        if (empty($keyword)) {
            return '';
        }

        $this->params['keyword'] = $keyword;
        $this->keyword           = $keyword;
    }

    private function handelAreaId($areaId)
    {
        // 先把逗号转成下划线
        $areaId = str_replace(',', '_', $areaId);
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        $areaIdArr = explode('_', $areaId);
        // 去掉空的和非数字的
        $areaIdArr = array_filter($areaIdArr, function ($item) {
            return is_numeric($item);
        });

        if (PLATFORM != 'MINI') {
            if (count($areaIdArr) > 5) {
                throw new MessageException('最多只能选择5个地区');
            }
        }

        // 把地区都下沉到第二级
        $areaIds = BaseArea::getCityIds($areaIdArr);

        $this->params['areaId'] = $areaIds;

        return $areaId;
    }

    private function handelCompanyNature($companyNature)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        $companyNature    = str_replace(',', '_', $companyNature);
        $companyNatureArr = explode('_', $companyNature);
        // 去掉空的和非数字的
        $companyNatureArr = array_filter($companyNatureArr, function ($item) {
            return is_numeric($item);
        });

        if (PLATFORM != 'MINI') {
            if (count($companyNatureArr) > 5) {
                throw new MessageException('最多只能选择5个单位性质');
            }
        }

        $this->params['companyNature'] = $companyNatureArr;

        return $companyNature;
    }

    private function handelCompanyType($companyType)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        $companyType    = str_replace(',', '_', $companyType);
        $companyTypeArr = explode('_', $companyType);
        // 去掉空的和非数字的
        $companyTypeArr = array_filter($companyTypeArr, function ($item) {
            return is_numeric($item);
        });

        if (PLATFORM != 'MINI') {
            if (count($companyTypeArr) > 5) {
                throw new MessageException('最多只能选择5个单位类型');
            }
        }

        $this->params['companyType'] = $companyTypeArr;

        return $companyType;
    }

    private function handelMajorId($majorId)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        $majorId    = str_replace(',', '_', $majorId);
        $majorIdArr = explode('_', $majorId);
        // 去掉空的和非数字的
        $majorIdArr = array_filter($majorIdArr, function ($item) {
            return is_numeric($item);
        });

        if (PLATFORM != 'MINI') {
            if (count($majorIdArr) > 5) {
                throw new MessageException('最多只能选择5个专业');
            }
        }

        $this->params['majorId'] = $majorIdArr;

        return $majorId;
    }

    private function handelEducationType($educationType)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        // $educationTypeArr = explode('_', $educationType);
        // // 去掉空的和非数字的
        // $educationTypeArr = array_filter($educationTypeArr, function ($item) {
        //     return is_numeric($item);
        // });
        //
        if (PLATFORM != 'MINI') {
            // if (count($educationTypeArr) > 5) {

            //     throw new MessageException('最多只能选择5个学历');
        }
        // }

        $educationTypeList = BaseDictionary::getEducationValueByKey($educationType);
        if (is_string($educationTypeList)) {
            $educationTypeList = explode(',', $educationTypeList);
        }

        $this->params['educationType'] = $educationTypeList;

        return $educationType;
    }

    private function handelIndustryId($industryId)
    {
        // 只能一个并且需要是数字
        if (!is_numeric($industryId)) {
            throw new MessageException('行业类别必须是数字');
        }

        // 行业类别是两级的,所以我们需要把下面的都下沉到第二级
        $trade = BaseTrade::findOne($industryId);

        if ($trade->level == 1) {
            $this->params['industryId'] = BaseTrade::find()
                ->select('id')
                ->where(['parent_id' => $industryId])
                ->column();
        } else {
            $this->params['industryId'] = $industryId;
        }

        return $industryId;
    }

    private function handelExperienceType($experienceType)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id
        $experienceType = str_replace(',', '_', $experienceType);

        $experienceTypeArr = explode('_', $experienceType);
        // 去掉空的和非数字的
        $experienceTypeArr = array_filter($experienceTypeArr, function ($item) {
            return is_numeric($item);
        });

        if (PLATFORM != 'MINI') {
            if (count($experienceTypeArr) > 5) {
                throw new MessageException('最多只能选择5个工作经验');
            }
        }

        $this->params['experienceType'] = $experienceTypeArr;

        return $experienceType;
    }

    private function handelMiniReleaseTimeType($releaseTimeType)
    {
        $releaseTimeInfoArray = explode(',', $releaseTimeType);

        // 找到最大的一个(4,3,2,1)
        $releaseTimeType = max($releaseTimeInfoArray);

        $releaseTimeInfo = BaseDictionary::getReleaseTimeListInfo($releaseTimeType);

        if (empty($releaseTimeInfo)) {
            throw new MessageException('发布时间类型错误');
        }

        $this->params['releaseTimeBegin'] = $releaseTimeInfo;

        return $releaseTimeInfo;
    }

    private function handelReleaseTimeType($releaseTimeType)
    {
        $releaseTimeInfo = BaseDictionary::getReleaseTimeListInfo($releaseTimeType);

        if (empty($releaseTimeInfo)) {
            throw new MessageException('发布时间类型错误');
        }

        $this->params['releaseTimeBegin'] = $releaseTimeInfo;

        return $releaseTimeInfo;
    }

    private function handelWageId($wageId)
    {
        $wageInfo = BaseDictionary::getMinAndMaxWage($wageId);

        if (empty($wageInfo)) {
            throw new MessageException('薪资类型错误');
        }

        //累赘一个字段，后面判断面议用
        $this->params['wageId']    = $wageId;
        $this->params['wageBegin'] = $wageInfo['min'];
        $this->params['wageEnd']   = $wageInfo['max'];

        return $wageInfo;
    }

    private function handelScale($scale)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id,并且只能是1~7
        $scale = str_replace(',', '_', $scale);

        $scaleArr = explode('_', $scale);
        // 去掉空的和非数字的
        $scaleArr = array_filter($scaleArr, function ($item) {
            return is_numeric($item) && $item >= 1 && $item <= 7;
        });

        if (count($scaleArr) > 7) {
            throw new MessageException('最多只能选择7个公司规模');
        }

        $this->params['companyScaleType'] = $scaleArr;

        return $scale;
    }

    private function handelNatureType($natureType)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id,并且只能是1~7
        $natureType = str_replace(',', '_', $natureType);

        $natureTypeArr = explode('_', $natureType);
        // 去掉空的和非数字的
        $natureTypeArr = array_filter($natureTypeArr, function ($item) {
            return is_numeric($item) && $item >= 1 && $item <= 5;
        });

        if (PLATFORM != 'MINI') {
            if (count($natureTypeArr) > 5) {
                throw new MessageException('最多只能选择5个工作性质');
            }
        }

        $this->params['natureType'] = $natureTypeArr;

        return $natureType;
    }

    private function handelJobType($jobType)
    {
        $jobType = str_replace(',', '_', $jobType);

        $jobTypeArr = explode('_', $jobType);
        // 去掉空的和非数字的

        if (PLATFORM != 'MINI') {
            if (count($jobTypeArr) > 5) {
                throw new MessageException('最多只能选择5个职位类型');
            }
        }

        $this->params['jobType'] = $jobTypeArr;

        return $jobType;
    }

    private function handelIsFast()
    {
        // 去掉空的和非数字的

        $this->params['isFast'] = 1;

        return '1';
    }

    private function handelIsFresh()
    {
        // 去掉空的和非数字的

        $this->params['isFresh'] = 1;

        return '1';
    }

    private function handelIsCooperation()
    {
        // 去掉空的和非数字的

        $this->params['isCooperation'] = 1;

        return '1';
    }

    private function handelIsEstablishment()
    {
        // 去掉空的和非数字的

        $this->params['isEstablishment'] = 1;

        return '1';
    }

    private function handelApplyHeat($applyHeat)
    {
        // 判断是否存在该热度类型
        if (!in_array($applyHeat, BaseJob::APPLY_HEAT_TYPE_LIST)) {
            throw new MessageException('职位热度类型不存在');
        }
        $this->params['applyHeatType'] = $applyHeat;

        return '1';
    }

    private function handelWelfareLabelId($welfareLabelId)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id,并且只能是1~7
        $welfareLabelId = str_replace(',', '_', $welfareLabelId);

        $welfareLabelIdArr = explode('_', $welfareLabelId);
        // 去掉空的和非数字的
        $welfareLabelIdArr = array_filter($welfareLabelIdArr, function ($item) {
            return is_numeric($item);
        });

        if (PLATFORM != 'MINI') {
            if (count($welfareLabelIdArr) > 5) {
                throw new MessageException('最多只能选择5个工作性质');
            }
        }

        $this->params['welfareLabelId'] = $welfareLabelIdArr;

        return $welfareLabelId;
    }

    private function handelTitleType($titleType)
    {
        // 前端传过来是xxx_xxx_xxx等模式,所以我们需要处理成id,并且只能是1~7
        $titleType = str_replace(',', '_', $titleType);

        $titleTypeArr = explode('_', $titleType);
        // 去掉空的和非数字的
        $titleTypeArr = array_filter($titleTypeArr, function ($item) {
            return is_numeric($item) && $item >= 1 && $item <= 4;
        });

        if (count($titleTypeArr) > 4) {
            throw new MessageException('最多只能选择4个职称');
        }

        $this->params['titleType'] = $titleTypeArr;

        return $titleType;
    }

    public function getSeoWikiJobList($ids)
    {
        if (empty($ids)) {
            return [];
        }
        $list = BaseJob::find()
            ->alias('j')
            ->innerJoin(['c' => BaseCompany::tableName()], 'j.company_id = c.id')
            ->innerJoin(['cgss' => BaseCompanyGroupScoreSystem::tableName()], 'cgss.id = c.group_score_system_id')
            ->where([
                'j.is_show' => BaseJob::IS_SHOW_YES,
                'j.id'      => $ids,
            ])
            ->orderBy(new Expression('FIELD(j.id,' . implode(',', $ids) . ')'))
            ->select([
                'j.id as jobId',
                'j.status',
                'j.name as jobName',
                'j.company_id as companyId',
                'j.min_wage as minWage',
                'j.max_wage as maxWage',
                'j.wage_type as wageType',
                'j.experience_type as experienceType',
                'j.education_type as educationType',
                'j.amount',
                'j.job_category_id as jobCategoryId',
                'j.welfare_tag as welfareTag',
                'j.province_id as provinceId',
                'j.city_id as cityId',
                'c.full_name as companyName',
                'c.type as companyType',
                'c.nature as companyNature',
                'j.release_time as releaseTime',
                'j.refresh_time as refreshTime',
                'j.apply_type as applyType',
                'j.apply_address as applyAddress',
                'j.announcement_id as announcementId',
                'j.delivery_way',
                'j.major_id as majorId',
                'j.is_miniapp as isMiniapp',
                'j.is_establishment as isEstablishment',
                'c.is_cooperation as isCooperation',
                'c.logo_url as companyLogo',
                'c.sort as companySort',
                'j.duty',
                'j.requirement',
                'j.refresh_time',
            ])
            ->asArray()
            ->all();
        foreach ($list as $k => &$jobRecord) {
            // 有些职位在添加的时候加上了一些换行,这里去掉
            $jobRecord['jobName'] = str_replace(PHP_EOL, '', $jobRecord['jobName']);
            //拼接工资
            if ($jobRecord['minWage'] == 0 && $jobRecord['maxWage'] == 0) {
                $jobRecord['wage'] = '面议';
            } else {
                $jobRecord['wage'] = BaseJob::formatWage($jobRecord['minWage'], $jobRecord['maxWage'],
                    $jobRecord['wageType']);
            }

            // 找到公告的信息
            if ($jobRecord['announcementId']) {
                $announcement = BaseAnnouncement::find()
                    ->select([
                        'title',
                        'template_id',
                    ])
                    ->where([
                        'id' => $jobRecord['announcementId'],
                    ])
                    ->asArray()
                    ->one();

                if ($announcement) {
                    $jobRecord['announcementName'] = $announcement['title'];
                    $jobRecord['templateId']       = $announcement['template_id'];
                }
            } else {
                $jobRecord['announcementName'] = '';
            }

            //获取经验要求
            $jobRecord['experience'] = $this->getTmpDictionaryNameList('experience', $jobRecord['experienceType']);
            // 获取学历要求
            $jobRecord['education'] = $this->getTmpDictionaryNameList('education', $jobRecord['educationType']);
            //获取意向职能
            $jobRecord['jobCategory'] = $this->getTmpDictionaryNameList('jobCategory', $jobRecord['jobCategoryId']);
            //获取福利标签
            $jobRecord['welfareTagArr'] = $this->getTmpDictionaryNameList('welfareTag', $jobRecord['welfareTag']);
            // 获取地区名称
            $jobRecord['areaName'] = $this->getTmpDictionaryNameList('area',
                    $jobRecord['provinceId']) . '-' . $this->getTmpDictionaryNameList('area', $jobRecord['cityId']);
            // 获取城市名称
            $jobRecord['city'] = $this->getTmpDictionaryNameList('area', $jobRecord['cityId']);
            //获取单位类型
            $jobRecord['companyTypeName'] = $this->getTmpDictionaryNameList('companyType', $jobRecord['companyType']);
            // 单位类型
            $jobRecord['companyNatureName'] = $this->getTmpDictionaryNameList('companyNature',
                $jobRecord['companyNature']);
            //获取职位需求专业
            $jobRecord['jobRecord'] = $this->getTmpDictionaryNameList('major', $jobRecord['majorId']);

            //处理发布时间
            $thisYearTime = mktime(0, 0, 0, 1, 1, date('Y'));
            $releaseTime  = strtotime($jobRecord['releaseTime']);
            if ($releaseTime > $thisYearTime) {
                $jobRecord['releaseTime'] = date('m-d', $releaseTime);
            } else {
                $jobRecord['releaseTime'] = date('Y-m-d', $releaseTime);
            }
            $refreshTime = $jobRecord['refreshTime'];
            //判断职位是否是合作单位的职位
            $jobRecord['applyStatus']      = BaseJob::JOB_APPLY_STATUS_NO;
            $jobRecord['userEmail']        = '';
            $jobRecord['isEmailApply']     = "false";
            $jobRecord['shortRefreshTime'] = date('m-d', strtotime($refreshTime));
            $jobRecord['refreshDate']      = TimeHelper::formatDateByYear($refreshTime);
        }

        return $list;
    }

}
