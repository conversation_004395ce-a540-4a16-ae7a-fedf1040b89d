<?php
/**
 * create user：shannon
 * create time：2024/3/5 11:39
 */
namespace common\service\resume;

use common\base\BaseActiveRecord;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCertificate;
use common\base\models\BaseDictionary;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAcademicBook;
use common\base\models\BaseResumeAcademicPage;
use common\base\models\BaseResumeAcademicPatent;
use common\base\models\BaseResumeAcademicReward;
use common\base\models\BaseResumeAdditionalInfo;
use common\base\models\BaseResumeCertificate;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeIntention;
use common\base\models\BaseResumeOtherReward;
use common\base\models\BaseResumeOtherSkill;
use common\base\models\BaseResumeResearchDirection;
use common\base\models\BaseResumeResearchProject;
use common\base\models\BaseResumeSkill;
use common\base\models\BaseResumeWork;
use common\base\models\BaseSkill;
use common\helpers\FormatConverter;
use common\libs\Cache;
use common\service\CommonService;
use Faker\Provider\Base;

/**
 * 实现求职者简历缓存服务
 * 文档路劲 document/personCache/resumeCache.md
 * Class ResumeCacheService
 * @package common\service\resume
 */
class ResumeCacheService extends CommonService
{
    /** 缓存KEY结构 */
    //    RESUME:
    //        ID:
    //            INFO:
    //            TOP_EDUCATION:
    //            EDUCATION:
    //            WORK:
    //            INTENTION：
    //            ACADEMIC_BOOK：
    //            ACADEMIC_PAGE：
    //            ACADEMIC_PATENT：
    //            ACADEMIC_REWARD：
    //            ADDITIONAL_INFO：
    //            CERTIFICATE：
    //            OTHER_REWARD：
    //            OTHER_SKILL：
    //            RESEARCH_DIRECTION：
    //            RESEARCH_PROJECT：
    //            SKILL：

    //const RESUME_CACHE_KEY_PREFIX             = 'RESUME:';//简历缓存前缀
    const RESUME_CACHE_KEY_INFO               = 'INFO';//简历基本信息
    const RESUME_CACHE_KEY_TOP_EDUCATION      = 'TOP_EDUCATION';//最高学历
    const RESUME_CACHE_KEY_EDUCATION          = 'EDUCATION';//教育经历
    const RESUME_CACHE_KEY_WORK               = 'WORK';//工作经历
    const RESUME_CACHE_KEY_INTENTION          = 'INTENTION';//求职意向
    const RESUME_CACHE_KEY_ACADEMIC_BOOK      = 'ACADEMIC_BOOK';//学术著作
    const RESUME_CACHE_KEY_ACADEMIC_PAGE      = 'ACADEMIC_PAGE';//学术论文
    const RESUME_CACHE_KEY_ACADEMIC_PATENT    = 'ACADEMIC_PATENT';//学术专利
    const RESUME_CACHE_KEY_ACADEMIC_REWARD    = 'ACADEMIC_REWARD';//学术奖励
    const RESUME_CACHE_KEY_ADDITIONAL_INFO    = 'ADDITIONAL_INFO';//附加信息
    const RESUME_CACHE_KEY_CERTIFICATE        = 'CERTIFICATE';//资质证书
    const RESUME_CACHE_KEY_OTHER_REWARD       = 'OTHER_REWARD';//其他奖励
    const RESUME_CACHE_KEY_OTHER_SKILL        = 'OTHER_SKILL';//其他技能
    const RESUME_CACHE_KEY_RESEARCH_DIRECTION = 'RESEARCH_DIRECTION';//研究方向
    const RESUME_CACHE_KEY_RESEARCH_PROJECT   = 'RESEARCH_PROJECT';//研究项目
    const RESUME_CACHE_KEY_SKILL              = 'SKILL';//技能/语言

    //一月过期时间
    const RESUME_CACHE_EXPIRE_TIME = 2592000;

    /** 简历缓存KEY列表 */
    const RESUME_CACHE_KEY_LIST = [
        self::RESUME_CACHE_KEY_INFO,
        self::RESUME_CACHE_KEY_TOP_EDUCATION,
        self::RESUME_CACHE_KEY_EDUCATION,
        self::RESUME_CACHE_KEY_WORK,
        self::RESUME_CACHE_KEY_INTENTION,
        self::RESUME_CACHE_KEY_ACADEMIC_BOOK,
        self::RESUME_CACHE_KEY_ACADEMIC_PAGE,
        self::RESUME_CACHE_KEY_ACADEMIC_PATENT,
        self::RESUME_CACHE_KEY_ACADEMIC_REWARD,
        self::RESUME_CACHE_KEY_ADDITIONAL_INFO,
        self::RESUME_CACHE_KEY_CERTIFICATE,
        self::RESUME_CACHE_KEY_OTHER_REWARD,
        self::RESUME_CACHE_KEY_OTHER_SKILL,
        self::RESUME_CACHE_KEY_RESEARCH_DIRECTION,
        self::RESUME_CACHE_KEY_RESEARCH_PROJECT,
        self::RESUME_CACHE_KEY_SKILL,
    ];

    /**
     * 获取简历缓存KEY
     * @param int $resumeId
     * @return string
     */
    private static function getResumeCacheKey(int $resumeId): string
    {
        return Cache::RESUME_CACHE_KEY_PREFIX . $resumeId;
    }

    /**
     * 获取简历的全部信息
     * @param int $resumeId 简历ID
     */
    public static function getAll(int $resumeId): array
    {
        $result = [];
        BaseActiveRecord::openDb2();
        //直接获取缓存
        foreach (self::RESUME_CACHE_KEY_LIST as $item) {
            $method = self::getCamelCase($item, 'get');
            if (method_exists(self::class, $method)) {
                $result[self::upperToLowerCamel($item)] = self::$method($resumeId);
            }
        }
        BaseActiveRecord::closeDb2();

        return $result;
    }

    /**
     * 设置简历的全部信息
     */
    public static function setAll(int $resumeId)
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::delete($key);
        foreach (self::RESUME_CACHE_KEY_LIST as $item) {
            if ($item == self::RESUME_CACHE_KEY_TOP_EDUCATION) {
                continue;
            }
            $method = self::getCamelCase($item, 'set');
            if (method_exists(self::class, $method)) {
                self::$method($resumeId);
            }
        }
        //设置过期时间
        //Cache::expire($key, self::RESUME_CACHE_EXPIRE_TIME);

        //        //补充基础信息
        //        self::setInfo($resumeId);
        //        //补充教育经历--这涵盖了最高学历更新，所以不用管最高学历更新了
        //        self::setEducation($resumeId);
        //        //补充工作经历
        //        self::setWork($resumeId);
        //        //补充求职意向
        //        self::setIntention($resumeId);
        //        //补充学术专著
        //        self::setAcademicBook($resumeId);
        //        //补充学术论文
        //        self::setAcademicPage($resumeId);
        //        //补充学术专利
        //        self::setAcademicPatent($resumeId);
        //        //补充学术奖励
        //        self::setAcademicReward($resumeId);
        //        //补充附加信息
        //        self::setAdditionalInfo($resumeId);
        //        //补充资质证书
        //        self::setCertificate($resumeId);
        //        //补充其他奖励
        //        self::setOtherReward($resumeId);
        //        //补充其他技能
        //        self::setOtherSkill($resumeId);
        //        //补充研究方向
        //        self::setResearchDirection($resumeId);
        //        //补充研究项目
        //        self::setResearchProject($resumeId);
        //        //补充技能/语言
        //        self::setSkill($resumeId);

        return true;
    }

    /**
     * 获取指定的简历信息
     * @param int          $resumeId 简历ID
     * @param string|array $key      缓存KEY必须是RESUME_CACHE_KEY_LIST中的值
     */
    public static function get(int $resumeId, $key)
    {
        if (is_array($key)) {
            $result = [];
            foreach ($key as $item) {
                if (!in_array($item, self::RESUME_CACHE_KEY_LIST)) {
                    continue;
                }
                $method = self::getCamelCase($item, 'get');
                if (method_exists(self::class, $method)) {
                    $result[self::upperToLowerCamel($item)] = self::$method($resumeId);
                }
            }

            return $result;
        } else {
            if (!in_array($key, self::RESUME_CACHE_KEY_LIST)) {
                return [];
            }
            $method = self::getCamelCase($key, 'get');
            if (method_exists(self::class, $method)) {
                return self::$method($resumeId);
            }

            return [];
        }
    }

    /**
     * 设置指定的简历信息
     * @param int          $resumeId 简历ID
     * @param string|array $key      缓存KEY必须是RESUME_CACHE_KEY_LIST中的值
     */
    public static function set(int $resumeId, $key)
    {
        if (is_array($key)) {
            //$result = [];
            foreach ($key as $item) {
                if (!in_array($item, self::RESUME_CACHE_KEY_LIST)) {
                    continue;
                }
                $method = self::getCamelCase($item, 'set');
                if (method_exists(self::class, $method)) {
                    self::$method($resumeId);
                    //$result[self::upperToLowerCamel($item)] = self::$method($resumeId);
                }
            }
            //return $result;
        } else {
            if (!in_array($key, self::RESUME_CACHE_KEY_LIST)) {
                return false;
            }
            $method = self::getCamelCase($key, 'set');
            if (method_exists(self::class, $method)) {
                self::$method($resumeId);
                //return self::$method($resumeId);
            }
        }

        return true;
    }

    /**
     * 删除简历信息--谨慎使用：使用需考虑缓存数据更新补充问题
     * @param int $resumeId
     * @return true
     */
    public static function deleteAll(int $resumeId)
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::delete($key);

        return true;
    }

    /**
     * 删除指定的简历信息--谨慎使用：使用需考虑缓存数据更新补充问题
     * @param int          $resumeId 简历ID
     * @param string|array $key      缓存KEY必须是RESUME_CACHE_KEY_LIST中的值
     */
    public static function delete(int $resumeId, $key)
    {
        if (is_array($key)) {
            foreach ($key as $item) {
                if (!in_array($item, self::RESUME_CACHE_KEY_LIST)) {
                    continue;
                }
                Cache::hDel(self::getResumeCacheKey($resumeId), $item);
            }
        } else {
            if (!in_array($key, self::RESUME_CACHE_KEY_LIST)) {
                return false;
            }
            Cache::hDel(self::getResumeCacheKey($resumeId), $key);
        }

        return true;
    }

    /**
     * 获取简历的基本信息
     * @param int $resumeId 简历ID
     */
    public static function getInfo(int $resumeId)
    {
        $key  = self::getResumeCacheKey($resumeId);
        $info = Cache::hGet($key, self::RESUME_CACHE_KEY_INFO);
        if (!$info) {
            $info = self::setInfo($resumeId);
        } else {
            $info = json_decode($info, true);
        }

        return $info;
    }

    /**
     * 设置简历基本信息
     */
    public static function setInfo(int $resumeId)
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::hDel($key, self::RESUME_CACHE_KEY_INFO);
        //获取简历基本信息
        $info = BaseResume::find()
            ->select([
                'id',
                'member_id',
                'name',
                'gender',
                'birthday',
                'residence',
                'marriage',
                'enter_job_time',
                'native_place_area_id',
                'id_card',
                'advantage',
                'political_status_id',
                'nation_id',
                'title_id',
                'household_register_id',
                'arrive_date_type',
                'work_status',
                'is_project_school',
                'age',
                'work_experience',
                'last_education_id',
                'top_education_code',
                'last_work_id',
                'birthday_code',
                'work_begin_date_code',
                'last_update_time',
                'complete',
                'identity_type',
                'begin_work_date',
                'uuid',
                'is_abroad',
                'is_resume_library',
                'resume_type',
            ])
            ->where(['id' => $resumeId])
            ->asArray()
            ->one();
        // 处理一些数据信息
        //把用户头像加上
        $info['avatar'] = BaseMember::findOneVal(['id' => $info['member_id']], 'avatar');
        // 性别文案
        $info['genderName'] = BaseResume::GENDER_LIST[$info['gender']];
        // 籍贯文案
        $info['nativePlaceName'] = BaseArea::getAreaName($info['native_place_area_id']);
        // 政治面貌文案
        $info['politicalStatusName'] = BaseDictionary::getPoliticalStatusName($info['political_status_id']);
        // 民族文案
        $info['nationName'] = BaseDictionary::getNationName($info['nation_id']);
        // 职称文案
        $info['titleName'] = BaseResume::getTitleName($info['title_id']);
        // 户籍/国籍
        $info['householdRegisterName'] = BaseArea::getAreaName($info['household_register_id']);
        // 到岗时间文案
        $info['arriveDateTypeName'] = BaseDictionary::getArriveDateName($info['arrive_date_type']);
        // 已婚状态文案
        $info['marriageName'] = BaseResume::MARRIAGE_LIST[$info['marriage']];
        // 在职状态文案
        $info['workStatusName'] = BaseDictionary::getJobStatusName($info['work_status']);
        // 身份类型文案
        $info['identityTypeName'] = BaseResume::IDENTITY_TEXT_LIST[$info['identity_type']];

        // 将数据转成驼峰
        $info = FormatConverter::convertLine($info);
        // 设置缓存进去
        self::setCache($key, self::RESUME_CACHE_KEY_INFO, $info);

        return $info;
    }

    /**
     * 获取简历的最高学历
     * @param int $resumeId 简历ID
     */
    public static function getTopEducation($resumeId)
    {
        //获取缓存KEY
        $key          = self::getResumeCacheKey($resumeId);
        $topEducation = Cache::hGet($key, self::RESUME_CACHE_KEY_TOP_EDUCATION);
        if (!$topEducation) {
            $topEducation = self::setTopEducation($resumeId);
        } else {
            $topEducation = json_decode($topEducation, true);
        }

        return $topEducation;
    }

    /**
     * 设置简历的最高学历
     * 一般跟随教育经验去更新
     * @param int $resumeId 简历ID
     */
    private static function setTopEducation(int $resumeId)
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::hDel($key, self::RESUME_CACHE_KEY_TOP_EDUCATION);
        //获取最高学历ID
        $top_education_id = BaseResumeEducation::getTopEducationId($resumeId);
        //获取最高学历信息
        $topEducation = BaseResumeEducation::find()
            ->select([
                'id',
                'resume_id',
                'member_id',
                'school',
                'begin_date',
                'end_date',
                'is_abroad',
                'major_id',
                'education_id',
                'is_recruitment',
                'college',
                'is_project_school',
                'major_custom',
                'major_id_level_1',
                'major_id_level_2',
                'major_id_level_3',
                'mentor',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseResumeEducation::STATUS_ACTIVE,
                'id'        => $top_education_id,
            ])
            ->asArray()
            ->one();

        // 学历文案
        $topEducation['educationName'] = BaseDictionary::getEducationName($topEducation['education_id']);
        // 专业文案
        $topEducation['majorName'] = $topEducation['major_id'] > 0 ? BaseMajor::getMajorName($topEducation['major_id']) : '';
        // 是否海外文案
        $topEducation['isAbroadName'] = $topEducation['is_abroad'] == 1 ? '是' : '否';
        // 一级专业文案
        $topEducation['majorLevel1Name'] = $topEducation['major_id_level_1'] > 0 ? BaseMajor::getMajorName($topEducation['major_id_level_1']) : '';
        // 二级专业文案
        $topEducation['majorLevel2Name'] = $topEducation['major_id_level_2'] > 0 ? BaseMajor::getMajorName($topEducation['major_id_level_2']) : '';
        // 三级专业文案
        $topEducation['majorLevel3Name'] = $topEducation['major_id_level_3'] > 0 ? BaseMajor::getMajorName($topEducation['major_id_level_3']) : '';

        // 将数据转成驼峰
        $topEducation = FormatConverter::convertLine($topEducation);
        // 设置缓存进去
        self::setCache($key, self::RESUME_CACHE_KEY_TOP_EDUCATION, $topEducation);

        return $topEducation;
    }

    /**
     * 获取简历的教育经历
     * @param int $resumeId 简历ID
     */
    public static function getEducation(int $resumeId)
    {
        //获取缓存KEY
        $key       = self::getResumeCacheKey($resumeId);
        $education = Cache::hGet($key, self::RESUME_CACHE_KEY_EDUCATION);
        if (!$education) {
            $education = self::setEducation($resumeId);
        } else {
            $education = json_decode($education, true);
        }

        return $education;
    }

    /**
     * 设置简历的教育经历
     * @param int $resumeId 简历ID
     */
    public static function setEducation(int $resumeId): array
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::hDel($key, self::RESUME_CACHE_KEY_EDUCATION);
        //获取数据
        $education = BaseResumeEducation::find()
            ->select([
                'id',
                'resume_id',
                'member_id',
                'school',
                'begin_date',
                'end_date',
                'is_abroad',
                'college',
                'major_id',
                'education_id',
                'is_recruitment',
                'is_project_school',
                'major_custom',
                'major_id_level_1',
                'major_id_level_2',
                'major_id_level_3',
                'mentor',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseResumeEducation::STATUS_ACTIVE,
            ])
            //按照education_id 4,3,2,1,5及end_date降序
            ->orderBy([
                'FIELD(education_id,4,3,2,1,5)' => SORT_ASC,
                'end_date'                      => SORT_DESC,
                'id'                            => SORT_DESC,
            ])
            ->asArray()
            ->all();
        // 处理一些数据信息
        foreach ($education as &$item) {
            // 学历文案
            $item['educationName'] = BaseDictionary::getEducationName($item['education_id']);
            // 专业文案
            $item['majorName'] = $item['major_id'] > 0 ? BaseMajor::getMajorName($item['major_id']) : '';
            //is_recruitment是否统招文案
            $item['recruitmentName'] = $item['is_recruitment'] == BaseResumeEducation::IS_RECRUITMENT_YES ? BaseResumeEducation::IS_RECRUITMENT_YES_NAME : BaseResumeEducation::IS_RECRUITMENT_NO_NAME;
            // 是否海外文案
            $item['isAbroadName'] = $item['is_abroad'] == 1 ? '是' : '否';
            // 一级专业文案
            $item['majorLevel1Name'] = $item['major_id_level_1'] > 0 ? BaseMajor::getMajorName($item['major_id_level_1']) : '';
            // 二级专业文案
            $item['majorLevel2Name'] = $item['major_id_level_2'] > 0 ? BaseMajor::getMajorName($item['major_id_level_2']) : '';
            // 三级专业文案
            $item['majorLevel3Name'] = $item['major_id_level_3'] > 0 ? BaseMajor::getMajorName($item['major_id_level_3']) : '';
        }
        //设置一下最高学历的缓存
        self::setTopEducation($resumeId);

        // 将数据转成驼峰
        $education = FormatConverter::convertLine($education);
        // 设置缓存进去
        self::setCache($key, self::RESUME_CACHE_KEY_EDUCATION, $education);

        return $education;
    }

    /**
     * 获取简历的工作经历
     * @param int $resumeId 简历ID
     */
    public static function getWork(int $resumeId)
    {
        //获取缓存KEY
        $key  = self::getResumeCacheKey($resumeId);
        $work = Cache::hGet($key, self::RESUME_CACHE_KEY_WORK);
        if (!$work) {
            $work = self::setWork($resumeId);
        } else {
            $work = json_decode($work, true);
        }

        return $work;
    }

    /**
     * 设置简历的工作经历
     * @param int $resumeId 简历ID
     */
    public static function setWork(int $resumeId): array
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::hDel($key, self::RESUME_CACHE_KEY_WORK);
        //获取数据
        $work = BaseResumeWork::find()
            ->select([
                'id',
                'resume_id',
                'member_id',
                'begin_date',
                'end_date',
                'is_today',
                'is_abroad',
                'is_postdoc',
                'company',
                'department',
                'job_name',
                'content',
                'is_practice',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseResumeWork::STATUS_ACTIVE,
            ])
            ->orderBy([
                'end_date' => SORT_DESC,
                'id'       => SORT_DESC,
            ])
            ->asArray()
            ->all();
        // 数据处理
        foreach ($work as &$item) {
            // 是否海外文案
            $item['isAbroadName'] = $item['is_abroad'] == 1 ? '是' : '否';
            // 是否博士后文案
            $item['isPostdocName'] = $item['is_postdoc'] == 1 ? '是' : '否';
            // 是否实习文案
            $item['isPracticeName'] = $item['is_practice'] == 1 ? '是' : '否';
        }

        // 将数据转成驼峰
        $work = FormatConverter::convertLine($work);
        // 设置缓存进去
        self::setCache($key, self::RESUME_CACHE_KEY_WORK, $work);

        return $work;
    }

    /**
     * 获取简历的求职意向
     * @param int $resumeId
     */
    public static function getIntention(int $resumeId)
    {
        //获取缓存KEY
        $key       = self::getResumeCacheKey($resumeId);
        $intention = Cache::hGet($key, self::RESUME_CACHE_KEY_INTENTION);
        if (!$intention) {
            $intention = self::setIntention($resumeId);
        } else {
            $intention = json_decode($intention, true);
        }

        return $intention;
    }

    /**
     * 设置简历求职意向
     * @param int $resumeId
     */
    public static function setIntention(int $resumeId): array
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::hDel($key, self::RESUME_CACHE_KEY_INTENTION);
        //获取简历求职意向
        $intention = BaseResumeIntention::find()
            ->select([
                'id',
                'resume_id',
                'member_id',
                'nature_type',
                'job_category_id',
                'area_id',
                'wage_type',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseResumeIntention::STATUS_ACTIVE,
            ])
            ->asArray()
            ->all();
        // 处理一些数据信息
        foreach ($intention as &$item) {
            // 工作性质文案
            $item['natureTypeName'] = BaseDictionary::getNatureName($item['nature_type']);
            // 工作类别文案
            $item['jobCategoryName'] = BaseCategoryJob::getName($item['job_category_id']);
            // 期望工作地点文案
            $item['areaName'] = BaseArea::getTextByIdList(explode(',', $item['area_id']));
            // 期望薪资文案
            $item['wageTypeName'] = BaseDictionary::getWageRangeName($item['wage_type']);
        }

        // 将数据转成驼峰
        $intention = FormatConverter::convertLine($intention);
        // 设置缓存进去
        self::setCache($key, self::RESUME_CACHE_KEY_INTENTION, $intention);

        return $intention;
    }

    /**
     * 获取简历学术专著
     * @param int $resumeId
     */
    public static function getAcademicBook(int $resumeId)
    {
        //获取缓存KEY
        $key          = self::getResumeCacheKey($resumeId);
        $academicBook = Cache::hGet($key, self::RESUME_CACHE_KEY_ACADEMIC_BOOK);
        if (!$academicBook) {
            $academicBook = self::setAcademicBook($resumeId);
        } else {
            $academicBook = json_decode($academicBook, true);
        }

        return $academicBook;
    }

    /**
     * 设置简历学术专著
     * @param int $resumeId
     */
    public static function setAcademicBook(int $resumeId)
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::hDel($key, self::RESUME_CACHE_KEY_ACADEMIC_BOOK);
        //获取简历学术专著
        $academicBook = BaseResumeAcademicBook::find()
            ->select([
                'id',
                'resume_id',
                'member_id',
                'name',
                'publish_date',
                'words',
                'publish_amount',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseResumeAcademicBook::STATUS_ACTIVE,
            ])
            ->orderBy([
                'publish_date' => SORT_DESC,
                'id'           => SORT_DESC,
            ])
            ->asArray()
            ->all();
        // 处理一些数据信息

        // 将数据转成驼峰
        $academicBook = FormatConverter::convertLine($academicBook);
        // 设置缓存进去
        self::setCache($key, self::RESUME_CACHE_KEY_ACADEMIC_BOOK, $academicBook);

        return $academicBook;
    }

    /**
     * 获取简历学术论文
     * @param int $resumeId
     */
    public static function getAcademicPage(int $resumeId)
    {
        //获取缓存KEY
        $key          = self::getResumeCacheKey($resumeId);
        $academicPage = Cache::hGet($key, self::RESUME_CACHE_KEY_ACADEMIC_PAGE);
        if (!$academicPage) {
            $academicPage = self::setAcademicPage($resumeId);
        } else {
            $academicPage = json_decode($academicPage, true);
        }

        return $academicPage;
    }

    /**
     * 设置简历学术论文
     * @param int $resumeId
     */
    public static function setAcademicPage(int $resumeId)
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::hDel($key, self::RESUME_CACHE_KEY_ACADEMIC_PAGE);
        //获取简历学术论文
        $academicPage = BaseResumeAcademicPage::find()
            ->select([
                'id',
                'resume_id',
                'member_id',
                'title',
                'serial_number',
                'publish_date',
                'record_situation',
                'position',
                'impact_factor',
                'description',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseResumeAcademicPage::STATUS_ACTIVE,
            ])
            ->orderBy([
                'publish_date' => SORT_DESC,
                'id'           => SORT_DESC,
            ])
            ->asArray()
            ->all();
        // 处理一些数据信息
        foreach ($academicPage as &$item) {
            // 名次
            $item['positionName'] = $item['position'] > 0 ? BaseDictionary::getPaperRankName($item['position']) : '';
        }

        // 将数据转成驼峰
        $academicPage = FormatConverter::convertLine($academicPage);
        // 设置缓存进去
        self::setCache($key, self::RESUME_CACHE_KEY_ACADEMIC_PAGE, $academicPage);

        return $academicPage;
    }

    /**
     * 获取简历学术专利
     * @param int $resumeId
     */
    public static function getAcademicPatent(int $resumeId)
    {
        //获取缓存KEY
        $key            = self::getResumeCacheKey($resumeId);
        $academicPatent = Cache::hGet($key, self::RESUME_CACHE_KEY_ACADEMIC_PATENT);
        if (!$academicPatent) {
            $academicPatent = self::setAcademicPatent($resumeId);
        } else {
            $academicPatent = json_decode($academicPatent, true);
        }

        return $academicPatent;
    }

    /**
     * 设置简历学术专利
     * @param int $resumeId
     */
    public static function setAcademicPatent(int $resumeId)
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::hDel($key, self::RESUME_CACHE_KEY_ACADEMIC_PATENT);
        //获取简历学术专利
        $academicPatent = BaseResumeAcademicPatent::find()
            ->select([
                'id',
                'resume_id',
                'member_id',
                'name',
                'number',
                'authorization_date',
                'finish_status',
                'position',
                'description',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseResumeAcademicPatent::STATUS_ACTIVE,
            ])
            ->orderBy([
                'authorization_date' => SORT_DESC,
                'id'                 => SORT_DESC,
            ])
            ->asArray()
            ->all();
        // 处理一些数据信息
        foreach ($academicPatent as &$item) {
            // 名次
            $item['positionName'] = $item['position'] > 0 ? BaseDictionary::getPatentRankName($item['position']) : '';
        }

        // 将数据转成驼峰
        $academicPatent = FormatConverter::convertLine($academicPatent);
        // 设置缓存进去
        self::setCache($key, self::RESUME_CACHE_KEY_ACADEMIC_PATENT, $academicPatent);

        return $academicPatent;
    }

    /**
     * 获取简历学术奖励
     * @param int $resumeId
     */
    public static function getAcademicReward(int $resumeId)
    {
        //获取缓存KEY
        $key            = self::getResumeCacheKey($resumeId);
        $academicReward = Cache::hGet($key, self::RESUME_CACHE_KEY_ACADEMIC_REWARD);
        if (!$academicReward) {
            $academicReward = self::setAcademicReward($resumeId);
        } else {
            $academicReward = json_decode($academicReward, true);
        }

        return $academicReward;
    }

    /**
     * 设置简历学术奖励
     * @param int $resumeId
     */
    public static function setAcademicReward(int $resumeId)
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::hDel($key, self::RESUME_CACHE_KEY_ACADEMIC_REWARD);
        //获取简历学术奖励
        $academicReward = BaseResumeAcademicReward::find()
            ->select([
                'id',
                'resume_id',
                'member_id',
                'name',
                'obtain_date',
                'level',
                'role',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseResumeAcademicReward::STATUS_ACTIVE,
            ])
            ->orderBy([
                'obtain_date' => SORT_DESC,
                'id'          => SORT_DESC,
            ])
            ->asArray()
            ->all();
        // 处理一些数据信息

        // 将数据转成驼峰
        $academicReward = FormatConverter::convertLine($academicReward);
        // 设置缓存进去
        self::setCache($key, self::RESUME_CACHE_KEY_ACADEMIC_REWARD, $academicReward);

        return $academicReward;
    }

    /**
     * 获取简历附加信息
     * @param int $resumeId
     */
    public static function getAdditionalInfo(int $resumeId)
    {
        //获取缓存KEY
        $key            = self::getResumeCacheKey($resumeId);
        $additionalInfo = Cache::hGet($key, self::RESUME_CACHE_KEY_ADDITIONAL_INFO);
        if (!$additionalInfo) {
            $additionalInfo = self::setAdditionalInfo($resumeId);
        } else {
            $additionalInfo = json_decode($additionalInfo, true);
        }

        return $additionalInfo;
    }

    /**
     * 设置简历附加信息
     * @param int $resumeId
     */
    public static function setAdditionalInfo(int $resumeId)
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::hDel($key, self::RESUME_CACHE_KEY_ADDITIONAL_INFO);
        //获取简历附加信息
        $additionalInfo = BaseResumeAdditionalInfo::find()
            ->select([
                'id',
                'resume_id',
                'member_id',
                'theme_id',
                'theme_name',
                'content',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseResumeAdditionalInfo::STATUS_ACTIVE,
            ])
            ->asArray()
            ->all();

        // 处理一些数据信息
        foreach ($additionalInfo as &$item) {
            // 主题文案
            $item['themeIdName'] = $item['theme_id'] > 0 ? BaseDictionary::getThemeName($item['theme_id']) : '';
        }

        // 将数据转成驼峰
        $additionalInfo = FormatConverter::convertLine($additionalInfo);
        // 设置缓存进去
        self::setCache($key, self::RESUME_CACHE_KEY_ADDITIONAL_INFO, $additionalInfo);

        return $additionalInfo;
    }

    /**
     * 获取简历资质证书
     * @param int $resumeId
     */
    public static function getCertificate(int $resumeId)
    {
        //获取缓存KEY
        $key         = self::getResumeCacheKey($resumeId);
        $certificate = Cache::hGet($key, self::RESUME_CACHE_KEY_CERTIFICATE);
        if (!$certificate) {
            $certificate = self::setCertificate($resumeId);
        } else {
            $certificate = json_decode($certificate, true);
        }

        return $certificate;
    }

    /**
     * 设置简历资质证书
     * @param int $resumeId
     */
    public static function setCertificate(int $resumeId)
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::hDel($key, self::RESUME_CACHE_KEY_CERTIFICATE);
        //获取简历资质证书
        $certificate = BaseResumeCertificate::find()
            ->select([
                'id',
                'resume_id',
                'member_id',
                'certificate_id',
                'obtain_date',
                'score',
                'certificate_custom',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseResumeCertificate::STATUS_ACTIVE,
            ])
            ->orderBy([
                'obtain_date' => SORT_DESC,
                'id'          => SORT_DESC,
            ])
            ->asArray()
            ->all();
        // 处理一些数据信息
        foreach ($certificate as &$item) {
            // 证书文案
            $item['certificateName'] = $item['certificate_id'] > 0 ? BaseCertificate::getName($item['certificate_id']) : '';
        }

        // 将数据转成驼峰
        $certificate = FormatConverter::convertLine($certificate);
        // 设置缓存进去
        self::setCache($key, self::RESUME_CACHE_KEY_CERTIFICATE, $certificate);

        return $certificate;
    }

    /**
     * 获取简历其他奖励
     * @param int $resumeId
     */
    public static function getOtherReward(int $resumeId)
    {
        //获取缓存KEY
        $key         = self::getResumeCacheKey($resumeId);
        $otherReward = Cache::hGet($key, self::RESUME_CACHE_KEY_OTHER_REWARD);
        if (!$otherReward) {
            $otherReward = self::setOtherReward($resumeId);
        } else {
            $otherReward = json_decode($otherReward, true);
        }

        return $otherReward;
    }

    /**
     * 设置简历其他奖励
     * @param int $resumeId
     */
    public static function setOtherReward(int $resumeId)
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::hDel($key, self::RESUME_CACHE_KEY_OTHER_REWARD);
        //获取简历其他奖励
        $otherReward = BaseResumeOtherReward::find()
            ->select([
                'id',
                'resume_id',
                'member_id',
                'name',
                'obtain_date',
                'level',
                'role',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseResumeOtherReward::STATUS_ACTIVE,
            ])
            ->orderBy([
                'obtain_date' => SORT_DESC,
                'id'          => SORT_DESC,
            ])
            ->asArray()
            ->all();

        // 将数据转成驼峰
        $otherReward = FormatConverter::convertLine($otherReward);
        // 设置缓存进去
        self::setCache($key, self::RESUME_CACHE_KEY_OTHER_REWARD, $otherReward);

        return $otherReward;
    }

    /**
     * 获取简历其他技能
     * @param int $resumeId
     */
    public static function getOtherSkill(int $resumeId)
    {
        //获取缓存KEY
        $key        = self::getResumeCacheKey($resumeId);
        $otherSkill = Cache::hGet($key, self::RESUME_CACHE_KEY_OTHER_SKILL);
        if (!$otherSkill) {
            $otherSkill = self::setOtherSkill($resumeId);
        } else {
            $otherSkill = json_decode($otherSkill, true);
        }

        return $otherSkill;
    }

    /**
     * 设置简历其他技能
     * @param int $resumeId
     */

    public static function setOtherSkill(int $resumeId)
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::hDel($key, self::RESUME_CACHE_KEY_OTHER_SKILL);
        //获取简历其他技能
        $otherSkill = BaseResumeOtherSkill::find()
            ->select([
                'id',
                'resume_id',
                'member_id',
                'name',
                'degree_type',
                'description',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseResumeOtherSkill::STATUS_ACTIVE,
            ])
            ->asArray()
            ->all();
        // 处理一些数据信息
        foreach ($otherSkill as &$item) {
            // 熟练程度文案
            $item['degreeTypeName'] = $item['degree_type'] > 0 ? BaseDictionary::getDegreeTypeName($item['degree_type']) : '';
        }

        // 将数据转成驼峰
        $otherSkill = FormatConverter::convertLine($otherSkill);
        // 设置缓存进去
        self::setCache($key, self::RESUME_CACHE_KEY_OTHER_SKILL, $otherSkill);

        return $otherSkill;
    }

    /**
     * 获取简历研究方向
     * @param int $resumeId
     */
    public static function getResearchDirection(int $resumeId)
    {
        //获取缓存KEY
        $key               = self::getResumeCacheKey($resumeId);
        $researchDirection = Cache::hGet($key, self::RESUME_CACHE_KEY_RESEARCH_DIRECTION);
        if (!$researchDirection) {
            $researchDirection = self::setResearchDirection($resumeId);
        } else {
            $researchDirection = json_decode($researchDirection, true);
        }

        return $researchDirection;
    }

    /**
     * 设置简历研究方向
     * @param int $resumeId
     */
    public static function setResearchDirection(int $resumeId)
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::hDel($key, self::RESUME_CACHE_KEY_RESEARCH_DIRECTION);
        //获取简历研究方向
        $researchDirection = BaseResumeResearchDirection::find()
            ->select([
                'id',
                'resume_id',
                'member_id',
                'content',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseResumeResearchDirection::STATUS_ACTIVE,
            ])
            ->asArray()
            ->one();

        // 将数据转成驼峰
        $researchDirection = FormatConverter::convertLine($researchDirection);
        // 设置缓存进去
        self::setCache($key, self::RESUME_CACHE_KEY_RESEARCH_DIRECTION, $researchDirection);

        return $researchDirection;
    }

    /**
     * 获取简历研究项目
     * @param int $resumeId
     */
    public static function getResearchProject(int $resumeId)
    {
        //获取缓存KEY
        $key             = self::getResumeCacheKey($resumeId);
        $researchProject = Cache::hGet($key, self::RESUME_CACHE_KEY_RESEARCH_PROJECT);
        if (!$researchProject) {
            $researchProject = self::setResearchProject($resumeId);
        } else {
            $researchProject = json_decode($researchProject, true);
        }

        return $researchProject;
    }

    /**
     * 设置简历研究项目
     * @param int $resumeId
     */
    public static function setResearchProject(int $resumeId)
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::hDel($key, self::RESUME_CACHE_KEY_RESEARCH_PROJECT);
        //获取简历研究项目
        $researchProject = BaseResumeResearchProject::find()
            ->select([
                'id',
                'resume_id',
                'member_id',
                'name',
                'category',
                'role',
                'begin_date',
                'end_date',
                'company',
                'description',
                'is_close',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseResumeResearchProject::STATUS_ACTIVE,
            ])
            ->orderBy([
                'end_date' => SORT_DESC,
                'id'       => SORT_DESC,
            ])
            ->asArray()
            ->all();
        // 处理一些数据信息
        foreach ($researchProject as &$item) {
            // 项目类别文案
            $item['categoryName'] = $item['category'] > 0 ? BaseDictionary::getProjectCateName($item['category']) : '';
            // 是否结题文案
            $item['isCloseName'] = $item['is_close'] == 1 ? '是' : '否';
        }

        // 将数据转成驼峰
        $researchProject = FormatConverter::convertLine($researchProject);
        // 设置缓存进去
        self::setCache($key, self::RESUME_CACHE_KEY_RESEARCH_PROJECT, $researchProject);

        return $researchProject;
    }

    /**
     * 获取简历技能/语言
     * @param int $resumeId
     */
    public static function getSkill(int $resumeId)
    {
        //获取缓存KEY
        $key   = self::getResumeCacheKey($resumeId);
        $skill = Cache::hGet($key, self::RESUME_CACHE_KEY_SKILL);
        if (!$skill) {
            $skill = self::setSkill($resumeId);
        } else {
            $skill = json_decode($skill, true);
        }

        return $skill;
    }

    /**
     * 设置简历技能/语言
     * @param int $resumeId
     */
    public static function setSkill(int $resumeId)
    {
        //获取缓存KEY
        $key = self::getResumeCacheKey($resumeId);
        //删除缓存
        Cache::hDel($key, self::RESUME_CACHE_KEY_SKILL);
        //获取简历技能/语言
        $skill = BaseResumeSkill::find()
            ->select([
                'id',
                'resume_id',
                'member_id',
                'skill_id',
                'degree_type',
            ])
            ->where([
                'resume_id' => $resumeId,
                'status'    => BaseResumeSkill::STATUS_ACTIVE,
            ])
            ->asArray()
            ->all();
        // 处理一些数据信息
        foreach ($skill as &$item) {
            // 技能文案
            $item['skillName'] = $item['skill_id'] > 0 ? BaseSkill::getName($item['skill_id']) : '';
            // 熟练程度文案
            $item['degreeTypeName'] = $item['degree_type'] > 0 ? BaseDictionary::getDegreeTypeName($item['degree_type']) : '';
        }
        // 将数据转成驼峰
        $skill = FormatConverter::convertLine($skill);
        // 设置缓存进去
        self::setCache($key, self::RESUME_CACHE_KEY_SKILL, $skill);

        return $skill;
    }

    /**
     * 转换大驼峰
     * @param int $resumeId
     */
    private static function getCamelCase($str, $prefix = ''): string
    {
        $str = strtolower($str);
        $str = str_replace('_', ' ', $str);
        $str = ucwords($str);

        return $prefix . str_replace(' ', '', $str);
    }

    /**
     * 大写转小驼峰
     */
    public static function upperToLowerCamel($str)
    {
        $str = strtolower($str);
        $str = str_replace('_', ' ', $str);
        $str = ucwords($str);

        return lcfirst(str_replace(' ', '', $str));
    }

    /**
     * 设置缓存
     */
    public static function setCache($key, $field, $data)
    {
        Cache::hSet($key, $field, json_encode($data));
        Cache::expire($key, self::RESUME_CACHE_EXPIRE_TIME);
        //Cache::setex($key . '_time', 300, date('Y-m-d H:i:s'));
    }
}