<?php

namespace common\service\meilisearch\job;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseCompany;
use common\base\models\BaseJob;
use common\helpers\DebugHelper;
use common\service\meilisearch\BaseService;

class AddService extends BaseService
{
    private $jobInfo;
    private $jobId;
    private $addInfo;

    public function __construct()
    {
        self::$index = $this->keyInfo['jobCenterBListKey'];
        parent::__construct();
    }

    //    /**
    //     * 提供初始化数据
    //     * @return mixed
    //     */
    //    public static function initData()
    //    {
    //        //获取职位信息
    //        $jobList = BaseJob::find()
    //            ->alias('j')
    //            ->select([
    //                'j.id as jobId',
    //                'j.name as jobName',
    //                'c.full_name as companyName',
    //                'a.title as announcementName',
    //            ])
    //            ->where([
    //                'j.status'  => [
    //                    BaseJob::STATUS_ONLINE,
    //                    BaseJob::STATUS_OFFLINE,
    //                ],
    //                'j.is_show' => BaseJob::IS_SHOW_YES,
    //            ])
    //            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = j.company_id')
    //            ->leftJoin(['a' => BaseAnnouncement::tableName()], 'a.id = j.announcement_id')
    //            //这里初始化要根据自身列表的顺序来放入
    //            //职位刷新时间
    //            ->orderBy('j.refresh_time desc')
    //            ->asArray()
    //            ->all();
    //        //输出日志
    //
    //
    //        return $jobList;
    //    }

    /**
     * 通过职位id添加、编辑meilisearch职位
     * @param $id
     * @return false|void
     */
    public function saveById($id)
    {
        $this->jobId = $id;
        //获取职位信息
        $this->jobInfo = BaseJob::findOne($this->jobId);
        if (empty($this->jobInfo)) {
            //退出
            return false;
        }
        $this->setInfo();
        $this->updateData();
    }

    /**
     * 设置添加数组
     * @return void
     */
    protected function setInfo()
    {
        //暂时需要jobId、jobName、companyName、announcementName个参数
        $this->addInfo['jobId']            = $this->jobInfo->id;
        $this->addInfo['jobName']          = $this->jobInfo->name;
        $this->addInfo['companyName']      = BaseCompany::findOneVal(['id' => $this->jobInfo->company_id], 'full_name');
        $this->addInfo['announcementName'] = BaseAnnouncement::findOneVal(['id' => $this->jobInfo->announcement_id],
            'title');
    }

    /**
     * 处理数据
     * - 删除
     * - 添加
     * - 更新
     */
    protected function updateData()
    {
        //判断职位的状态确认执行的操作
        //获取一下职位的文档信息
        $getIndexInfo = self::getInstance()
            ->getDocument($this->jobId);
        if ($getIndexInfo && in_array($this->jobInfo->status, [
                BaseJob::STATUS_ONLINE,
                BaseJob::STATUS_OFFLINE,
            ])) {
            $this->update();
        } elseif (!$getIndexInfo && in_array($this->jobInfo->status, [
                BaseJob::STATUS_ONLINE,
                BaseJob::STATUS_OFFLINE,
            ])) {
            $this->add();
        } else {
            $this->delete();
        }
    }

    /**
     * 添加
     * @return void
     */
    protected function add()
    {
        $res = self::getInstance()
            ->addDocuments($this->addInfo);
        //输出日志

    }

    /**
     * 更新
     * @return void
     */
    protected function update()
    {
        $res = self::getInstance()
            ->updateDocuments($this->addInfo, 'jobId');
        //输出日志

    }

    /**
     * 删除
     * @return void
     */
    protected function delete()
    {
        $res = self::getInstance()
            ->deleteDocument($this->jobId);
        //输出日志

    }
}