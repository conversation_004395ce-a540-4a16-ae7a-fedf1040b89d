<?php
namespace common\service\engine;

use common\base\BaseActiveRecord;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseArea;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseDictionary;
use common\base\models\BaseEngineSearchRule;
use common\base\models\BaseEngineSearchSource;
use common\base\models\BaseEngineSearchSourceRule;
use common\base\models\BaseEngineSearchStatistics;
use common\base\models\BaseEngineSearchType;
use common\base\models\BaseHomePosition;
use common\base\models\BaseJob;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobCollect;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseSeoHotWordConfig;
use common\base\models\BaseSeoJobWiki;
use common\base\models\BaseShowcase;
use common\base\models\BaseWelfareLabel;
use common\helpers\TimeHelper;
use common\libs\Cache;
use common\service\CommonService;
use queue\Producer;
use Yii;

/**
 * 处理搜索引擎处理sreach的问题
 * Class SearchService
 * @package common\service\engine
 */
class SearchService extends CommonService
{
    /* 搜索引擎路由 */
    const SEARCH_ENGINE_ROUTE = '/rczhaopin';
    /* 站内职位列表 */
    const INNER_JOB_ROUTE = '/job';
    /* 热门关键字 */
    const HOT_WORD_ROUTE = '/hotword';
    /* 职位百科 */
    const JOB_WIKI_ROUTE = '/bk_jobs';
    /* 站内职位筛选参数key-工作地点 */
    const INNER_JOB_SEARCH_AREA_TYPE = 'areaId';
    /* 站内职位筛选参数key-单位类型 */
    const INNER_JOB_SEARCH_COMPANY_TYPE = 'companyType';
    /* 站内职位筛选参数key-学科分类 */
    const INNER_JOB_SEARCH_MAJOR_TYPE = 'majorId';
    /* 站内职位筛选参数key-学历要求 */
    const INNER_JOB_SEARCH_EDUCATION_TYPE = 'educationType';
    /* 站内职位筛选参数key-职位类型 */
    const INNER_JOB_SEARCH_JOB_TYPE = 'jobType';
    /* 对应筛选类型-站内职位筛选参数key */
    const INNER_JOB_SEARCH_KEY = [
        BaseEngineSearchType::SEARCH_TYPE_AREA           => self::INNER_JOB_SEARCH_AREA_TYPE,
        BaseEngineSearchType::SEARCH_TYPE_COMPANY_TYPE   => self::INNER_JOB_SEARCH_COMPANY_TYPE,
        BaseEngineSearchType::SEARCH_TYPE_MAJOR          => self::INNER_JOB_SEARCH_MAJOR_TYPE,
        BaseEngineSearchType::SEARCH_TYPE_EDUCATION_TYPE => self::INNER_JOB_SEARCH_EDUCATION_TYPE,
        BaseEngineSearchType::SEARCH_TYPE_JOB_TYPE       => self::INNER_JOB_SEARCH_JOB_TYPE,
    ];

    /* 重定向方向 */
    /* 重定向到搜索引擎 */
    const REDIRECT_TYPE_ENGINE = 1;
    /* 重定向到站内职位列表 */
    const REDIRECT_TYPE_INNER = 2;
    /* 重定向到404 */
    const REDIRECT_TYPE_404 = 3;
    /* 随机数量 */
    const LIMIT_NUM = 15;
    /* 缓存时间 */
    const ENGINE_RECOMMEND_EXPIRE          = 7776000;//90 * 24 * 3600 90天缓存时间
    const ENGINE_SEARCH_HOT_EXPIRE         = 864000;//热搜职位10天缓存时间
    const ENGINE_SEARCH_EXPIRE_DAY_10      = 864000;//10天缓存时间
    const ENGINE_SEARCH_LIST_EXPIRE        = 86400;//搜索条件缓存时间
    const ENGINE_SEARCH_LIST_EXPIRE_MONTH  = 2592000;//搜索条件缓存时间一个月
    const ENGINE_SEARCH_LIST_EXPIRE_WEEK   = 604800;//搜索条件缓存时间一个周
    const ENGINE_SEARCH_LIST_EXPIRE_HOUR   = 3600;//搜索条件缓存时间一个小时
    const ENGINE_SEARCH_LIST_EXPIRE_MINUTE = 600;//搜索条件缓存时间10分钟
    const ENGINE_JOB_LIST_EXPIRE           = 86400;//职位列表缓存时间

    /* 公共seo TDk */
    private $common_seo = [
        'seo_title'       => '全国高层次人才职位信息中心-高校人才网|高才网',
        'seo_keywords'    => '全国招聘信息,高层次人才招聘',
        'seo_description' => '高校人才网为您提供全国招聘信息，职位信息、薪资待遇、招聘公告、招聘单位等信息一应俱全，欢迎来到高校人才网。',
    ];
    /* 搜索seo TDk */
    private $search_keyword_seo = [
        'seo_title'       => '{p1}招聘_{p1}职位推荐-高校人才网|高才网',
        'seo_keywords'    => '{p1}招聘,{p1}招聘信息',
        'seo_description' => '高校人才网为您提供关于{p1}招聘的相似职位信息，帮助您了解{p1}的薪资待遇、招聘要求，助力求职者找到一份好工作！',
    ];
    /* host */
    private $host;
    /* job_uri */
    private $job_uri;
    /* engine_uri */
    private $engine_uri;
    /* hot word uri  */
    private $hotword_uri;
    /* job wiki uri  */
    private $job_wiki_uri;
    /* 筛选的参数 */
    private $search_params;
    /* 筛选的参数元素条件数组 */
    private $search_params_element;
    /* 保存允许筛选的类型 */
    private $allow_search_type;
    /* 保存需要匹配类型规则 */
    private $search_source_match_rule;
    /* 保存参数所有资源数据 */
    private $all_params_source;
    /* 保存当前URl */
    private $current_url;
    /* 保存当前规则 */
    private $current_rule;
    /* 保存当前规则信息 */
    private $current_rule_info;
    /* 保存当前页码 */
    private $page;
    /* 保存keyword参数 */
    private $keyword_param;

    public function __construct()
    {
        //获取host
        $this->host = Yii::$app->request->hostInfo;
        //获取job_uri
        $this->job_uri = $this->host . self::INNER_JOB_ROUTE . '?';
        //获取engine_uri
        $this->engine_uri = $this->host . self::SEARCH_ENGINE_ROUTE . '/';
        //获取hotword_uri
        $this->hotword_uri = $this->host . self::HOT_WORD_ROUTE . '/';
        //获取job_wiki_uri
        $this->job_wiki_uri = $this->host . self::JOB_WIKI_ROUTE . '/';
        //获取参数
        $this->search_params['level_1'] = Yii::$app->request->get('level1');
        $this->search_params['level_2'] = Yii::$app->request->get('level2');
        $tj                             = Yii::$app->request->get('tj');
        $this->search_params['keyword'] = $tj;
        $this->keyword_param            = $tj ? str_replace('tj_', '', $tj) : '';
        unset($tj);
        //将分页参数塞进去
        $page = Yii::$app->request->get('p');
        if (!empty($page)) {
            $page       = str_replace('p', '', $page);
            $this->page = $page > 0 ? $page : 1;
            //限制最大页码20 大于20去A页面
            if ($this->page > 20) {
                $this->redirect404();
            }
        } else {
            $this->page = 1;
        }
        //这里初始化一下规则二表
        if (!Cache::hvals(Cache::ENGINE_RULE_SOURCE_LIST_KEY)) {
            BaseEngineSearchSourceRule::getRuleSourceList();
        }
        //获取参数元素条件数组
        $this->search_params_element = $this->getSearchParamsElement();
        //获取筛选项类型
        $this->allow_search_type = BaseEngineSearchType::getTypeData();
        //获取需要匹配类型规则
        $this->search_source_match_rule = $this->getRuleChildGroup();
        //获取参数所有资源数据
        $this->all_params_source = BaseEngineSearchSource::getSourceByNameList($this->search_params_element);
        //获取当前URl
        $this->current_url = $this->getCurrentUrl();
        //获取当前规则
        $this->current_rule = $this->getRuleByLevel(array_keys($this->all_params_source));
        //获取规则信息
        if (!empty($this->current_rule['type_level_1'])) {
            $this->current_rule_info = BaseEngineSearchRule::getRuleByLevel($this->current_rule['type_level_1'],
                $this->current_rule['type_level_2']);
        }

        parent::__construct();
    }

    /**
     * 处理搜索引擎处理sreach数据返回
     */
    public function run()
    {
        //推荐参数keyword_param不为空则需要验证改内容是否在职位列表中含有
        //        if (!empty($this->keyword_param)) {
        //            $jonInfo = BaseJob::findOne(['name' => $this->keyword_param]);
        //            if (!$jonInfo->id) {
        //                $this->redirect404();
        //            }
        //        }
        //验证当前参数是否符合规则 不符合则重定向到404
        $verify_result = $this->verifyRouteRule();
        if (!$verify_result) {
            $this->redirect404();
        }
        //做一个大前提判断，如果level1和level2都参数其中有没有获取到资源，那么直接跳转到404
        //如果资源长度与参数长度不等或者当前规则不存在，那么直接跳转到404
        if ((empty($this->current_rule_info) && !empty($this->search_params_element)) || count($this->all_params_source) != count($this->search_params_element)) {
            $this->redirect404();
        }
        //这里就说明当前的访问符合规则 进行记录
        //$this->engineSearchStatisticsTable();
        //如果当前条件存在缓存就直接获取缓存
        $search_cache_key_arr             = $this->search_params;
        $search_cache_key_arr['keyword']  = $this->keyword_param;
        $search_cache_key_arr['platform'] = $this->operationPlatform;
        ksort($search_cache_key_arr);
        $search_cache_key = Cache::ENGINE_SEARCH_LIST . ':' . ($this->operationPlatform) . ':' . md5(http_build_query($search_cache_key_arr));
        $search_data      = Cache::get($search_cache_key);
        if (!$search_data) {
            $search        = [];//搜索引擎的搜索条件
            $search_active = [];//搜索引擎的搜索选中条件
            $search_none   = [];//搜索引擎的数据的全部信息定义
            $search_params = [];//搜索引擎的数据的全部信息定义
            //将筛选塞进数组
            foreach ($this->allow_search_type as $type_value) {
                $search[$type_value['id']] = [
                    'title_id'    => $type_value['id'],
                    'title'       => $type_value['name'],
                    'search_list' => [],
                ];
            }
            //获取搜索选项全部数据，再按照类型分类
            $search_data = BaseEngineSearchSource::getSourceData();
            //循环处理筛选数据
            foreach ($search_data as $search_item) {
                //当前数据与传递参数组合,返回静态数据集合
                $item = $this->searchDataAnalysis($search_item);
                //做选中active属性
                $active         = $this->getActive($search_item);
                $item['active'] = $active;
                //选中的数据
                if ($active) {
                    $search_spell_name = empty($search_item['level_spell_2']) ? $search_item['level_spell_1'] : $search_item['level_spell_2'];
                    //选中资源做取消选中数据结构
                    if (!empty($this->search_params['level_1'])) {
                        $level_1_arr      = explode('_', $this->search_params['level_1']);
                        $level_name_1_arr = array_diff($level_1_arr, [$search_spell_name]);
                        $level_name_1     = implode('_', $level_name_1_arr);
                    }
                    if (!empty($this->search_params['level_2'])) {
                        $level_2_arr      = explode('_', $this->search_params['level_2']);
                        $level_name_2_arr = array_diff($level_2_arr, [$search_spell_name]);
                        $level_name_2     = implode('_', $level_name_2_arr);
                    }
                    $active_url          = rtrim($this->engine_uri . (empty($level_name_1) ? '' : (empty($level_name_2) ? $level_name_1 : $level_name_1 . '/')) . (empty($level_name_2) ? '' : $level_name_2),
                        '/');
                    $active_item         = $item;
                    $active_item['name'] = empty($search_item['level_name_2']) ? $search_item['level_name_1'] : $search_item['level_name_2'];
                    $active_item['id']   = empty($search_item['level_id_2']) ? $search_item['level_id_1'] : $search_item['level_id_2'];
                    $active_item['url']  = $active_url;
                    //push到选中数组
                    array_push($search_active, $active_item);
                    $none_item['active']                                              = true;
                    $none_item['url']                                                 = $active_url;
                    $search_none[self::INNER_JOB_SEARCH_KEY[$search_item['type_id']]] = $none_item;
                    //写入当前条件
                    $search_params[self::INNER_JOB_SEARCH_KEY[$search_item['type_id']]] = empty($search_item['level_id_2']) ? $search_item['level_id_1'] : $search_item['level_id_2'];;
                }
                //筛选项数据结构重组
                if ($search_item['level_id_1'] > 0 && $search_item['level_id_2'] > 0) {
                    //如果是二级筛选，组长两级数据
                    if (!isset($search[$search_item['type_id']]['search_list'][$search_item['level_id_1']])) {
                        $search_level_1                                                             = [
                            'url'        => '',
                            'name'       => $search_item['level_name_1'],
                            'id'         => $search_item['level_id_1'],
                            'child'      => [],
                            'active'     => false,
                            'is_hot'     => $search_item['is_hot'] == BaseEngineSearchSource::IS_HOT_YES,
                            'level_tag'  => $search_item['level_tag'],
                            'sort_num_1' => $search_item['sort_num_1'],
                        ];
                        $search[$search_item['type_id']]['search_list'][$search_item['level_id_1']] = $search_level_1;
                    }
                    if (isset($search[$search_item['type_id']]['search_list'][$search_item['level_id_1']]['child'])) {
                        //如果是二级筛选,切实选中的那么将一级塞一个选中状态进去
                        if ($search[$search_item['type_id']]['search_list'][$search_item['level_id_1']]['active'] == false && $item['active'] == true) {
                            $search[$search_item['type_id']]['search_list'][$search_item['level_id_1']]['active'] = $item['active'];
                        }
                        $item['name']       = $search_item['level_name_2'];
                        $item['id']         = $search_item['level_id_2'];
                        $item['level_tag']  = $search_item['level_tag'];
                        $item['sort_num_1'] = $search_item['sort_num_1'];
                        $item['is_hot']     = $search_item['is_hot'] == BaseEngineSearchSource::IS_HOT_YES;
                        array_push($search[$search_item['type_id']]['search_list'][$search_item['level_id_1']]['child'],
                            $item);
                    }
                } else {
                    $item['name']       = $search_item['level_name_1'];
                    $item['id']         = $search_item['level_id_1'];
                    $item['level_tag']  = $search_item['level_tag'];
                    $item['sort_num_1'] = $search_item['sort_num_1'];
                    $item['is_hot']     = $search_item['is_hot'] == BaseEngineSearchSource::IS_HOT_YES;
                    array_push($search[$search_item['type_id']]['search_list'], $item);
                }
            }
            //处理索引KEY
            foreach ($search as $key => $value) {
                $search[self::INNER_JOB_SEARCH_KEY[$key]] = $value;
                unset($search[$key]);
            }
            $cache_data = [
                'search_active' => $search_active,
                'search'        => $search,
                'search_params' => $search_params,
                'search_none'   => $search_none,
            ];
            //这里做一个时间加权，根据现有访问量进行加权
            //加权规则
            //            Cache::setex($search_cache_key, $this->weightedAlgorithm(), json_encode($cache_data));
            Cache::setex($search_cache_key, $this->cacheTime($search_cache_key_arr), json_encode($cache_data));
        } else {
            $search_data   = json_decode($search_data, true);
            $search_active = $search_data['search_active'] ?? [];
            $search        = $search_data['search'] ?? [];
            $search_params = $search_data['search_params'] ?? [];
            $search_none   = $search_data['search_none'] ?? [];
        }

        //面包屑导航数据
        $breadcrumb_navigation = $this->getBreadcrumbNavigation();
        //筛选各项特殊处理
        $special_item_data   = $this->getSpecialItem($search_none);
        $search_special_item = $special_item_data['search_special_item'];
        $position_data       = $special_item_data['position_data'];
        //获取seo信息
        //判断一下是否含有搜索参数
        if (empty($this->keyword_param)) {
            $seo_data = $this->getSeoData();
        } else {
            $seo_name        = '';
            $params_type_arr = array_keys($this->all_params_source);
            if (in_array(BaseEngineSearchType::SEARCH_TYPE_AREA, $params_type_arr)) {
                $area_info = $this->all_params_source[BaseEngineSearchType::SEARCH_TYPE_AREA];
                $seo_name  .= $area_info['level_name_1'];
            }
            $seo_name .= $this->keyword_param;

            $seo_data = [
                'title'       => $this->textReplace($this->search_keyword_seo['seo_title'], $seo_name),
                'keywords'    => $this->textReplace($this->search_keyword_seo['seo_keywords'], $seo_name),
                'description' => $this->textReplace($this->search_keyword_seo['seo_description'], $seo_name),
            ];
        }

        //获取职位列表或者推荐列表
        $job_list = $this->getJobData();
        //定义返回数组
        $result = [
            'search_active'         => $search_active,
            'search'                => $search,
            'search_special_item'   => $search_special_item,
            'breadcrumb_navigation' => $breadcrumb_navigation,
            'job_data'              => $job_list['job_data'],
            'is_recommend'          => $job_list['is_recommend'],
            'tj_data'               => $job_list['tj_data'],
            'current_url'           => $this->current_url,
            'position_data'         => $position_data,
            'seo_data'              => $seo_data,
            'search_params'         => json_encode($search_params),
        ];
        if ($this->operationPlatform == self::PLATFORM_WEB) {
            //获取热门岗位
            $post_hot = $this->getCategoryData();
            //获取热门职位
            $job_hot = $this->getNearbyJobData();
            //热搜职位
            $search_hot_job = $this->getSearchHotData();
            //热门关键词
            $hot_word = $this->getHotWordData();
            //职位百科
            $job_wiki = $this->getWikiData();
            //分页Html
            $p                    = $job_list['job_data']['p'];
            $totalNum             = $job_list['job_data']['totalNum'];
            $page_html            = $this->pageHtml($totalNum, $p);
            $result['page_html']  = $page_html;
            $result['post_hot']   = $post_hot;
            $result['job_hot']    = $job_hot;
            $result['search_hot'] = $search_hot_job;
            $result['hot_word']   = $hot_word;
            $result['job_wiki']   = $job_wiki;
        }

        return $result;
    }

    /**
     * 返回热门岗位与热门职位
     * 只用在A页面（站内调用）
     * @param array $params
     */
    public function runHotData($params)
    {
        //获取热门岗位
        $post_hot = $this->getCategoryData($params);
        //获取热门职位
        $job_hot = $this->getNearbyJobData($params);

        return [
            'post_hot' => $post_hot,
            'job_hot'  => $job_hot,
        ];
    }

    /**
     * 获取职位列表----用于API返回使用
     */
    public function runJobData()
    {
        //获取职位列表或者推荐列表
        $job_list = $this->getJobData();

        return $job_list['job_data'];
    }

    /**
     * 根据规则进行数据分析返回特定结构数据
     * @param $search_item
     * @return array
     */
    private function searchDataAnalysis($search_item)
    {
        //获取当前参数的资源数据
        $source_list = $this->all_params_source;
        //加入当前数据
        $source_list[$search_item['type_id']] = $search_item;
        //获取资源的类型
        $params_source_type = array_keys($source_list);
        $level_type         = $this->getRuleByLevel($params_source_type);
        //获取现有的规则数据
        $rule_info = BaseEngineSearchRule::getRuleByLevel($level_type['type_level_1'], $level_type['type_level_2']);
        //判断是否需要进项规则二判断
        $rule_bool_child = $this->isAllowMatch($params_source_type);
        //默认情况下已经符合规则一,如果不需要进行规则二判断则不需要进行规则二判断
        $rule_two_bool = true;
        //需要进行规则二判断,将影响规则二的布尔值
        if ($rule_bool_child) {
            $rule_two_bool = $this->sourceRuleMatch($source_list, $rule_info['is_match']);
        }
        //判断时候含有当前规则
        if ($rule_info && $rule_two_bool) {
            //拿到规则则符合规则
            //根据获取对应的资源数据
            $url = $this->engine_uri;
            if ($rule_info['rule_level_1']) {
                $rule_level_1_arr     = explode(',', $rule_info['rule_level_1']);
                $url_name_level_1_arr = [];
                foreach ($rule_level_1_arr as $rule_level_1_item) {
                    array_push($url_name_level_1_arr,
                        empty($source_list[$rule_level_1_item]['level_spell_2']) ? $source_list[$rule_level_1_item]['level_spell_1'] : $source_list[$rule_level_1_item]['level_spell_2']);
                }
                $url .= implode('_', $url_name_level_1_arr) . '/';
            }
            if ($rule_info['rule_level_2']) {
                $rule_level_2_arr     = explode(',', $rule_info['rule_level_2']);
                $url_name_level_2_arr = [];
                foreach ($rule_level_2_arr as $rule_level_2_item) {
                    array_push($url_name_level_2_arr,
                        empty($source_list[$rule_level_2_item]['level_spell_2']) ? $source_list[$rule_level_2_item]['level_spell_1'] : $source_list[$rule_level_2_item]['level_spell_2']);
                }
                $url .= implode('_', $url_name_level_2_arr);
            }
            $url = rtrim($url, '/');
        } else {
            $redirect_params = [];
            foreach ($source_list as $source_item) {
                $redirect_params[self::INNER_JOB_SEARCH_KEY[$source_item['type_id']]] = $source_item['level_id_2'] <= 0 ? $source_item['level_id_1'] : $source_item['level_id_2'];
            }
            $url = $this->job_uri . http_build_query($redirect_params);
            //            $redirect_type = self::REDIRECT_TYPE_INNER;
        }

        //构造返回数组
        $result['url'] = $url ?? '';
        //        $result['redirect_params'] = $redirect_params ?? [];
        //        $result['redirect_type'] = $redirect_type ?? self::REDIRECT_TYPE_ENGINE;

        return $result;
    }

    /**
     * 获取职位列表数据及是否推荐
     * @return array
     */
    private function getJobData()
    {
        //组装一下参数
        $job_params = $this->getJobParams();
        //获取职位列表数据
        $job_params_all = $job_params;
        //将分页参数塞进去
        $job_params_all['p'] = $this->page;
        //判断keyword_param参数是否为空
        if (!empty($this->keyword_param)) {
            $job_params_all['keyword'] = $this->keyword_param;
        }
        //平台类型
        //        switch ($this->operationPlatform) {
        //            case self::PLATFORM_H5:
        //                $type = BaseService::TYPE_H5_JOB_LIST;
        //                break;
        //            case self::PLATFORM_WEB:
        //            default:
        //                $type = BaseService::TYPE_PC_JOB_LIST;;
        //                break;
        //        }
        //                $job_data              = (new PcJobListService())->run($job_params_all, $type);
        $job_data            = self::getJobList($job_params_all);
        $job_data['p']       = $job_params_all['p'];
        $job_data['keyword'] = $job_params_all['keyword'] ?? '';
        if (empty($job_data['list']) && $job_data['p'] == 1) {
            $job_data['totalNum'] = 0;
        }
        //定义一下推荐的数据源
        $recommend_data = [];
        $is_recommend   = false;
        //PC端数据需要推荐数据 H5端不需要
        if ($this->operationPlatform == self::PLATFORM_WEB && empty($job_data['list']) && $job_data['p'] == 1) {
            $is_recommend       = true;
            $recommend_all_data = [];
            //获取推荐职位
            foreach ($job_params as $key => $value) {
                //                $res_data = (new PcJobListService())->run([$key => $value], $type);
                $res_data = self::getJobList([$key => $value]);
                if (!empty($res_data['list']) && count($recommend_all_data) < 20) {
                    $recommend_all_data = array_merge($recommend_all_data, $res_data['list']);
                }
            }
            //这里为空 那么当成条件为空去回职位数量
            if (empty($recommend_all_data) || count($recommend_all_data) < 20) {
                //                $res_data           = (new PcJobListService())->run([], $type);
                $res_data           = self::getJobList();
                $recommend_all_data = array_merge($recommend_all_data, $res_data['list']);
            }
            $recommend_data['list']     = array_slice($recommend_all_data, 0, 20);
            $recommend_data['totalNum'] = 0;
            $recommend_data['p']        = 0;
            $job_data                   = $recommend_data;
        }
        //判断是PC端且不是推荐数据且数据不为空推荐一下第一条职位数据
        $tj_data = [];
        if ($this->operationPlatform == self::PLATFORM_WEB && !$is_recommend && !empty($job_data['list']) && $this->keyword_param == '') {
            //看看参数level1是否是地区
            //组装条件
            $search          = [];
            $tj_name         = '';
            $url_bool        = true;
            $is_area         = false;
            $tj_url          = $this->engine_uri;
            $params_type_arr = array_keys($this->all_params_source);
            if (in_array(BaseEngineSearchType::SEARCH_TYPE_AREA, $params_type_arr)) {
                $area_info = $this->all_params_source[BaseEngineSearchType::SEARCH_TYPE_AREA];
                $tj_url    .= $area_info['level_spell_1'] . '/';
                $tj_name   .= $area_info['level_name_1'];
                $is_area   = true;
                //地区条件
                $search[self::INNER_JOB_SEARCH_KEY[BaseEngineSearchType::SEARCH_TYPE_AREA]] = $area_info['level_id_1'];
            }
            //第一个职位信息
            $recommend_source_info = $job_data['list'][0];
            $search['keyword']     = $recommend_source_info['jobName'];
            $tj_url                .= 'tj_' . $recommend_source_info['jobName'];
            $tj_name               .= $recommend_source_info['jobName'];
            //            $recommend_source_data = (new PcJobListService())->run($search, $type);
            $recommend_source_data = self::getJobList($search);
            $tj_list               = $recommend_source_data['list'];
            //专享推荐列表小于5条就不成静态链接
            if (count($tj_list) < 5) {
                $tj_url   = '';
                $tj_name  = '';
                $url_bool = false;
            }
            //取五条数据用作展示
            $tj_list = array_slice($tj_list, 0, 5);
            $tj_data = [
                'tj_url'   => $tj_url,
                'tj_name'  => $tj_name,
                'url_bool' => $url_bool,
                'tj_list'  => $tj_list,
            ];
            //前提条件1、推荐推荐列表大于5条静态链接 2、职位名称不含有数字且不含有特殊符号
            if ($url_bool && !preg_match('/-|=|\d|\[|]|【|】|,|，|、|（|）|\(|\)|@|!|！|`|#|&|%|\$|\*|\^|;|:|：|\'|"|。|\./',
                    $tj_name)) {
                $item_cache = [
                    'url'      => $tj_url,
                    'name'     => $tj_name . '招聘',
                    'job_name' => $tj_name,
                ];
                if ($is_area) {
                    //做个长度100的redis队列-含有地区
                    $redis_key            = Cache::ENGINE_SEARCH_HOT_AREA_QUEUE;
                    $redis_job_key_prefix = Cache::ENGINE_SEARCH_HOT_AREA_JOB_QUEUE;
                } else {
                    //做个长度100的redis队列-不含地区
                    $redis_key            = Cache::ENGINE_SEARCH_HOT_QUEUE;
                    $redis_job_key_prefix = Cache::ENGINE_SEARCH_HOT_JOB_QUEUE;
                }
                $redis_job_key = $redis_job_key_prefix . ':' . md5($tj_name);
                if (Cache::lLen($redis_key) >= 100) {
                    $pop_cache        = Cache::rPop($redis_key);
                    $pop_data         = json_decode($pop_cache, true);
                    $delete_cache_key = $redis_job_key_prefix . ':' . md5($pop_data['job_name']);
                    Cache::delete($delete_cache_key);
                }
                if (!Cache::get($redis_job_key)) {
                    Cache::lPush($redis_key, json_encode($item_cache));
                    Cache::set($redis_job_key, 1);
                }
            }
        }

        $job_data['level1'] = $this->search_params['level_1'] ?? '';
        $job_data['level2'] = $this->search_params['level_2'] ?? '';
        $job_data['tj']     = $this->keyword_param ?? '';

        return [
            'job_data'     => $job_data,
            'is_recommend' => $is_recommend,
            'tj_data'      => $tj_data,
        ];
    }

    /**
     * 获取当前URl
     * @return string
     */
    private function getCurrentUrl()
    {
        $current_url = $this->engine_uri;
        foreach ($this->search_params as $value) {
            if (!empty($value)) {
                $current_url .= $value . '/';
            }
        }

        return rtrim($current_url, '/');
    }

    /**
     * 获取面包屑导航数据
     * @return array
     */
    private function getBreadcrumbNavigation()
    {
        $result = [];
        //push面包屑一级 高校人才网
        $website_item = [
            'name' => '高校人才网',
            'url'  => $this->host,
        ];
        array_push($result, $website_item);
        //push面包屑二级 职位中心
        $job_item = [
            'name' => '职位中心',
            'url'  => trim($this->engine_uri, '/'),
        ];
        array_push($result, $job_item);
        //push面包屑三级及四级
        //根据当前参数资源判断
        $source_list     = $this->all_params_source;
        $source_type_arr = array_column($source_list, 'type_id');
        //1、是否同时含有地区与职位类型
        //2、是否只含有地区
        //3、是否只含有职位类型
        if (in_array(BaseEngineSearchType::SEARCH_TYPE_AREA,
                $source_type_arr) && in_array(BaseEngineSearchType::SEARCH_TYPE_JOB_TYPE, $source_type_arr)) {
            //push面包屑三级 地区
            $source_list_area_info = $source_list[BaseEngineSearchType::SEARCH_TYPE_AREA];
            $area_name             = empty($source_list_area_info['level_name_2']) ? $source_list_area_info['level_name_1'] : $source_list_area_info['level_name_2'];
            $area_url_name         = empty($source_list_area_info['level_spell_2']) ? $source_list_area_info['level_spell_1'] : $source_list_area_info['level_spell_2'];
            $area_item             = [
                'name' => $area_name . '招聘',
                'url'  => $this->engine_uri . $area_url_name,
            ];
            array_push($result, $area_item);
            //push面包屑四级 职位类型
            $source_list_job_type_info = $source_list[BaseEngineSearchType::SEARCH_TYPE_JOB_TYPE];
            $job_type_name             = empty($source_list_job_type_info['level_name_2']) ? $source_list_job_type_info['level_name_1'] : $source_list_job_type_info['level_name_2'];
            $job_type_item             = [
                'name' => $area_name . $job_type_name . '招聘',
                'url'  => '',
            ];
            array_push($result, $job_type_item);
        } elseif (in_array(BaseEngineSearchType::SEARCH_TYPE_AREA, $source_type_arr)) {//地区
            $source_list_area_info = $source_list[BaseEngineSearchType::SEARCH_TYPE_AREA];
            $area_name             = empty($source_list_area_info['level_name_2']) ? $source_list_area_info['level_name_1'] : $source_list_area_info['level_name_2'];
            if ($this->keyword_param) {
                //push面包屑三级 地区
                $area_url_name = empty($source_list_area_info['level_spell_2']) ? $source_list_area_info['level_spell_1'] : $source_list_area_info['level_spell_2'];
                $area_item     = [
                    'name' => $area_name . '招聘',
                    'url'  => $this->engine_uri . $area_url_name,
                ];
                array_push($result, $area_item);
                //push面包屑四级 职位名称
                $job_name_item = [
                    'name' => $area_name . $this->keyword_param . '招聘',
                    'url'  => '',
                ];
                array_push($result, $job_name_item);
            } else {
                //push面包屑三级 地区
                $area_item = [
                    'name' => $area_name . '招聘',
                    'url'  => '',
                ];
                array_push($result, $area_item);
            }
        } elseif (in_array(BaseEngineSearchType::SEARCH_TYPE_JOB_TYPE, $source_type_arr)) {//职位类型
            //push面包屑三级 职位类型
            $source_list_job_type_info = $source_list[BaseEngineSearchType::SEARCH_TYPE_JOB_TYPE];
            $job_type_name             = empty($source_list_job_type_info['level_name_2']) ? $source_list_job_type_info['level_name_1'] : $source_list_job_type_info['level_name_2'];
            $job_type_item             = [
                'name' => $job_type_name . '招聘',
                'url'  => '',
            ];
            array_push($result, $job_type_item);
        } else {
            //push面包屑三级 职位名称
            if ($this->keyword_param) {
                $job_name_item = [
                    'name' => $this->keyword_param . '招聘',
                    'url'  => '',
                ];
                array_push($result, $job_name_item);
            }
        }

        return $result;
    }

    /**
     * 获取各条件类型的特殊项
     * @return array
     */
    private function getSpecialItem($search_none)
    {
        $search_special_item = [];//保存上选项的特殊处理项
        switch ($this->operationPlatform) {
            case self::PLATFORM_WEB:
            default:
                $name = '全部';
                //PC才需要 获取广告位数据
                $position_data = $this->getPositionData();
                break;
            case self::PLATFORM_H5:
                $name          = '不限';
                $position_data = [];
                break;
        }
        //地区
        if (isset($search_none[self::INNER_JOB_SEARCH_AREA_TYPE]) && $search_none[self::INNER_JOB_SEARCH_AREA_TYPE]['active'] == true) {
            $area_active = false;
            $area_url    = $search_none[self::INNER_JOB_SEARCH_AREA_TYPE]['url'];
        } else {
            $area_active = true;
            $area_url    = $this->current_url;
        }
        $area_item                                             = [
            'name'   => $name,
            'id'     => 0,
            'active' => $area_active,
            'url'    => $area_url,
        ];
        $search_special_item[self::INNER_JOB_SEARCH_AREA_TYPE] = $area_item;
        //职位类型
        if (isset($search_none[self::INNER_JOB_SEARCH_JOB_TYPE]) && $search_none[self::INNER_JOB_SEARCH_JOB_TYPE]['active'] == true) {
            $job_active = false;
            $job_url    = $search_none[self::INNER_JOB_SEARCH_JOB_TYPE]['url'];
        } else {
            $job_active = true;
            $job_url    = $this->current_url;
        }
        $job_item                                             = [
            'name'   => $name,
            'id'     => 0,
            'active' => $job_active,
            'url'    => $job_url,
        ];
        $search_special_item[self::INNER_JOB_SEARCH_JOB_TYPE] = $job_item;
        //学历
        if (isset($search_none[self::INNER_JOB_SEARCH_EDUCATION_TYPE]) && $search_none[self::INNER_JOB_SEARCH_EDUCATION_TYPE]['active'] == true) {
            $education_active = true;
            $education_url    = $search_none[self::INNER_JOB_SEARCH_EDUCATION_TYPE]['url'];
        } else {
            $education_active = false;
            $education_url    = $this->current_url;
        }
        $education_item                                             = [
            'name'   => $name,
            'id'     => 0,
            'active' => $education_active,
            'url'    => $education_url,
        ];
        $search_special_item[self::INNER_JOB_SEARCH_EDUCATION_TYPE] = $education_item;
        //单位类型
        if (isset($search_none[self::INNER_JOB_SEARCH_COMPANY_TYPE]) && $search_none[self::INNER_JOB_SEARCH_COMPANY_TYPE]['active'] == true) {
            $company_active = true;
            $company_url    = $search_none[self::INNER_JOB_SEARCH_COMPANY_TYPE]['url'];
        } else {
            $company_active = false;
            $company_url    = $this->current_url;
        }
        $company_item                                             = [
            'name'   => $name,
            'id'     => 0,
            'active' => $company_active,
            'url'    => $company_url,
        ];
        $search_special_item[self::INNER_JOB_SEARCH_COMPANY_TYPE] = $company_item;
        //专业
        if (isset($search_none[self::INNER_JOB_SEARCH_MAJOR_TYPE]) && $search_none[self::INNER_JOB_SEARCH_MAJOR_TYPE]['active'] == true) {
            $major_active = true;
            $major_url    = $search_none[self::INNER_JOB_SEARCH_MAJOR_TYPE]['url'];
        } else {
            $major_active = false;
            $major_url    = $this->current_url;
        }
        $major_item                                             = [
            'name'   => $name,
            'id'     => 0,
            'active' => $major_active,
            'url'    => $major_url,
        ];
        $search_special_item[self::INNER_JOB_SEARCH_MAJOR_TYPE] = $major_item;

        return [
            'search_special_item' => $search_special_item,
            'position_data'       => $position_data,
        ];
    }

    /**
     * 根据参数确认是否是选中的状态
     * @param $search_item
     * @return bool
     */
    private function getActive($search_item)
    {
        $params = [];
        if ($this->search_params['level_1']) {
            $level_1_arr = explode('_', $this->search_params['level_1']);
            //合并
            $params = array_merge($params, $level_1_arr);
        }
        if ($this->search_params['level_2']) {
            $level_2_arr = explode('_', $this->search_params['level_2']);
            //合并
            $params = array_merge($params, $level_2_arr);
        }
        //判断当前资源是否在参数里面
        $spell_name = empty($search_item['level_spell_2']) ? $search_item['level_spell_1'] : $search_item['level_spell_2'];
        if (in_array($spell_name, $params)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取SEO头部数据
     */
    private function getSeoData()
    {
        //获取当前参数的资源数据
        $source_list = $this->all_params_source;
        //获取当前参数的规则信息
        $rule_info        = $this->current_rule_info;
        $replace_p1       = '';
        $replace_p2       = '';
        $rule_level_1_arr = empty($rule_info['rule_level_1']) ? [] : explode(',', $rule_info['rule_level_1']);
        $rule_level_2_arr = empty($rule_info['rule_level_2']) ? [] : explode(',', $rule_info['rule_level_2']);
        foreach ($rule_level_1_arr as $item1) {
            $item_info  = $source_list[$item1];
            $replace_p1 .= empty($item_info['level_name_2']) ? $item_info['level_name_1'] : $item_info['level_name_2'];
            if ($item1 == BaseEngineSearchType::SEARCH_TYPE_JOB_TYPE) {
                $replace_p2 .= $item_info['level_name_1'];
            }
        }
        foreach ($rule_level_2_arr as $item2) {
            $item_info  = $source_list[$item2];
            $replace_p1 .= empty($item_info['level_name_2']) ? $item_info['level_name_1'] : $item_info['level_name_2'];
            if ($item2 == BaseEngineSearchType::SEARCH_TYPE_JOB_TYPE) {
                $replace_p2 .= $item_info['level_name_1'];
            }
        }
        //替换文本
        $replace_arr = [];
        if (!empty($replace_p1)) {
            array_push($replace_arr, $replace_p1);
        }
        if (!empty($replace_p2)) {
            array_push($replace_arr, $replace_p2);
        }
        $seo_title       = empty($rule_info['seo_title']) ? $this->common_seo['seo_title'] : $rule_info['seo_title'];
        $seo_keywords    = empty($rule_info['seo_keywords']) ? $this->common_seo['seo_keywords'] : $rule_info['seo_keywords'];
        $seo_description = empty($rule_info['seo_description']) ? $this->common_seo['seo_description'] : $rule_info['seo_description'];

        //处理年份的替换
        $seo_title       = $this->yearTimeReplace($seo_title);
        $seo_keywords    = $this->yearTimeReplace($seo_keywords);
        $seo_description = $this->yearTimeReplace($seo_description);
        //变量文本的替换
        $title       = $this->textReplace($seo_title, $replace_arr);
        $keywords    = $this->textReplace($seo_keywords, $replace_arr);
        $description = $this->textReplace($seo_description, $replace_arr);

        return [
            'title'       => $title,
            'keywords'    => $keywords,
            'description' => $description,
        ];
    }

    /**
     * 获取当前规则
     * @param $type_ids
     * @return array|bool
     */
    private static function getRuleByLevel($type_ids)
    {
        $res = [
            'type_level_1' => '',
            'type_level_2' => '',
        ];
        if (count($type_ids) <= 0) {
            return $res;
        }
        $type_level_1 = [];
        $type_level_2 = [];
        if (in_array(BaseEngineSearchType::SEARCH_TYPE_AREA, $type_ids)) {
            array_push($type_level_1, BaseEngineSearchType::SEARCH_TYPE_AREA);
            $type_level_2 = array_diff($type_ids, $type_level_1);
            sort($type_level_2);
        } else {
            $type_level_1 = $type_ids;
            sort($type_level_1);
        }
        if (count($type_level_1) > 0) {
            $res['type_level_1'] = implode(',', $type_level_1);
        }
        if (count($type_level_2) > 0) {
            $res['type_level_2'] = implode(',', $type_level_2);
        }

        return $res;
    }

    /**
     * 判断条件类型是否是需要进行规则二验证
     * @param $type_ids
     * @return bool
     */
    private function isAllowMatch($type_ids)
    {
        sort($type_ids);
        //判断当前资源类型集合是否需要进行规则二的验证
        foreach ($this->search_source_match_rule as $allow_match_type_item) {
            if (count(array_intersect($type_ids, $allow_match_type_item)) == count($allow_match_type_item)) {
                //当前资源类型集合在在规则二中需要进行进一步二级规则验证
                return true;
            }
        }

        //当前二级判断验证不需要在匹配规则二
        return false;
    }

    /**
     * 判断资源是否符合规则二
     * @param $source_list
     * @return bool
     */
    private function sourceRuleMatch($source_list, $is_match = 1)
    {
        //获取资源在规则二中的匹配结果
        //$rule_list = BaseEngineSearchSourceRule::getRuleSourceInfo($source_ids);
        foreach ($this->search_source_match_rule as $item) {
            if (isset($source_list[$item[0]]) && isset($source_list[$item[1]])) {
                $key_arr   = [
                    $source_list[$item[0]]['type_id'],
                    $source_list[$item[0]]['id'],
                    $source_list[$item[1]]['type_id'],
                    $source_list[$item[1]]['id'],
                ];
                $key       = implode('_', $key_arr);
                $rule_list = Cache::hGet(Cache::ENGINE_RULE_SOURCE_LIST_KEY, $key);
                if ($rule_list) {
                    $rule_list = json_decode($rule_list, true);
                }
            }
        }
        if (!empty($rule_list)) {
            if ($rule_list['type'] == BaseEngineSearchSourceRule::IS_MATCH_NO) {
                //不匹配
                return false;
            } else {
                //匹配
                return true;
            }
        } else {//资源规则不存在 看是允许匹配
            if ($is_match == 1) {
                return true;//允许匹配
            } else {
                return false;//不允许匹配
            }
        }
    }

    /**
     * 获取参数条件元素数组
     * @return array
     */
    private function getSearchParamsElement()
    {
        $search_params         = $this->search_params;
        $search_params_element = [];
        if ($search_params['level_1']) {
            $level_1_arr           = explode('_', $search_params['level_1']);
            $search_params_element = array_merge($search_params_element, $level_1_arr);
        }
        if ($search_params['level_2']) {
            $level_2_arr           = explode('_', $search_params['level_2']);
            $search_params_element = array_merge($search_params_element, $level_2_arr);
        }

        return $search_params_element;
    }

    /**
     * 获取规则二的分组
     * 类型一正序排序
     */
    private function getRuleChildGroup()
    {
        $source_rule_list = BaseEngineSearchSourceRule::getRuleGroupList();
        $rule_child_group = [];
        foreach ($source_rule_list as $item) {
            $item_arr = [
                $item['source_type_1'],
                $item['source_type_2'],
            ];
            sort($item_arr);
            array_push($rule_child_group, $item_arr);
        }

        return $rule_child_group;
    }

    /**
     * 返当前参数的职位参数
     */
    private function getJobParams()
    {
        $job_params = [];
        foreach ($this->all_params_source as $item) {
            $job_params[self::INNER_JOB_SEARCH_KEY[$item['type_id']]] = $item['level_id_2'] <= 0 ? $item['level_id_1'] : $item['level_id_2'];
        }

        return $job_params;
    }

    /**
     * 推荐热门岗位
     * @param array $cache_params
     */
    private function getCategoryData($cache_params = [])
    {
        $cache_key = Cache::ENGINE_RECOMMEND_CATEGORY;
        if (!empty($cache_params)) {
            $cache_key = $cache_key . '_' . implode('_', $cache_params);
        }
        //获取职位类型资源
        $job_source = BaseEngineSearchSource::getSourceByType(BaseEngineSearchType::SEARCH_TYPE_JOB_TYPE);
        //保存参数资源类型
        $search_params_type_ids = array_column($this->all_params_source, 'type_id');
        $area_source            = [];
        if (in_array(BaseEngineSearchType::SEARCH_TYPE_AREA, $search_params_type_ids)) {
            // 含有地区类型
            //获取地区类型的资源
            $area_source = $this->all_params_source[BaseEngineSearchType::SEARCH_TYPE_AREA];
        }
        //参数接收并且排序
        $params = $this->search_params_element;
        if (count($params) > 0) {
            sort($params);
            $cache_key .= '_' . implode('_', $params);
        }
        //缓存数据
        $data = Cache::get($cache_key);
        if ($data) {
            return json_decode($data, true);
        }

        //职位类型资源
        if (count($job_source) > self::LIMIT_NUM) {
            $job_rand_key_arr = array_rand($job_source, self::LIMIT_NUM);
            $job_rand_arr     = [];
            foreach ($job_rand_key_arr as $key) {
                $job_rand_arr[] = $job_source[$key];
            }
        } else {
            $job_rand_arr = $job_source;
        }
        //返回数据
        $data = [];
        //循环职位类型资源
        foreach ($job_rand_arr as $value) {
            $url  = $this->engine_uri;
            $name = '';
            if (!empty($area_source)) {
                $url  .= (empty($area_source['level_spell_2']) ? $area_source['level_spell_1'] : $area_source['level_spell_2']) . '/';
                $name .= empty($area_source['level_name_2']) ? $area_source['level_name_1'] : $area_source['level_name_2'];
            }
            $url  .= empty($value['level_spell_2']) ? $value['level_spell_1'] : $value['level_spell_2'];
            $name .= empty($value['level_name_2']) ? $value['level_name_1'] : $value['level_name_2'];
            $name .= '招聘';
            $item = [
                'url'  => $url,
                'name' => $name,
            ];
            array_push($data, $item);
        }
        Cache::set($cache_key, json_encode($data), self::ENGINE_SEARCH_EXPIRE_DAY_10);

        return $data;
    }

    /**
     * 推荐附近职位
     * @param array $params
     */
    private function getNearbyJobData($cache_params = [])
    {
        $cache_key = Cache::ENGINE_RECOMMEND_JOB;
        if (!empty($cache_params)) {
            $cache_key = $cache_key . '_' . implode('_', $cache_params);
        }
        //保存参数资源类型
        $search_params_type_ids = array_column($this->all_params_source, 'type_id');
        //获取地区类型资源
        $area_source = BaseEngineSearchSource::getSourceByType(BaseEngineSearchType::SEARCH_TYPE_AREA);
        //判断参数
        $job_source = [];
        if (in_array(BaseEngineSearchType::SEARCH_TYPE_JOB_TYPE, $search_params_type_ids)) {
            // 含有职位类型
            //获取职位类型的资源
            $job_source = $this->all_params_source[BaseEngineSearchType::SEARCH_TYPE_JOB_TYPE];
        }
        //参数接收并且排序
        $params = $this->search_params_element;
        if (count($params) > 0) {
            sort($params);
            $cache_key .= '_' . implode('_', $params);
        }
        //缓存数据
        $data = Cache::get($cache_key);
        if ($data) {
            return json_decode($data, true);
        }

        //职位类型资源
        if (count($area_source) > self::LIMIT_NUM) {
            $area_rand_key_arr = array_rand($area_source, self::LIMIT_NUM);
            $area_rand_arr     = [];
            foreach ($area_rand_key_arr as $key) {
                $area_rand_arr[] = $area_source[$key];
            }
        } else {
            $area_rand_arr = $area_source;
        }
        //返回数据
        $data = [];
        //循环职位类型资源
        foreach ($area_rand_arr as $value) {
            $url  = $this->engine_uri;
            $name = '';
            $url  .= (empty($value['level_spell_2']) ? $value['level_spell_1'] : $value['level_spell_2']);
            $name .= empty($value['level_name_2']) ? $value['level_name_1'] : $value['level_name_2'];
            if ($job_source) {
                $url  .= '/' . (empty($job_source['level_spell_2']) ? $job_source['level_spell_1'] : $job_source['level_spell_2']);
                $name .= empty($job_source['level_name_2']) ? $job_source['level_name_1'] : $job_source['level_name_2'];
            }
            $name .= '招聘';
            $item = [
                'url'  => $url,
                'name' => $name,
            ];
            array_push($data, $item);
        }
        Cache::set($cache_key, json_encode($data), self::ENGINE_SEARCH_EXPIRE_DAY_10);

        return $data;
    }

    /**
     * 推荐热搜职位
     * @param array $cache_params
     */
    private function getSearchHotData($cache_params = [])
    {
        $cache_key = Cache::ENGINE_RECOMMEND_SEARCH_HOT;
        if (!empty($cache_params)) {
            $cache_key = $cache_key . '_' . implode('_', $cache_params);
        }
        //参数接收并且排序
        $params = $this->search_params_element;
        if (count($params) > 0) {
            sort($params);
            $cache_key .= '_' . implode('_', $params);
        }
        if ($this->keyword_param) {
            $cache_key .= '_' . md5($this->keyword_param);
        }
        //保存参数资源类型
        $search_params_type_ids = array_column($this->all_params_source, 'type_id');
        $area_bool              = false;
        if (in_array(BaseEngineSearchType::SEARCH_TYPE_AREA, $search_params_type_ids)) {
            // 含有地区类型
            $area_bool = true;
        }
        //缓存数据
        $data = Cache::get($cache_key);
        if ($data) {
            return json_decode($data, true);
        }
        if ($area_bool) {
            //含有地区-去地区队列拿15条
            $queue_cache_key = Cache::ENGINE_SEARCH_HOT_AREA_QUEUE;
        } else {
            //不含有地区-去热搜队列拿15条
            $queue_cache_key = Cache::ENGINE_SEARCH_HOT_QUEUE;
        }
        //队列长度
        $queue_count = Cache::lLen($queue_cache_key);
        //        if ($queue_count < 15) {
        //            $this->productQueueData();
        //            $queue_count = Cache::lLen($queue_cache_key);
        //        }
        if ($queue_count >= 20) {
            $max_index = $queue_count - 1;
            $index_arr = [];
            $data      = [];
            //循环随机拿去队列当中LIMIT_NUM个元素
            for ($i = 1; $i <= self::LIMIT_NUM; $i++) {
                $index = rand(0, $max_index);
                while (in_array($index, $index_arr)) {
                    $index = rand(0, $max_index);
                }
                $data_item = Cache::lIndex($queue_cache_key, $index);
                if ($data_item) {
                    array_push($index_arr, $index);
                    array_push($data, json_decode($data_item, true));
                }
            }
        } else {
            $data = [];
        }
        //$data不满就不写缓存
        if (count($data) == self::LIMIT_NUM) {
            Cache::set($cache_key, json_encode($data), self::ENGINE_SEARCH_EXPIRE_DAY_10);
        }

        return $data;
    }

    /**
     * 推荐热搜关键字
     * @param array $cache_params
     */
    private function getHotWordData($cache_params = [])
    {
        $cache_key = Cache::ENGINE_RECOMMEND_HOT_WORD;
        if (!empty($cache_params)) {
            $cache_key = $cache_key . '_' . implode('_', $cache_params);
        }
        //参数接收并且排序
        $params = $this->search_params_element;
        if (count($params) > 0) {
            sort($params);
            $cache_key .= '_' . implode('_', $params);
        }
        if ($this->keyword_param) {
            $cache_key .= '_' . md5($this->keyword_param);
        }
        //缓存数据
        $data = Cache::get($cache_key);
        if ($data) {
            return json_decode($data, true);
        }
        $ids = BaseActiveRecord::getRandIds(BaseSeoHotWordConfig::class, self::LIMIT_NUM * 3);
        //随机拿去LIMIT_NUM个元素
        $list = BaseSeoHotWordConfig::find()
            ->select([
                'id',
                'keyword',
                'code',
            ])
            ->andWhere(['id' => $ids])
            //->orderBy('rand()')
            ->limit(self::LIMIT_NUM)
            ->asArray()
            ->all();
        $data = [];
        foreach ($list as $value) {
            $item   = [
                'name' => $value['keyword'] . '招聘',
                'url'  => $this->hotword_uri . $value['code'],
            ];
            $data[] = $item;
        }
        //$data不满就不写缓存
        if (count($data) == self::LIMIT_NUM) {
            Cache::set($cache_key, json_encode($data), self::ENGINE_SEARCH_EXPIRE_DAY_10);
        }

        return $data;
    }

    /**
     * 职位百科
     * @param array $cache_params
     */
    private function getWikiData($cache_params = [])
    {
        $cache_key = Cache::ENGINE_RECOMMEND_WIKI;
        if (!empty($cache_params)) {
            $cache_key = $cache_key . '_' . implode('_', $cache_params);
        }
        //参数接收并且排序
        $params = $this->search_params_element;
        if (count($params) > 0) {
            sort($params);
            $cache_key .= '_' . implode('_', $params);
        }
        if ($this->keyword_param) {
            $cache_key .= '_' . md5($this->keyword_param);
        }
        //缓存数据
        $data = Cache::get($cache_key);
        if ($data) {
            return json_decode($data, true);
        }
        $ids = BaseActiveRecord::getRandIds(BaseSeoJobWiki::class, self::LIMIT_NUM * 3);
        //随机拿去LIMIT_NUM个元素
        $list    = BaseSeoJobWiki::find()
            ->select([
                'id',
                'keyword',
                'code',
            ])
            ->where(['is_delete' => BaseSeoJobWiki::NOT_DELETE])
            ->andWhere(['id' => $ids])
            //->orderBy('rand()')
            ->limit(self::LIMIT_NUM)
            ->asArray()
            ->all();
        $data    = [];
        $textArr = [
            '招聘要求',
            '岗位职责',
            '工作内容',
            '岗位要求',
            '任职要求',
        ];
        foreach ($list as $value) {
            $item   = [
                'name' => $value['keyword'] . $textArr[rand(0, count($textArr) - 1)],
                'url'  => $this->job_wiki_uri . $value['code'],
            ];
            $data[] = $item;
        }
        //$data不满就不写缓存
        if (count($data) == self::LIMIT_NUM) {
            Cache::set($cache_key, json_encode($data), self::ENGINE_SEARCH_EXPIRE_DAY_10);
        }

        return $data;
    }

    /**
     * 获取广告位数据
     */
    private function getPositionData()
    {
        //获取广告位置
        $position_info = BaseHomePosition::findOne([
            'number' => 'zhiweizhongxin_B_youce',
            'status' => BaseHomePosition::STATUS_SHOW_YES,
        ]);

        //获取广告位数据
        return BaseShowcase::getByPositionConfig($position_info->id, $position_info->number);
    }

    /**
     * 判断当前参数是否遵循规则
     * @return bool
     */
    private function verifyRouteRule()
    {
        //获取当前参数
        $params = $this->search_params_element;
        //获取当前规则
        $current_rule = $this->current_rule;
        //获取当前资源信息
        $current_source_info = $this->all_params_source;
        //获取当前资源类型数组
        $current_source_type_ids = array_keys($current_source_info);
        //获取当前规则信息
        $current_rule_info = $this->current_rule_info;
        //获取当前资源ID数组
        $current_source_ids = array_column($current_source_info, 'id');
        //规则一如果含有地区参数level1必须相等 否则404
        if (in_array(BaseEngineSearchType::SEARCH_TYPE_AREA,
                $current_source_type_ids) && $this->search_params['level_1'] != $current_source_info[BaseEngineSearchType::SEARCH_TYPE_AREA]['level_spell_1']) {
            return false;
        }
        $rule_source = [];
        if (isset($current_rule['type_level_1']) && $current_rule['type_level_1']) {
            $type_level_1_arr = explode(',', $current_rule['type_level_1']);
            foreach ($type_level_1_arr as $type_level_1) {
                $item_source = $current_source_info[$type_level_1];
                $item_name   = empty($item_source['level_spell_2']) ? $item_source['level_spell_1'] : $item_source['level_spell_2'];
                array_push($rule_source, $item_name);
            }
        }
        if (isset($current_rule['type_level_2']) && $current_rule['type_level_2']) {
            $type_level_2_arr = explode(',', $current_rule['type_level_2']);
            foreach ($type_level_2_arr as $type_level_2) {
                $item_source = $current_source_info[$type_level_2];
                $item_name   = empty($item_source['level_spell_2']) ? $item_source['level_spell_1'] : $item_source['level_spell_2'];
                array_push($rule_source, $item_name);
            }
        }
        $rule_1 = true;
        foreach ($rule_source as $key => $item) {
            if (isset($params[$key])) {
                if ($params[$key] != $item) {
                    $rule_1 = false;
                }
            } else {
                $rule_1 = false;
            }
            if (!$rule_1) {
                break;
            }
        }
        //当前条件进行是否需要进行二级验证
        $rule_2   = true;
        $is_match = $this->isAllowMatch($current_source_type_ids);
        if ($is_match) {
            $rule_2 = $this->sourceRuleMatch($current_source_info, $current_rule_info['is_match']);
        }

        if ($rule_1 && $rule_2) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 写入搜索引擎条件记录表
     * @return bool|null
     */
    private function engineSearchStatisticsTable()
    {
        $route_url = explode('?', Yii::$app->request->url);
        $data      = [
            'route_url'        => rtrim($route_url[0], '/'),
            'rule_id'          => $this->current_rule_info['id'],
            'level_content_1'  => $this->search_params['level_1'] ?: '',
            'level_content_2'  => $this->search_params['level_2'] ?: '',
            'level_content_tj' => $this->keyword_param ?: '',
        ];
        Producer::engineUpdateStatisticsTable($data);

        return true;
    }

    /**
     * PC分页
     * @param $totalNum
     * @param $current_page
     * @return string
     */
    private function pageHtml($totalNum, $current_page)
    {
        $page_size   = 20;
        $page        = ceil($totalNum / $page_size);//向上取整
        $html        = '';
        $current_url = rtrim($this->engine_uri, '/');
        if ($this->search_params['level_1']) {
            $current_url .= '/' . $this->search_params['level_1'];
        }
        if ($this->search_params['level_2']) {
            $current_url .= '/' . $this->search_params['level_2'];
        }
        $current_url = rtrim($current_url, '/');
        if ($this->keyword_param) {
            $keyword_uri = '/' . 'tj_' . $this->keyword_param;
        } else {
            $keyword_uri = '';
        }

        // 爬取比较厉害，所以暂时只给20页
        if (PLATFORM == 'PC' && $page > 20) {
            $page = 20;
        }

        if ($page > 1) {
            if ($current_page == 1) {
                $html .= '<a href="" class="btn-prev disabled"></a>';
            } else {
                $html .= '<a href="' . $current_url . '/p' . ($current_page - 1) . $keyword_uri . '" class="btn-prev"></a>';
            }
            if ($page <= 7) {
                for ($i = 1; $i <= $page; $i++) {
                    if ($i == $current_page) {
                        $html .= '<a href="javascript:;" class="number current">' . $i . '</a>';
                    } else {
                        $html .= '<a href="' . $current_url . '/p' . $i . $keyword_uri . '" class="number">' . $i . '</a>';
                    }
                }
            } elseif ($current_page - 2 <= 2 || $current_page + 2 >= $page - 1) {
                $html .= '<a href="' . ($current_page == 1 ? "javascript:;" : ($current_url . '/p1' . $keyword_uri)) . '" class="number ' . ($current_page == 1 ? "current" : "") . '">1</a>';
                if ($current_page - 2 <= 2) {
                    //计算一下上下加减多少
                    $up_num   = $current_page - 2;//特殊情况是1-2=-1；
                    $down_num = 4 - $up_num;
                    for ($j = $current_page - $up_num; $j <= $current_page + $down_num; $j++) {
                        if ($j == $current_page) {
                            $html .= '<a href="javascript:;" class="number current">' . $j . '</a>';
                        } else {
                            $html .= '<a href="' . $current_url . '/p' . $j . $keyword_uri . '" class="number">' . $j . '</a>';
                        }
                    }
                    if ($current_page + 3 <= $page) {
                        if ($current_page + 5 >= $page) {
                            $html .= '<a class="el-icon more btn-quicknext el-icon-more" href="' . $current_url . '/p' . $page . $keyword_uri . '"></a>';
                        } else {
                            $html .= '<a class="el-icon more btn-quicknext el-icon-more" href="' . $current_url . '/p' . ($current_page + 5) . $keyword_uri . '"></a>';
                        }
                    }
                }
                if ($current_page + 2 >= $page - 1) {
                    if ($current_page - 3 != 1 || $current_page - 2 < 1) {
                        if ($current_page - 5 <= 1) {
                            $html .= '<a class="el-icon more btn-quickprev el-icon-more" href="' . $current_url . '/p1' . $keyword_uri . '"></a>';
                        } else {
                            $html .= '<a class="el-icon more btn-quickprev el-icon-more" href="' . $current_url . '/p' . ($current_page - 5) . $keyword_uri . '"></a>';
                        }
                    }
                    $down_num = $page - $current_page - 1;
                    $up_num   = 4 - $down_num;//特殊情况是8-8-1=-1；
                    for ($j = $current_page - $up_num; $j <= ($current_page + $down_num); $j++) {
                        if ($j == $current_page) {
                            $html .= '<a href="javascript:;" class="number current">' . $j . '</a>';
                        } else {
                            $html .= '<a href="' . $current_url . '/p' . $j . $keyword_uri . '" class="number">' . $j . '</a>';
                        }
                    }
                }
                $html .= '<a href="' . ($current_page == $page ? "javascript:;" : ($current_url . '/p' . $page . $keyword_uri)) . '" class="number ' . ($current_page == $page ? "current" : "") . '">' . $page . '</a>';
            } else {
                $html .= '<a href="' . ($current_page == 1 ? "javascript:;" : ($current_url . '/p1' . $keyword_uri)) . '" class="number ' . ($current_page == 1 ? "current" : "") . '">1</a>';
                if ($current_page - 3 != 1 || $current_page - 2 < 1) {
                    if ($current_page - 5 <= 1) {
                        $html .= '<a class="el-icon more btn-quickprev el-icon-more" href="' . $current_url . '/p1' . $keyword_uri . '"></a>';
                    } else {
                        $html .= '<a class="el-icon more btn-quickprev el-icon-more" href="' . $current_url . '/p' . ($current_page - 5) . $keyword_uri . '"></a>';
                    }
                }
                for ($j = ($current_page - 2); $j <= ($current_page + 2); $j++) {
                    if ($j == $current_page) {
                        $html .= '<a href="javascript:;" class="number current">' . $j . '</a>';
                    } else {
                        $html .= '<a href="' . $current_url . '/p' . $j . $keyword_uri . '" class="number">' . $j . '</a>';
                    }
                }
                if ($current_page + 3 <= $page) {
                    if ($current_page + 5 >= $page) {
                        $html .= '<a class="el-icon more btn-quicknext el-icon-more" href="' . $current_url . '/p' . $page . $keyword_uri . '"></a>';
                    } else {
                        $html .= '<a class="el-icon more btn-quicknext el-icon-more" href="' . $current_url . '/p' . ($current_page + 5) . $keyword_uri . '"></a>';
                    }
                }
                $html .= '<a href="' . ($current_page == $page ? "javascript:;" : ($current_url . '/p' . $page . $keyword_uri)) . '" class="number ' . ($current_page == $page ? "current" : "") . '">' . $page . '</a>';
            }
            if ($current_page == $page) {
                $html .= '<a href="javascript:;" class="btn-next disabled"></a>';
            } else {
                $html .= '<a href="' . $current_url . '/p' . ($current_page + 1) . $keyword_uri . '" class="btn-next "></a>';
            }
        } else {
            $html .= '<a href="" class="btn-prev disabled"></a>';
            $html .= '<a href="javascript:;" class="number current">1</a>';
            $html .= '<a href="javascript:;" class="btn-next disabled"></a>';
        }

        return $html;
    }

    /**
     * 生成列表缓存KEY
     * @return string
     */
    private function getJobListCacheKey()
    {
        $sign_arr         = $this->search_params;
        $sign_arr['host'] = self::SEARCH_ENGINE_ROUTE;
        $sign_arr['p']    = $this->page;
        ksort($sign_arr);
        $key = http_build_query($sign_arr);

        return md5($key);
    }

    /**
     * 生产一些缓存
     */
    private function productQueueData()
    {
        //获取在线职位按照发布时间倒序获取100条
        $job_list = BaseJob::find()
            ->select('id,name')
            ->where(['status' => BaseJob::STATUS_ONLINE])
            ->orderBy('refresh_time desc')
            ->asArray()
            ->limit(100)
            ->all();
        foreach ($job_list as $job) {
            //做个长度100的redis队列-不含地区
            $redis_key     = Cache::ENGINE_SEARCH_HOT_QUEUE;
            $redis_job_key = Cache::ENGINE_SEARCH_HOT_JOB_QUEUE . ':' . md5($job['name']);
            if (preg_match('/\d/', $job['name']) && Cache::get($redis_job_key) != 1) {
                continue;
            }
            $item = [
                'url'      => $this->engine_uri . 'tj_' . $job['name'],
                'name'     => $job['name'] . '招聘',
                'job_name' => $job['name'],
            ];

            if (Cache::get($redis_job_key) != 1) {
                Cache::lPush($redis_key, json_encode($item));
                Cache::set($redis_job_key, 1);
            }

            $spell_info      = BaseEngineSearchSource::findOne(['id' => rand(4, 111)]);
            $spell_name      = $spell_info->level_spell_1;
            $spell_area_name = $spell_info->level_name_1;
            $area_item       = [
                'url'  => $this->engine_uri . $spell_name . '/' . 'tj_' . $job['name'],
                'name' => $spell_area_name . $job['name'] . '招聘',
            ];
            //做个长度100的redis队列-含有地区
            $redis_area_key     = Cache::ENGINE_SEARCH_HOT_AREA_QUEUE;
            $redis_job_area_key = Cache::ENGINE_SEARCH_HOT_AREA_JOB_QUEUE . ':' . md5($job['name']);
            if (Cache::get($redis_job_area_key) != 1) {
                Cache::lPush($redis_area_key, json_encode($area_item));
                Cache::set($redis_job_area_key, 1);
            }
        }
    }

    /**
     * 加权算法
     */
    private function weightedAlgorithm()
    {
        //移动端直接返回7200
        if ($this->operationPlatform == self::PLATFORM_H5) {
            return self::ENGINE_SEARCH_LIST_EXPIRE;
        }
        $route_url = explode('?', Yii::$app->request->url);
        //所有定义数据加权-年份加权系数
        $weight = date('Y') - 2023 ?: 1;
        //当前路由的访问量
        $current_number = BaseEngineSearchStatistics::findOneval(['route_url' => rtrim($route_url[0], '/')],
            'number') ?: 0;
        //访问量小于500不进行加权
        if ($current_number <= 500 * $weight) {
            return self::ENGINE_SEARCH_LIST_EXPIRE;
        }
        //小于1000-缓存时间变成12小时
        if ($current_number <= 1000 * $weight) {
            return self::ENGINE_SEARCH_LIST_EXPIRE * 6;
        }
        //加权规则
        //定义一个加权容量---会缓存一年365 * 24 * 60 * 60
        $weighted_capacity = 31536000;
        //加权总访问量-虚拟定义
        $weighted_total_number = 100000 * $weight;

        //加权时间戳
        return floor($weighted_capacity * $current_number / $weighted_total_number);
    }

    /**
     * 缓存时间
     * 根据条件数量进行缓存
     */
    private function cacheTime($params)
    {
        //如果level_1和level_2都为空
        if (empty($params['level_1']) && empty($params['level_2']) && empty($params['keyword'])) {
            return self::ENGINE_SEARCH_LIST_EXPIRE_MONTH;//一个月
        }
        //如果level_1不为空 level_2为空 keyword为空
        if (!empty($params['level_1']) && empty($params['level_2']) && empty($params['keyword'])) {
            //这里判断一下level_1中是否含有"_"如果含有"_"则是多条件
            if (strpos($params['level_1'], '_') !== false) {
                return self::ENGINE_SEARCH_LIST_EXPIRE_HOUR;//1小时
            }

            return self::ENGINE_SEARCH_LIST_EXPIRE_WEEK;//一周
        }
        //如果level_1和level_2都不为空 keyword为空 且level_2是单条件
        if (!empty($params['level_1']) && !empty($params['level_2']) && empty($params['keyword']) && count(explode('_',
                $params['level_2'])) == 1) {
            return self::ENGINE_SEARCH_LIST_EXPIRE_HOUR;//1小时
        }

        return self::ENGINE_SEARCH_LIST_EXPIRE_MINUTE;//10分钟
    }

    /**
     * 获取职位
     */
    private static function getJobList($params = [])
    {
        if (empty($params)) {
            $cacheKey = Cache::ENGINE_JOB_LIST . ':' . 'job_list';
        } else {
            //$params按照索引排序
            ksort($params);
            $cacheKey = Cache::ENGINE_JOB_LIST . ':' . md5(json_encode($params));
        }
        //获取缓存
        $result = Cache::get($cacheKey);
        if ($result) {
            $result = json_decode($result, true);
        } else {
            $query = BaseJob::find()
                ->alias('j')
                ->innerJoin(['c' => BaseCompany::tableName()], 'j.company_id=c.id')
                ->andWhere([
                    'j.status'  => [
                        BaseJob::STATUS_ONLINE,
                        BaseJob::STATUS_OFFLINE,
                    ],
                    'j.is_show' => BaseJob::IS_SHOW_YES,
                ]);
            //根据条件筛选
            //工作地点
            if ($params['areaId']) {
                $areaInfo = BaseArea::findOne($params['areaId']);
                if ($areaInfo->level == 2) {
                    $query->andWhere(['j.city_id' => $params['areaId']]);
                } elseif ($areaInfo->level == 1) {
                    $areaIds = BaseArea::find()
                        ->select('id')
                        ->where([
                            'parent_id' => $params['areaId'],
                            'level'     => 2,
                        ])
                        ->column();
                    $query->andWhere(['j.city_id' => $areaIds]);
                }
            }
            //单位类型
            if ($params['companyType']) {
                $query->andWhere(['c.type' => $params['companyType']]);
            }
            //学历要求
            if ($params['educationType']) {
                $query->andWhere(['j.education_type' => $params['educationType']]);
            }
            //职位类型
            if ($params['jobType']) {
                $query->andWhere(['j.job_category_id' => $params['jobType']]);
            }
            //专业-学科
            if ($params['majorId']) {
                $query->leftJoin(['jmr' => BaseJobMajorRelation::tableName()], 'j.id=jmr.job_id')
                    ->andWhere([
                        'jmr.major_id' => $params['majorId'],
                    ]);
            }
            //关键字搜索
            if ($params['keyword']) {
                //走meilisearch
                $jobIds = (new \common\service\meilisearch\job\SearchService())->setKeyword($params['keyword'])
                    ->run();
                if (!empty($jobIds)) {
                    $query->andWhere([
                        'j.id' => $jobIds,
                    ]);
                } else {
                    return [
                        'list'     => [],
                        'totalNum' => 0,
                    ];
                }
            }

            $page     = $params['p'] ?: 1;
            $pageSize = $params['pageSize'] ?: 20;
            $offset   = ($page - 1) * $pageSize;
            $list     = $query->select([
                'j.id as  jobId',
                'j.status',
                'j.name as jobName',
                'j.company_id as companyId',
                'j.min_wage as minWage',
                'j.max_wage as maxWage',
                'j.wage_type as wageType',
                'j.experience_type as experienceType',
                'j.education_type as educationType',
                'j.amount',
                'j.job_category_id as jobCategoryId',
                'j.welfare_tag as welfareTag',
                'j.province_id as provinceId',
                'j.city_id as cityId',
                'c.full_name as companyName',
                'c.type as companyType',
                'c.nature as companyNature',
                'j.refresh_time as refreshTime',
                'j.announcement_id as announcementId',
                'c.is_cooperation as isCooperation',
                'c.logo_url as companyLogo',
            ])
                ->orderBy('j.status desc,j.refresh_date desc,j.company_sort desc,j.id desc')
                ->offset($offset)
                ->limit($pageSize)
                ->asArray()
                ->all();

            //数据处理
            foreach ($list as &$jobRecord) {
                // 有些职位在添加的时候加上了一些换行,这里去掉
                $jobRecord['jobName'] = str_replace(PHP_EOL, '', $jobRecord['jobName']);
                //拼接工资
                if ($jobRecord['minWage'] == 0 && $jobRecord['maxWage'] == 0) {
                    $jobRecord['wage'] = '面议';
                } else {
                    $jobRecord['wage'] = BaseJob::formatWage($jobRecord['minWage'], $jobRecord['maxWage'],
                        $jobRecord['wageType']);
                }
                // 找到公告的信息
                if ($jobRecord['announcementId']) {
                    $announcement = BaseAnnouncement::find()
                        ->select([
                            'title',
                        ])
                        ->where([
                            'id' => $jobRecord['announcementId'],
                        ])
                        ->asArray()
                        ->one();

                    $jobRecord['announcementName'] = $announcement['title'] ?: '';
                } else {
                    $jobRecord['announcementName'] = '';
                }
                //获取经验要求
                $jobRecord['experience'] = BaseDictionary::getExperienceName($jobRecord['experienceType']);
                // 获取学历要求
                $jobRecord['education'] = BaseDictionary::getEducationName($jobRecord['educationType']);
                //获取意向职能
                $jobRecord['jobCategory'] = BaseCategoryJob::getName($jobRecord['jobCategoryId']);
                //获取福利标签
                $jobRecord['welfareTagArr'] = array_slice(BaseWelfareLabel::getWelfareLabelNameList($jobRecord['welfareTag']),
                    0, 2);
                // 获取地区名称
                $cityName              = BaseArea::getAreaName($jobRecord['cityId']);
                $jobRecord['areaName'] = BaseArea::getAreaName($jobRecord['provinceId']) . '-' . $cityName;
                // 获取城市名称
                $jobRecord['city'] = $cityName;
                //获取单位类型
                $jobRecord['companyTypeName'] = BaseDictionary::getCompanyTypeName($jobRecord['companyType']);
                // 单位类型
                $jobRecord['companyNatureName'] = BaseDictionary::getCompanyNatureName($jobRecord['companyNature']);
                //判断职位是否是合作单位的职位
                $jobRecord['applyStatus'] = BaseJob::JOB_APPLY_STATUS_NO;
                $jobRecord['refreshDate'] = TimeHelper::formatDateByYear($jobRecord['refreshTime']);
                if ($jobRecord['isCooperation'] == BaseCompany::COOPERATIVE_UNIT_YES) {
                    //如果是合作单位，站内投递
                    $jobRecord['isCooperation'] = "1";
                } else {
                    //站外投递
                    $jobRecord['isCooperation'] = "2";
                }
                $jobRecord['url']             = BaseJob::getDetailUrl($jobRecord['jobId']);
                $jobRecord['companyUrl']      = BaseCompany::getDetailUrl($jobRecord['companyId']);
                $jobRecord['announcementUrl'] = BaseAnnouncement::getDetailUrl($jobRecord['announcementId']);
                $jobRecord['companyLogo']     = $jobRecord['companyLogo'] ?: Yii::$app->params['defaultCompanyLogo'];

                unset($jobRecord['wageType'], $jobRecord['minWage'], $jobRecord['maxWage'], $jobRecord['refreshTime'], $jobRecord['jobCategoryId'], $jobRecord['welfareTag'], $jobRecord['provinceId'], $jobRecord['cityId'], $jobRecord['companyType'], $jobRecord['companyNature'], $jobRecord['majorId'], $jobRecord['experienceType'], $jobRecord['educationType'], $jobRecord['announcementId'], $jobRecord['companyId']);
            }
            if (count($list) < $pageSize) {
                $count = ($page - 1) * $pageSize + count($list);
            } else {
                $count = 400;
            }

            $result = [
                'list'     => $list,
                'totalNum' => $count,
            ];
            if (count($list) > 0) {
                //设置缓存
                Cache::set($cacheKey, json_encode($result), self::ENGINE_JOB_LIST_EXPIRE);
            }
        }
        $memberId = Yii::$app->user->getId();
        //不能缓存的数据
        foreach ($result['list'] as &$item) {
            if ($memberId) {
                $item['isCollect']   = (BaseJobCollect::checkIsCollect($memberId, $item['jobId'])) ? '1' : '2';
                $item['applyStatus'] = BaseJobApplyRecord::checkJobApplyStatus($memberId, $item['jobId']);
            }
        }

        return $result;
    }
}