<?php

namespace common\service\downloadTask;

use common\base\models\BaseAdmin;
use common\base\models\BasePayTransformBuriedPointLog;
use common\libs\Excel;
use yii\base\Exception;

class PayTransformLogService
{
    private $actionList;
    private $startTime;
    private $endTime;

    public function setParams($params)
    {
        $this->actionList = $params['actionList'];
        $this->startTime  = $params['startTime'] ?: '';
        $this->endTime    = $params['endTime'] ?: '';
        if (!$this->actionList) {
            throw new Exception('缺少必要参数');
        }

        return $this;
    }

    public function run()
    {
        $select = [
            'id',
            'action_type',
            'action_id',
            'platform',
            'ip',
            'user_cookies',
            'member_id',
            'add_time',
            'params',
        ];

        $sheetName = [];
        foreach ($this->actionList as $k => $actionId) {
            $data  = [];
            $query = BasePayTransformBuriedPointLog::find()
                ->where(['action_id' => $actionId])
                ->select($select);

            if ($this->startTime && $this->endTime) {
                $query->andWhere([
                    'between',
                    'add_time',
                    $this->startTime,
                    $this->endTime,
                ]);
            }

            $logList = $query->asArray()
                ->all();

            $headers = [
                '序号',
                '事件类型',
                '事件ID',
                '事件名称',
                '产品端',
                'IP',
                'cookies',
                '用户ID',
                '发生时间',
            ];
            //取1条日志，获取需要显示的params内容
            $params = json_decode($logList[0]['params'], true);
            foreach ($params as $key => $val) {
                $keyName = BasePayTransformBuriedPointLog::PARAMS_TEXT_LIST[$key];
                if (!empty($keyName)) {
                    $headers[] = $keyName;
                }
            }
            foreach ($logList as $key => $item) {
                $logParams = json_decode($item['params'], true);
                $data[]    = [
                    //todo：暂时换id，方便查询
                    //                    $key+1,
                    $item['id'],
                    BasePayTransformBuriedPointLog::ACTION_TYPE_TEXT_LIST[$item['action_type']],
                    $item['action_id'],
                    BasePayTransformBuriedPointLog::ACTION_NAME_LIST[$item['action_id']],
                    BasePayTransformBuriedPointLog::PLATFORM_TYPE_LIST[$item['platform']],
                    $item['ip'],
                    $item['user_cookies'],
                    $item['member_id'],
                    $item['add_time'],
                ];
                foreach ($logParams as $paramKey => $val) {
                    if ($paramKey == BasePayTransformBuriedPointLog::PARAMS_CLICK_POSITION) {
                        //特殊参数，需要转换文案
                        $data[$key][] = BasePayTransformBuriedPointLog::CLICK_POSITION_TEXT_LIST[$val];
                    } else {
                        $data[$key][] = $val;
                    }
                }
            }
            $sheetName[] = [
                'sheetName' => BasePayTransformBuriedPointLog::ACTION_NAME_LIST[$actionId],
                'data'      => $data,
                'headers'   => $headers,
            ];
        }
        $excel = new Excel();
        $excel->multipleSheet($sheetName);

        $saveFile = $excel->saveFile;

        return $saveFile;
    }

}