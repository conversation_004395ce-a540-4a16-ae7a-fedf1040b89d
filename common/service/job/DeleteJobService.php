<?php

namespace common\service\job;

use common\base\models\BaseAdmin;
use common\base\models\BaseJob;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseMember;
use common\helpers\IpHelper;
use yii\base\Exception;

class DeleteJobService extends BaseService
{
    /**
     * 执行删除
     */
    public function run()
    {
        try {
            $this->delete();
        } catch (Exception $e) {
        }
    }

    /**
     * 删除
     * @throws Exception
     */
    private function delete()
    {
        $this->deleteJob();
    }

    /**
     * 设置好要处理的数据
     * @param $params
     * @return $this
     * @throws Exception
     */
    public function setData($params): DeleteJobService
    {
        $this->jobId = $params['id'];

        $data = BaseJob::find()
            ->where(['id' => $this->jobId])
            ->asArray()
            ->one();

        if (!$data) {
            throw new Exception('非法请求');
        }

        return $this;
    }

    /**
     * 删除职位
     * @throws Exception
     */
    private function deleteJob()
    {
        $model = BaseJob::findOne(['id' => $this->jobId]);

        $model->status      = BaseJob::STATUS_DELETE;
        $model->delete_time = CUR_DATETIME;
        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
    }

    /**
     * 在执行后
     * @throws \yii\base\NotSupportedException
     */
    public function afterRun($idArr)
    {
        // 消耗套餐,写日志等等
        $jobApplyWhere    = ['and'];
        $jobApplyWhere [] = [
            'in',
            'id',
            $idArr,
        ];
        $jobSelect        = [
            'id',
            'status',
        ];
        $jobList          = BaseJob::selectInfos($jobApplyWhere, $jobSelect);
        $nowDateTime      = date("Y-m-d H:i:s", time());
        foreach ($jobList as $job) {
            $list                  = [];
            $list['job_id']        = $job['id'];
            $list['handle_type']   = (string)BaseJobHandleLog::HANDLE_TYPE_OFFLINE;
            $list['handle_before'] = [
                '职位状态' => BaseJob::JOB_STATUS_NAME[$job['status']],
                '更新时间' => $job['update_time'],
            ];
            $list['handle_after']  = [
                '职位状态' => BaseJob::JOB_STATUS_NAME[BaseJob::STATUS_OFFLINE],
                '更新时间' => $nowDateTime,
            ];
            self::createJobHandleLog($list);
        }
    }

    /**
     * 操作入表
     * @throws \yii\base\NotSupportedException
     */
    public function createJobHandleLog($data)
    {
        $jobHandleLog = [
            'add_time'      => date("Y-m-d H:i:s", time()),
            'job_id'        => $data['job_id'],
            'handle_type'   => $data['handle_type'],
            'handler_type'  => $this->operatorType,
            'handler_id'    => $this->operatorId,
            'handler_name'  => $this->operatorName,
            'handle_before' => json_encode($data['handle_before']),
            'handle_after'  => json_encode($data['handle_after']),
            'ip'            => IpHelper::getIpInt(),
        ];
        try {
            BaseJobHandleLog::createInfo($jobHandleLog);
        } catch (Exception $e) {
        }
    }

    private function checkPermission(): bool
    {
        return true;
    }

}
