<?php
namespace common\libs;

use common\helpers\StringHelper;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Writer\HTML;
use yii\base\Exception;

class Word
{

    /**
     * 把word识别成html输出(现在就只需要给富文本使用)
     * @param      $filePath
     * @param bool $isDelete
     * @return string
     * @throws Exception
     * @throws \PhpOffice\PhpWord\Exception\Exception
     */
    public function recognizeToHtml($filePath, $isDelete = true)
    {
        $phpWord = IOFactory::load($filePath);
        $write   = new HTML($phpWord);
        $file    = 'uploads/' . CUR_TIMESTAMP . StringHelper::randNumber() . '.html';
        $write->save($file);

        if (!is_file($file)) {
            throw new Exception('非文件');
        }
        $rs = file_get_contents($file);

        preg_match("/<body[^>]*?>(.*\s*?)<\/body>/is", $rs, $body);
        preg_match("/<style[^>]*?>(.*\s*?)<\/style>/is", $rs, $style);

        $html = '';

        if ($style[1]) {
            $html = '<style>' . $style[1] . '</style>';
        }

        $html .= '<body>' . $body[1] . '</body>';

        // 删除两个文件
        unlink($file);
        if ($isDelete) {
            unlink($filePath);
        }

        return $html;
    }

}