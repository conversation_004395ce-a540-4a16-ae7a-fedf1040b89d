<?php
namespace common\libs;

use function AlibabaCloud\Client\value;

class HomeColumnCache
{
    private $type;
    private $id;
    public  $url;
    private $key;
    public  $msgCode;
    public  $msg;
    private $baseUrl;

    const TYPE_PC_HOME   = 1;
    const TYPE_H5_HOME   = 2;
    const TYPE_PC_COLUMN = 3;
    const TYPE_H5_COLUMN = 4;

    const CACHE_URL_KEY = 'updateCacheGaoCai2022';

    public function run($type, $id = 0)
    {
        $this->type = $type;
        $this->id   = $id;
        $this->setKey();
        $this->setUrl();

        return $this->send();
    }

    public function send()
    {
        try {
            $guzzle = new \GuzzleHttp\Client();
            $rs     = $guzzle->getAsync($this->url)
                ->wait();
            if ($rs->getStatusCode() == 200) {
                return true;
            }

            return false;
        } catch (\Exception $e) {
            $this->msgCode = $e->getCode();
            $this->msg     = $e->getMessage();

            return false;
        }
    }

    public function setUrl()
    {
        switch ($this->type) {
            case self::TYPE_PC_HOME:
                $url = $this->baseUrl = \Yii::$app->params['pcHost'] . '?' . self::CACHE_URL_KEY . '=' . time();
                break;
            case self::TYPE_PC_COLUMN:
                $this->baseUrl = \Yii::$app->params['pcHost'];
                $url           = $this->baseUrl . '/home/<USER>' . $this->id . '&' . self::CACHE_URL_KEY . '=' . time();
                break;
            case self::TYPE_H5_HOME:
                $url = $this->baseUrl = \Yii::$app->params['h5Host'] . '?' . self::CACHE_URL_KEY . '=' . time();;
                break;
            case self::TYPE_H5_COLUMN:
                $this->baseUrl = \Yii::$app->params['h5Host'];
                $url           = $this->baseUrl . '/home/<USER>' . $this->id . '&' . self::CACHE_URL_KEY . '=' . time();
                break;
            default:
                break;
        }

        $this->url = $url;
    }

    public function setKey()
    {
        $this->key = self::getKey($this->type, $this->id);
    }

    public function update()
    {
    }

    public function delete()
    {
    }

    public static function getKey($type, $id = '')
    {
        switch ($type) {
            case self::TYPE_PC_HOME:
                return Cache::PC_HOME_HTML_KEY;
            case self::TYPE_H5_HOME:
                return Cache::H5_HOME_HTML_KEY;
            case self::TYPE_PC_COLUMN:
                return Cache::PC_COLUMN_HTML_KEY . ':' . $id;
            case self::TYPE_H5_COLUMN:
                return Cache::H5_COLUMN_HTML_KEY . ':' . $id;
            default:
                return '';
        }
    }

}