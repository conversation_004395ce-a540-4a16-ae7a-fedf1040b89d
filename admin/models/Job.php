<?php

namespace admin\models;

use common\base\BaseActiveRecord;
use common\base\models\BaseAdmin;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementHandleLog;
use common\base\models\BaseArea;
use common\base\models\BaseArticle;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyInterview;
use common\base\models\BaseCompanyMemberConfig;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseDictionary;
use common\base\models\BaseFile;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseJob;
use common\base\models\BaseJobApply;
use common\base\models\BaseJobApplyHandleLog;
use common\base\models\BaseJobApplyRecord;
use common\base\models\BaseJobApplyRecordExtra;
use common\base\models\BaseJobContact;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseJobEdit;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseJobMajorRelation;
use common\base\models\BaseMajor;
use common\base\models\BaseMember;
use common\base\models\BaseOffSiteJobApply;
use common\base\models\BaseResume;
use common\base\models\BaseResumeAdditionalInfo;
use common\base\models\BaseResumeAttachment;
use common\base\models\BaseResumeDownloadLog;
use common\base\models\BaseUser;
use common\base\models\BaseWelfareLabel;
use common\helpers\DebugHelper;
use common\helpers\FileHelper;
use common\helpers\IpHelper;
use common\helpers\StringHelper;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use common\helpers\ValidateHelper;
use common\libs\Cache;
use common\libs\ColumnAuto\JobAutoClassify;
use common\libs\Excel;
use common\libs\JobBatchImport;
use common\service\CommonService;
use common\service\job\AddService;
use common\service\job\BaseService;
use common\service\job\DeleteJobService;
use common\service\job\JobService;
use common\service\job\OnlineOfflineService;
use frontendPc\models\Area;
use queue\Producer;
use Yii;
use yii\base\Exception;
use yii\db\ActiveQuery;
use yii\db\Expression;

class Job extends BaseJob
{
    const ORDER_BY_DESC = 1;
    const ORDER_BY_ASC  = 2;

    /**
     * 职位列表的查询构造器
     * @param $keywords
     * @return ActiveQuery
     * @throws Exception
     */
    public static function jobListBuilderQuery($keywords)
    {
        $query = self::find()
            ->alias('j')
            ->leftJoin(['an' => Announcement::tableName()], 'an.id = j.announcement_id')
            ->leftJoin(['art' => Article::tableName()], 'art.id=an.article_id')
            ->leftJoin(['c' => Company::tableName()], 'c.id=j.company_id')
            ->leftJoin(['jare' => BaseJobApplyRecordExtra::tableName()], 'j.id=jare.job_id')
            ->andWhere([
                'j.status' => [
                    self::STATUS_ONLINE,
                    self::STATUS_OFFLINE,
                ],
            ])
            ->andWhere(['c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES]);

        if ($keywords['announcement']) {
            if (strlen(intval($keywords['announcement'])) == 8) {
                $fullNameNum = UUIDHelper::decryption($keywords['announcement']);
                $query->andWhere(['an.id' => $fullNameNum]);
            } else {
                $query->andWhere([
                    'like',
                    'an.title',
                    $keywords['announcement'],
                ]);
            }
        }
        $query->andFilterCompare('j.creator', $keywords['creator'], 'like');
        $query->andFilterCompare('j.nature_type', $keywords['nature_type']);
        $query->andFilterCompare('j.department', $keywords['department'], 'like');
        $query->andFilterCompare('j.abroad_type', $keywords['abroad_type']);
        $query->andFilterCompare('j.title_type', $keywords['title_type'], 'in');
        $query->andFilterCompare('j.audit_status', $keywords['audit_status']);
        $query->andFilterCompare('j.status', $keywords['status']);
        $query->andFilterCompare('j.age_type', $keywords['age_type']);
        $query->andFilterCompare('j.gender_type', $keywords['gender_type']);
        $query->andFilterCompare('j.political_type', $keywords['political_type']);
        $query->andFilterCompare('j.is_article', $keywords['is_article']);
        $query->andFilterCompare('j.is_miniapp', $keywords['is_miniapp']);
        //筛选投递类型
        if ($keywords['delivery_type']) {
            $query->andWhere([
                'or',
                ['j.delivery_type' => $keywords['delivery_type']],
                [
                    'and',
                    ['j.delivery_type' => 0],
                    ['an.delivery_type' => $keywords['delivery_type']],
                ],
            ]);
        }
        //筛选投递方式
        if ($keywords['delivery_way']) {
            $query->andWhere([
                    'or',
                    ['j.delivery_way' => $keywords['delivery_way']],
                    [
                        'and',
                        ['an.delivery_way' => $keywords['delivery_way']],
                        ['j.delivery_way' => 0],
                    ],
                ]

            );
        }
        //筛选报名方式
        if ($keywords['apply_type']) {
            $query->andWhere([
                    'or',
                    [
                        'like',
                        'j.apply_type',
                        $keywords['apply_type'],
                    ],
                    [
                        'like',
                        'an.apply_type',
                        $keywords['apply_type'],
                    ],
                ]

            );
        }
        if ($keywords['education_type']) {
            $educationType = explode(',', $keywords['education_type']);
            $query->andWhere(['j.education_type' => $educationType]);
        }
        $query->andFilterCompare('j.experience_type', $keywords['experience_type']);
        if ($keywords['city']) {
            $city = explode(',', $keywords['city']);
            $query->andFilterCompare('j.city_id', $city, 'in');
        }

        if ($keywords['job_category_id']) {
            $jobCategoryId = explode(',', $keywords['job_category_id']);
            $temp          = [];
            foreach ($jobCategoryId as $categoryId) {
                $temp[] = (int)$categoryId;
            }
            $query->andFilterCompare('j.job_category_id', $temp, 'in');
        }

        if ($keywords['name']) {
            if (is_numeric($keywords['name']) && strlen($keywords['name']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['name']);
                $query->andFilterCompare('j.id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('j.name', $keywords['name'], 'like');
            }
        }

        if ($keywords['major_id']) {
            $query->andWhere("find_in_set(" . $keywords['major_id'] . ",j.major_id)");
        }

        if ($keywords['company']) {
            if (strlen(intval($keywords['company'])) == 8) {
                $query->andWhere(['j.company_id' => UUIDHelper::decryption($keywords['company'])]);
            } else {
                $query->andWhere([
                    'like',
                    'c.full_name',
                    $keywords['company'],
                ]);
            }
        }
        if ($keywords['add_time_start']) {
            $query->andWhere([
                '>=',
                'j.add_time',
                TimeHelper::dayToBeginTime($keywords['add_time_start']),
            ]);
        }
        if ($keywords['add_time_end']) {
            $query->andWhere([
                '<=',
                'j.add_time',
                TimeHelper::dayToEndTime($keywords['add_time_end']),
            ]);
        }
        if ($keywords['release_time_start']) {
            $query->andWhere([
                '>=',
                'j.release_time',
                TimeHelper::dayToBeginTime($keywords['release_time_start']),
            ]);
        }
        if ($keywords['release_time_end']) {
            $query->andWhere([
                '<=',
                'j.release_time',
                TimeHelper::dayToEndTime($keywords['release_time_end']),
            ]);
        }
        if ($keywords['refresh_time_start']) {
            $query->andWhere([
                '>=',
                'j.real_refresh_time',
                TimeHelper::dayToBeginTime($keywords['refresh_time_start']),
            ]);
        }
        if ($keywords['refresh_time_end']) {
            $query->andWhere([
                '<=',
                'j.real_refresh_time',
                TimeHelper::dayToEndTime($keywords['refresh_time_end']),
            ]);
        }
        if ($keywords['first_release_time_end'] && $keywords['first_release_time_start']) {
            $query->andWhere([
                'between',
                'j.first_release_time',
                TimeHelper::dayToBeginTime($keywords['first_release_time_start']),
                TimeHelper::dayToEndTime($keywords['first_release_time_end']),
            ]);
        }
        //处理联系人与协同联系人的搜索
        if ($keywords['contact']) {
            $ids = BaseJobContact::find()
                ->alias('jc')
                ->select('jc.job_id')
                ->leftJoin(['cmi' => BaseCompanyMemberInfo::tableName()], 'cmi.id = jc.company_member_info_id')
                ->leftJoin(['m' => BaseMember::tableName()], 'm.id = cmi.member_id')
                ->orWhere(['m.id' => $keywords['contact']])
                ->orWhere(['m.email' => $keywords['contact']])
                ->orWhere(['m.mobile' => $keywords['contact'],])
                ->asArray()
                ->column() ?: [];
            $query->andWhere([
                'in',
                'j.id',
                $ids,
            ]);
        }

        if ($keywords['establishment_type']) {
            // 这里会有几种可能性,-1和其他值(-1的情况下是没有选编制的类型,其他值是选了编制类型里面某个并且find_in_set
            $establishmentType = explode(',', $keywords['establishment_type']);
            $orWhere           = [
                'or',
            ];
            foreach ($establishmentType as $itemKey => $establishmentItem) {
                if ($establishmentItem == -1) {
                    $orWhere[] = ['j.is_establishment' => BaseJob::IS_ESTABLISHMENT_NO];
                } else {
                    $establishmentItemKey = 'establishmentItem' . $itemKey;
                    $orWhere[]            = new Expression("FIND_IN_SET(:" . $establishmentItemKey . ", j.establishment_type)",
                        [$establishmentItemKey => $establishmentItem]);
                }
            }
            $query->andWhere($orWhere);
        }

        //处理联系人与协同联系人的搜索
        if ($keywords['contact_synergy']) {
            $ids = BaseJobContactSynergy::find()
                ->alias('jcs')
                ->select('jcs.job_id')
                ->leftJoin(['cmi' => BaseCompanyMemberInfo::tableName()], 'cmi.id = jcs.company_member_info_id')
                ->leftJoin(['m' => BaseMember::tableName()], 'm.id = cmi.member_id')
                ->orWhere(['m.id' => $keywords['contact_synergy']])
                ->orWhere(['m.email' => $keywords['contact_synergy']])
                ->orWhere(['m.mobile' => $keywords['contact_synergy']])
                ->asArray()
                ->column() ?: [];
            $query->andWhere([
                'in',
                'j.id',
                $ids,
            ]);
        }

        $orderBy = ' j.status desc,j.add_time desc';
        if ($keywords['sort_apply_num']) {
            $sort    = $keywords['sort_apply_num'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' jare.total ' . $sort;
        }
        if ($keywords['sort_interview_num']) {
            $sort    = $keywords['sort_interview_num'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' jare.interview ' . $sort;
        }
        if ($keywords['sort_refresh_time']) {
            $sort    = $keywords['sort_refresh_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.real_refresh_time ' . $sort;
        }
        if ($keywords['sort_release_time']) {
            $sort    = $keywords['sort_release_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.real_refresh_time ' . $sort;
        }
        if ($keywords['sort_amount']) {
            $sort    = $keywords['sort_amount'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.amount ' . $sort;
        }
        if ($keywords['sort_click']) {
            $sort    = $keywords['sort_click'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.click ' . $sort;
        }
        if ($keywords['sort_download_num']) {
            $sort    = $keywords['sort_download_num'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.download_amount ' . $sort;
        }
        if ($keywords['sort_add_time']) {
            $sort    = $keywords['sort_add_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.add_time ' . $sort;
        }
        if ($keywords['sort_period_date']) {
            $sort    = $keywords['sort_period_date'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.period_date ' . $sort;
        }
        if ($keywords['sort_delete_time']) {
            $sort    = $keywords['sort_delete_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.delete_time ' . $sort;
        }

        $query->orderBy($orderBy);

        return $query;
    }

    /**
     * 获取合作单位的职位列表
     * @param $keywords
     * @return array
     * @throws Exception
     */
    public static function getJobListNew($keywords)
    {
        self::openDb2();

        //获取query
        $query       = self::jobListBuilderQuery($keywords);
        $total_query = clone $query;
        //获取总数
        $count = $query->count();
        //获取列表数据
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $jobList  = $query->select([
            'j.id',
            'j.name',
            'j.refresh_time',
            'j.first_release_time',
            'j.status',
            'j.department',
            'j.city_id',
            'j.period_date',
            'j.code',
            'j.education_type',
            'j.min_wage',
            'j.max_wage',
            'j.is_article',
            'j.audit_status',
            'j.release_time',
            'j.add_time',
            'j.wage_type',
            'j.province_id',
            'j.experience_type',
            'j.offline_type',
            'j.age_type',
            'j.title_type',
            'j.company_id',
            'j.member_id',
            'j.period_date',
            'j.announcement_id',
            'j.click',
            'j.amount',
            'j.political_type',
            'j.abroad_type',
            'j.download_amount',
            'j.gender_type',
            'j.creator',
            'j.delete_time',
            'j.is_miniapp',
            'j.is_show',
            'j.major_id',
            'j.job_category_id',
            'j.real_refresh_time',
            'j.delivery_limit_type',
            'c.full_name as company',
            'art.status as announcementStatus',
            'an.title',
            'an.audit_status as announcementAuditStatus',
            'art.status as articleStatus',
            'j.click as allClick',
            'jare.total as jobApplyNum',
            'jare.interview as jobInterviewNum',
        ])
            ->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        foreach ($jobList as &$job) {
            $experienceTypeTitle = BaseDictionary::getExperienceName($job['experience_type']);
            $educationTypeTitle  = BaseDictionary::getEducationName($job['education_type']);
            $city                = BaseArea::getAreaName($job['city_id']);
            //输出薪资
            $wage = BaseJob::formatWage($job['min_wage'], $job['max_wage'], $job['wage_type']);

            $job['basicInformation'] = $experienceTypeTitle . '|' . $educationTypeTitle . '|' . $city . "｜" . $wage;
            //关联公告
            $job['announcementTitle']  = $job['title'] ?: '';
            $job['status']             = intval($job['status']);
            $job['statusTitle']        = self::JOB_STATUS_NAME[$job['status']];
            $job['auditStatus']        = intval($job['audit_status']);
            $job['auditStatusTitle']   = self::JOB_AUDIT_STATUS_NAME[$job['audit_status']];
            $job['deleteDate']         = "";
            $job['id']                 = intval($job['id']);
            $job['uid']                = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $job['id']);
            $job['announcementId']     = intval($job['announcement_id']);
            $job['jobApplyNum']        = intval($job['jobApplyNum']);
            $job['jobInterviewNum']    = intval($job['jobInterviewNum']);
            $job['isArticle']          = intval($job['is_article']);
            $job['companyId']          = intval($job['company_id']);
            $job['click']              = intval($job['click']);
            $job['amount']             = intval($job['amount']);
            $job['downloadAmount']     = intval($job['downloadAmount']);
            $job['add_time']           = ($job['add_time'] == '0000-00-00 00:00:00' || empty($job['add_time'])) ? '-' : date('Y/m/d',
                strtotime($job['add_time']));
            $job['period_date']        = ($job['period_date'] == '0000-00-00 00:00:00' || empty($job['period_date'])) ? '-' : date('Y/m/d',
                strtotime($job['period_date']));
            $job['refresh_time']       = ($job['refresh_time'] == '0000-00-00 00:00:00' || empty($job['refresh_time'])) ? '-' : date('Y/m/d',
                strtotime($job['refresh_time']));
            $job['release_time']       = ($job['release_time'] == '0000-00-00 00:00:00' || empty($job['release_time'])) ? '-' : date('Y/m/d',
                strtotime($job['release_time']));
            $job['delete_time']        = ($job['delete_time'] == '0000-00-00 00:00:00' || empty($job['delete_time'])) ? '-' : date('Y/m/d',
                strtotime($job['delete_time']));
            $job['real_refresh_time']  = ($job['real_refresh_time'] == '0000-00-00 00:00:00' || empty($job['real_refresh_time'])) ? '-' : date('Y/m/d',
                strtotime($job['real_refresh_time']));
            $job['first_release_time'] = ($job['first_release_time'] == '0000-00-00 00:00:00' || empty($job['first_release_time'])) ? '-' : date('Y/m/d',
                strtotime($job['first_release_time']));

            if (empty($job['creator'])) {
                $job['creator'] = '-';
            }

            $query    = BaseMajor::find();
            $majorIds = [];
            if ($job['major_id']) {
                $majorIds = $query->select('id')
                    ->where(['parent_id' => explode(',', $job['major_id'])])
                    ->andWhere(['level' => 3])
                    ->column();
            }

            $job['majorIds'] = $majorIds;

            //获取职位的限制状态
            $deliveryLimitType = explode(',', $job['delivery_limit_type']);

            if (in_array(BaseJob::DELIVERY_LIMIT_EDUCATION, $deliveryLimitType)) {
                $job['isLimitEducation'] = 1;
            } else {
                $job['isLimitEducation'] = 2;
            }
            if (in_array(BaseJob::DELIVERY_LIMIT_MATERIAL, $deliveryLimitType)) {
                $job['isLimitFile'] = 1;
            } else {
                $job['isLimitFile'] = 2;
            }
            unset($job['delivery_limit_type'], $job['gender_type']);

            // 学科
            $job['majorTxt']     = BaseMajor::getAllMajorName(explode(',', $job['major_id']));
            $job['isMiniappTxt'] = self::IS_MINIAPP_LIST[$job['is_miniapp']];
            //处理职位联系人与协同账号
            $job['job_contact']             = Job::getJobContact($job['id']);
            $job['job_contact_synergy']     = Job::getJobContactSynergy($job['id']);
            $job['job_contact_synergy_num'] = count($job['job_contact_synergy']);
            $company_config_info            = BaseCompanyMemberConfig::getInfo($job['company_id']);
            $job['sub_used']                = intval($company_config_info['used']);
        }
        $result = [
            'list' => $jobList,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];

        //获取统计总数
        $total_data        = $total_query->select([
            'sum(j.click) as click',
            'sum(jare.total) as total',
            'sum(jare.interview) as interview',
            'sum(jare.delivery_way_platform) as platformNum',
            'sum(jare.delivery_way_email) as emailNum',
            'sum(jare.delivery_way_link) as linkNum',
        ])
            ->asArray()
            ->one();
        $total_data['all'] = $count;
        $result['amount']  = $total_data;

        self::closeDb2();

        return $result;
    }

    /**
     * 获取合作单位的职位列表
     * @param $keywords
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public static function getSliceJobList($keywords): array
    {
        $query = self::sliceJobListBuilderQuery($keywords);

        //获取总数
        $count = $query->count();
        //获取列表数据
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $jobList  = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        $visitUrl = 'http://' . str_replace('http://', '', Yii::$app->params['pcHost']);
        foreach ($jobList as &$job) {
            $job['city']     = BaseArea::getAreaName($job['city_id']);
            $job['province'] = BaseArea::getAreaName($job['province_id']);

            $job['status']             = intval($job['status']);
            $job['statusTitle']        = self::JOB_STATUS_NAME[$job['status']];
            $job['first_release_time'] = ($job['first_release_time'] == '0000-00-00 00:00:00' || empty($job['first_release_time'])) ? '-' : date('Y/m/d',
                strtotime($job['first_release_time']));
            $job['refresh_date']       = ($job['refresh_date'] == '0000-00-00' || empty($job['refresh_date'])) ? '-' : date('Y/m/d',
                strtotime($job['refresh_date']));

            // 学科
            $job['majorTxt'] = BaseMajor::getAllMajorName(explode(',', $job['major_id']));

            $job['job_link']          = $visitUrl . '/job/detail/' . $job['id'] . '.html';
            $job['announcement_link'] = $visitUrl . '/announcement/detail/' . $job['announcement_id'] . '.html';
            unset($job['city_id'], $job['province_id'], $job['announcement_id'], $job['status'], $job['major_id']);
        }

        return [
            'list' => $jobList,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];
    }

    /**
     * 职位列表的查询构造器
     * @param $keywords
     * @return ActiveQuery
     * @throws Exception
     */
    public static function sliceJobListBuilderQuery($keywords): ActiveQuery
    {
        $select = [
            'j.id',
            'j.uuid',
            'j.name',
            'j.first_release_time',
            'j.refresh_date',
            'j.status',
            'j.department',
            'j.education_type',
            'j.apply_type',
            'j.amount',
            'j.duty',
            'j.requirement',
            'j.remark',
            'c.city_id',
            'c.province_id',
            'j.announcement_id',
            'j.delivery_type',
            'j.major_id',
            'c.full_name as company',
            'c.type as company_type',
            'c.nature as company_nature',
        ];
        $query  = BaseJob::find()
            ->alias('j')
            ->innerJoin(['c' => BaseCompany::tableName()], 'c.id = j.company_id')
            ->select($select)
            ->where([
                'j.status'         => [
                    self::STATUS_ONLINE,
                    self::STATUS_OFFLINE,
                ],
                'c.is_cooperation' => BaseCompany::COOPERATIVE_UNIT_YES,
            ]);

        $query->andFilterCompare('j.nature_type', $keywords['nature_type']);
        $query->andFilterCompare('j.department', $keywords['department'], 'like');
        $query->andFilterCompare('j.abroad_type', $keywords['abroad_type']);
        $query->andFilterCompare('j.title_type', $keywords['title_type'], 'in');
        $query->andFilterCompare('j.audit_status', $keywords['audit_status']);
        $query->andFilterCompare('j.status', $keywords['status']);
        $query->andFilterCompare('j.age_type', $keywords['age_type']);

        if ($keywords['city']) {
            $city = explode(',', $keywords['city']);
            $query->andFilterCompare('j.city_id', $city, 'in');
        }

        if ($keywords['job_category_id']) {
            $jobCategoryId = explode(',', $keywords['job_category_id']);
            $temp          = [];
            foreach ($jobCategoryId as $categoryId) {
                $temp[] = (int)$categoryId;
            }
            $query->andFilterCompare('j.job_category_id', $temp, 'in');
        }

        if ($keywords['education_type']) {
            $educationType = explode(',', $keywords['education_type']);
            $query->andWhere(['j.education_type' => $educationType]);
        }

        if ($keywords['name']) {
            // 查询j.name 或者 j.uuid,直接or
            $query->andWhere([
                'or',
                [
                    'like',
                    'j.name',
                    $keywords['name'],
                ],
                ['j.uuid' => $keywords['name']],
            ]);

            // if (is_numeric($keywords['name']) && strlen($keywords['name']) == 8) {
            //     $query->andFilterCompare('j.uuid', (int)$keywords['name']);
            // } else {
            //     $query->andFilterCompare('j.name', $keywords['name'], 'like');
            // }
        }

        if ($keywords['major_id']) {
            $majorIds       = explode(',', $keywords['major_id']);
            $majorCondition = ['or'];
            foreach ($majorIds as $item) {
                $majorCondition[] = "find_in_set(" . $item . ",j.major_id)";
            }
            $query->andWhere($majorCondition);
        }

        if ($keywords['political_type']) {
            $politicalType = explode(',', $keywords['political_type']);
            $query->andWhere([
                'j.political_type' => $politicalType,
            ]);
        }

        if ($keywords['experience_type']) {
            $experienceType = explode(',', $keywords['experience_type']);
            $query->andWhere([
                'j.experience_type' => $experienceType,
            ]);
        }

        if ($keywords['add_time_start']) {
            $query->andWhere([
                '>=',
                'j.add_time',
                TimeHelper::dayToBeginTime($keywords['add_time_start']),
            ]);
        }
        if ($keywords['add_time_end']) {
            $query->andWhere([
                '<=',
                'j.add_time',
                TimeHelper::dayToEndTime($keywords['add_time_end']),
            ]);
        }
        if ($keywords['release_time_start']) {
            $query->andWhere([
                '>=',
                'j.refresh_time',
                TimeHelper::dayToBeginTime($keywords['release_time_start']),
            ]);
        }
        if ($keywords['release_time_end']) {
            $query->andWhere([
                '<=',
                'j.refresh_time',
                TimeHelper::dayToEndTime($keywords['release_time_end']),
            ]);
        }
        if ($keywords['refresh_time_start']) {
            $query->andWhere([
                '>=',
                'j.real_refresh_time',
                TimeHelper::dayToBeginTime($keywords['refresh_time_start']),
            ]);
        }
        if ($keywords['refresh_time_end']) {
            $query->andWhere([
                '<=',
                'j.real_refresh_time',
                TimeHelper::dayToEndTime($keywords['refresh_time_end']),
            ]);
        }
        if ($keywords['first_release_time_end'] && $keywords['first_release_time_start']) {
            $query->andWhere([
                'between',
                'j.first_release_time',
                TimeHelper::dayToBeginTime($keywords['first_release_time_start']),
                TimeHelper::dayToEndTime($keywords['first_release_time_end']),
            ]);
        }

        if ($keywords['establishment_type']) {
            // 这里会有几种可能性,-1和其他值(-1的情况下是没有选编制的类型,其他值是选了编制类型里面某个并且find_in_set
            $establishmentType = explode(',', $keywords['establishment_type']);
            $orWhere           = [
                'or',
            ];
            foreach ($establishmentType as $itemKey => $establishmentItem) {
                if ($establishmentItem == -1) {
                    $orWhere[] = ['j.is_establishment' => BaseJob::IS_ESTABLISHMENT_NO];
                } else {
                    $establishmentItemKey = 'establishmentItem' . $itemKey;
                    $orWhere[]            = new Expression("FIND_IN_SET(:" . $establishmentItemKey . ", j.establishment_type)",
                        [$establishmentItemKey => $establishmentItem]);
                }
            }
            $query->andWhere($orWhere);
        }

        if ($keywords['company']) {
            if (strlen(intval($keywords['company'])) == 8) {
                $query->andWhere(['c.uuid' => $keywords['company']]);
            } else {
                $query->andWhere([
                    'like',
                    'c.full_name',
                    $keywords['company'],
                ]);
            }
        }

        // 单位类型
        if ($keywords['company_type']) {
            $query->andWhere([
                'c.type' => explode(',', $keywords['company_type']),
            ]);
        }

        // 单位性质
        if ($keywords['company_nature']) {
            $query->andWhere([
                'c.nature' => explode(',', $keywords['company_nature']),
            ]);
        }

        $orderBy = ' j.status desc,j.add_time desc';

        /** 外部表*/
        //筛选报名方式
        $isAnnouncement = 0;
        if ($keywords['apply_type']) {
            $query->leftJoin(['an' => Announcement::tableName()], 'an.id = j.announcement_id');
            array_push($select, [
                'an.title as announcementTitle',
            ]);
            $isAnnouncement = 1;

            $query->andWhere([
                'or',
                [
                    'j.delivery_type' => $keywords['apply_type'],
                ],
                [
                    // 如果job.apply_type = 0,则查询an.apply_type,只能用case来查询
                    'and',
                    ['j.delivery_type' => 0],
                    ['an.delivery_type' => $keywords['apply_type']],
                ],
            ]);
        }
        if ($keywords['announcement']) {
            if ($isAnnouncement == 0) {
                $query->leftJoin(['an' => Announcement::tableName()], 'an.id = j.announcement_id');
                array_push($select, [
                    'an.title as announcementTitle',
                ]);
            }
            $isAnnouncement = 1;
            $query->innerJoin(['an' => Announcement::tableName()], 'an.id = j.announcement_id');
            $query->andWhere([
                'or',
                [
                    'like',
                    'an.title',
                    $keywords['announcement'],
                ],
                ['an.uuid' => $keywords['announcement']],
            ]);
        }
        //筛选投递类型
        if ($keywords['delivery_type']) {
            if ($isAnnouncement == 0) {
                $query->leftJoin(['an' => Announcement::tableName()], 'an.id = j.announcement_id');
                array_push($select, [
                    'an.title as announcementTitle',
                ]);
            }
            $isAnnouncement = 1;
            $query->andWhere([
                'or',
                ['j.delivery_type' => $keywords['delivery_type']],
                [
                    'and',
                    ['j.delivery_type' => 0],
                    ['an.delivery_type' => $keywords['delivery_type']],
                ],
            ]);
        }
        /** 外部表*/

        $query->orderBy($orderBy);

        return $query;
    }

    /**
     * 根据条件获取职位列表
     * @param $keywords
     * @return array
     * @throws Exception
     * @throws \Exception
     */
    public static function getJobList($keywords): array
    {
        $adminId = Yii::$app->user->id;
        //职位列表
        $jobList = self::validateSearchKeyWords($keywords);

        $jobStatusList      = self::JOB_STATUS_NAME;
        $jobAuditStatusList = self::JOB_AUDIT_STATUS_NAME;
        $educationTypeList  = BaseDictionary::getEducationList();
        $experienceList     = BaseDictionary::getExperienceList();
        //$isShowName         = BaseJob::IS_SHOW_NAME;
        $year = date('Y', time());
        // 日期时间格式数据数组
        $timeArray = [
            'add_time',
            'period_date',
            'refresh_time',
            'release_time',
            'delete_time',
            'real_refresh_time',
            'first_release_time',
        ];

        foreach ($jobList['list'] as &$job) {
            $experienceTypeTitle = $experienceList[intval($job['experience_type'])];
            $educationTypeTitle  = $educationTypeList[$job['education_type']];
            if ($job['education_type'] < 1 || strlen($job['education_type']) < 1) {
                $educationTypeTitle = '学历不限';
            }
            if ($job['experience_type'] < 1 || strlen($job['experience_type']) < 1) {
                $experienceTypeTitle = '经验不限';
            }
            $city = Area::getAreaName($job['city_id']);
            //输出薪资
            $wage = BaseJob::formatWage($job['min_wage'], $job['max_wage'], $job['wage_type']);

            $job['basicInformation'] = $experienceTypeTitle . '|' . $educationTypeTitle . '|' . $city . "｜" . $wage;
            //关联公告
            $job['announcementTitle'] = $job['title'] ?: '';
            $job['status']            = intval($job['status']);
            $job['statusTitle']       = $jobStatusList[$job['status']];
            $job['auditStatus']       = intval($job['audit_status']);
            $job['auditStatusTitle']  = $jobAuditStatusList[$job['audit_status']];
            $job['deleteDate']        = "";
            $job['id']                = intval($job['id']);
            $job['uid']               = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $job['id']);
            $job['announcementId']    = intval($job['announcement_id']);
            $job['jobApplyNum']       = intval($job['jobApplyNum']);
            $job['jobInterviewNum']   = intval($job['jobInterviewNum']);
            $job['isArticle']         = intval($job['is_article']);
            $job['companyId']         = intval($job['company_id']);
            $job['click']             = intval($job['click']);
            $job['amount']            = intval($job['amount']);
            $job['downloadAmount']    = intval($job['downloadAmount']);

            // 日期时间类型处理
            foreach ($timeArray as $item) {
                if (strtotime($job[$item]) < 1) {
                    $job[$item] = "-";
                } else {
                    $job[$item] = date('Y/m/d', strtotime($job[$item]));
                }
            }

            if (empty($job['creator'])) {
                $job['creator'] = '-';
            }

            //获取职位的限制状态
            $deliveryLimitType = explode(',', $job['delivery_limit_type']);

            if (in_array(BaseJob::DELIVERY_LIMIT_EDUCATION, $deliveryLimitType)) {
                $job['isLimitEducation'] = 1;
            } else {
                $job['isLimitEducation'] = 2;
            }
            if (in_array(BaseJob::DELIVERY_LIMIT_MATERIAL, $deliveryLimitType)) {
                $job['isLimitFile'] = 1;
            } else {
                $job['isLimitFile'] = 2;
            }
            unset($job['delivery_limit_type'], $job['gender_type']);

            // 学科
            $job['majorTxt']     = BaseMajor::getAllMajorName(explode(',', $job['major_id']));
            $job['isMiniappTxt'] = self::IS_MINIAPP_LIST[$job['is_miniapp']];
        }

        if ($keywords['export']) {
            $realKey  = Cache::PC_ADMIN_TABLE_STAGING_FIELD_KEY . ':' . 'jobQuery' . ':' . $adminId;
            $keyValue = Cache::get($realKey) ?: '';
            if (sizeof($keyValue) > 0) {
                $keyValueArray = json_decode($keyValue, true);
                $keyHeaders    = [];
                $headers       = [];
                foreach ($keyValueArray as $key => $keyList) {
                    if ($keyList['select'] && $keyList['name'] != 'operation') {
                        $keyHeaders[] = $keyList['name'];
                        $headers[]    = $keyList['v'];
                    }
                }
                if (!$keyHeaders || !$headers || sizeof($keyHeaders) <> sizeof($headers)) {
                    throw new Exception('当前不可下载');
                }

                $data = [];
                foreach ($jobList['list'] as $k => $val) {
                    $temp = [];
                    foreach ($keyHeaders as $keys) {
                        $temp [] = $val[$keys];
                    }
                    $data[] = $temp;
                }

                $excel    = new Excel();
                $fileName = $excel->export($data, $headers);

                return [
                    'excelUrl' => $fileName,
                ];
            } else {
                throw new Exception('未知下载列');
            }
        } else {
            // todo 这里获取整个列表统计数据
            $jobListStatistics = BaseJob::getJobListStatistics($keywords);
            $amount            = [
                'apply'       => 0,
                'interview'   => (int)$jobListStatistics['jobInterviewNum'],
                'click'       => (int)$jobListStatistics['allClick'],
                'platformNum' => (int)$jobListStatistics['applyTotal']['platformNum'],
                'emailNum'    => (int)$jobListStatistics['applyTotal']['emailNum'],
                'linkNum'     => (int)$jobListStatistics['applyTotal']['linkNum'],
                'all'         => $jobList['page']['count'],
            ];
            $jobList['amount'] = $amount;

            return $jobList;
        }
    }

    public static function getSimpleJobList($keywords)
    {
        $select = [
            'j.id',
            'j.name',
            //'j.update_time',
            'j.refresh_time',
            'j.first_release_time',
            'j.status',
            'j.department',
            'j.city_id',
            'j.period_date',
            'j.code',
            'j.education_type',
            'j.min_wage',
            'j.max_wage',
            'j.is_article',
            'j.audit_status',
            'j.release_time',
            'j.add_time',
            'j.wage_type',
            'j.province_id',
            //'j.remark',
            'j.experience_type',
            'j.offline_type',
            'j.age_type',
            'j.title_type',
            'j.company_id',
            'j.member_id',
            'j.period_date',
            'j.announcement_id',
            'j.click',
            'j.amount',
            'j.political_type',
            'j.abroad_type',
            'j.download_amount',
            'j.gender_type',
            'j.creator',
            'j.delete_time',
            'j.is_show',
            'j.major_id',
            'j.job_category_id',
            'j.real_refresh_time',
            'j.delivery_limit_type',
            'j.is_miniapp',
            'c.full_name as company',
            'art.status as announcementStatus',
            'an.title',
            'an.audit_status as announcementAuditStatus',
        ];

        $query = self::find()
            ->alias('j')
            ->leftJoin(['an' => Announcement::tableName()], 'an.id = j.announcement_id')
            ->leftJoin(['art' => Article::tableName()], 'art.id=an.article_id')
            ->innerJoin(['c' => Company::tableName()], 'c.id=j.company_id')
            ->select($select);

        $query->andWhere([
            'j.status' => [
                self::STATUS_ONLINE,
                self::STATUS_OFFLINE,
            ],
        ]);

        if ($keywords['announcement']) {
            $keywords['announcement_title_num'] = $keywords['announcement'];
        }

        JobApply::uidJudgeWhere($keywords['announcement_title_num'], 'an.id', 'an.title', $query);
        $query->andFilterCompare('c.is_cooperation', BaseCompany::COOPERATIVE_UNIT_YES);
        $query->andFilterCompare('j.creator', $keywords['creator'], 'like');
        $query->andFilterCompare('j.nature_type', $keywords['nature_type']);
        $query->andFilterCompare('j.department', $keywords['department'], 'like');
        $query->andFilterCompare('j.abroad_type', $keywords['abroad_type']);
        $query->andFilterCompare('j.title_type', $keywords['title_type'], 'in');
        $query->andFilterCompare('j.audit_status', $keywords['audit_status']);
        $query->andFilterCompare('j.status', $keywords['status']);
        $query->andFilterCompare('j.age_type', $keywords['age_type']);
        $query->andFilterCompare('j.gender_type', $keywords['gender_type']);
        $query->andFilterCompare('j.political_type', $keywords['political_type']);
        $query->andFilterCompare('j.is_article', $keywords['is_article']);
        $query->andFilterCompare('j.is_miniapp', $keywords['is_miniapp']);
        //筛选投递类型
        if ($keywords['delivery_type']) {
            $query->andWhere([
                'or',
                ['j.delivery_type' => $keywords['delivery_type']],
                [
                    'and',
                    ['j.delivery_type' => 0],
                    ['an.delivery_type' => $keywords['delivery_type']],
                ],
            ]);
        }
        //筛选投递方式
        if ($keywords['delivery_way']) {
            $query->andWhere([
                    'or',
                    ['j.delivery_way' => $keywords['delivery_way']],
                    [
                        'and',
                        ['an.delivery_way' => $keywords['delivery_way']],
                        ['j.delivery_way' => 0],
                    ],
                ]

            );
        }
        //筛选报名方式
        if ($keywords['apply_type']) {
            $query->andWhere([
                    'or',
                    [
                        'like',
                        'j.apply_type',
                        $keywords['apply_type'],
                    ],
                    [
                        'like',
                        'an.apply_type',
                        $keywords['apply_type'],
                    ],
                ]

            );
        }
        if ($keywords['education_type']) {
            $educationType = explode(',', $keywords['education_type']);
            $query->andFilterCompare('j.education_type', $educationType, 'in');
        }

        $query->andFilterCompare('j.experience_type', $keywords['experience_type']);

        if ($keywords['city']) {
            $city = explode(',', $keywords['city']);
            $query->andFilterCompare('j.city_id', $city, 'in');
        }

        if ($keywords['job_category_id']) {
            $jobCategoryId = explode(',', $keywords['job_category_id']);
            $temp          = [];
            foreach ($jobCategoryId as $categoryId) {
                $temp[] = (int)$categoryId;
            }
            $query->andFilterCompare('j.job_category_id', $temp, 'in');
        }

        if ($keywords['name']) {
            if (is_numeric($keywords['name']) && strlen($keywords['name']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['name']);
                $query->andFilterCompare('j.id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('j.name', $keywords['name'], 'like');
            }
        }

        if ($keywords['major_id']) {
            $query->andWhere("find_in_set(" . $keywords['major_id'] . ",j.major_id)");
        }

        if ($keywords['company']) {
            $findIds = BaseCompany::find()
                ->select('id')
                ->where([
                    'like',
                    'concat(full_name,id)',
                    $keywords['company'],
                ])
                ->asArray()
                ->all();

            $companyIds = [];
            foreach ($findIds as $id) {
                $companyIds[] = $id['id'];
            }
            $query->andFilterCompare('j.company_id', $companyIds, 'in');
        }
        if ($keywords['add_time_start']) {
            $query->andWhere([
                '>=',
                'j.add_time',
                TimeHelper::dayToBeginTime($keywords['add_time_start']),
            ]);
        }
        if ($keywords['add_time_end']) {
            $query->andWhere([
                '<=',
                'j.add_time',
                TimeHelper::dayToEndTime($keywords['add_time_end']),
            ]);
        }
        if ($keywords['release_time_start']) {
            $query->andWhere([
                '>=',
                'j.release_time',
                TimeHelper::dayToBeginTime($keywords['release_time_start']),
            ]);
        }
        if ($keywords['release_time_end']) {
            $query->andWhere([
                '<=',
                'j.release_time',
                TimeHelper::dayToEndTime($keywords['release_time_end']),
            ]);
        }
        if ($keywords['refresh_time_start']) {
            $query->andWhere([
                '>=',
                'j.real_refresh_time',
                TimeHelper::dayToBeginTime($keywords['refresh_time_start']),
            ]);
        }
        if ($keywords['refresh_time_end']) {
            $query->andWhere([
                '<=',
                'j.real_refresh_time',
                TimeHelper::dayToEndTime($keywords['refresh_time_end']),
            ]);
        }
        if ($keywords['first_release_time_end'] && $keywords['first_release_time_start']) {
            $query->andWhere([
                'between',
                'j.first_release_time',
                TimeHelper::dayToBeginTime($keywords['first_release_time_start']),
                TimeHelper::dayToEndTime($keywords['first_release_time_end']),
            ]);
        }

        $orderBy = ' j.status desc,j.add_time desc';
        if ($keywords['sort_apply_num']) {
            $sort    = $keywords['sort_apply_num'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' jobApplyNum ' . $sort;
        }
        if ($keywords['sort_interview_num']) {
            $sort    = $keywords['sort_interview_num'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' jobInterviewNum ' . $sort;
        }
        if ($keywords['sort_refresh_time']) {
            $sort    = $keywords['sort_refresh_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.real_refresh_time ' . $sort;
        }
        if ($keywords['sort_release_time']) {
            $sort    = $keywords['sort_release_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.real_refresh_time ' . $sort;
        }
        if ($keywords['sort_amount']) {
            $sort    = $keywords['sort_amount'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.amount ' . $sort;
        }
        if ($keywords['sort_click']) {
            $sort    = $keywords['sort_click'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' allClick ' . $sort;
        }
        if ($keywords['sort_download_num']) {
            $sort    = $keywords['sort_download_num'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.download_amount ' . $sort;
        }
        if ($keywords['sort_add_time']) {
            $sort    = $keywords['sort_add_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.add_time ' . $sort;
        }
        if ($keywords['sort_period_date']) {
            $sort    = $keywords['sort_period_date'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.period_date ' . $sort;
        }
        if ($keywords['sort_delete_time']) {
            $sort    = $keywords['sort_delete_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.delete_time ' . $sort;
        }

        if ($keywords['establishment_type']) {
            // 这里会有几种可能性,-1和其他值(-1的情况下是没有选编制的类型,其他值是选了编制类型里面某个并且find_in_set
            $establishmentType = explode(',', $keywords['establishment_type']);
            $orWhere           = [
                'or',
            ];
            foreach ($establishmentType as $itemKey => $establishmentItem) {
                if ($establishmentItem == -1) {
                    $orWhere[] = ['j.is_establishment' => BaseJob::IS_ESTABLISHMENT_NO];
                } else {
                    $establishmentItemKey = 'establishmentItem' . $itemKey;
                    $orWhere[]            = new Expression("FIND_IN_SET(:" . $establishmentItemKey . ", j.establishment_type)",
                        [$establishmentItemKey => $establishmentItem]);
                }
            }
            $query->andWhere($orWhere);
        }

        $count = $query->count();

        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $jobList  = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        $jobStatusList      = self::JOB_STATUS_NAME;
        $jobAuditStatusList = self::JOB_AUDIT_STATUS_NAME;
        $educationTypeList  = BaseDictionary::getEducationList();
        $experienceList     = BaseDictionary::getExperienceList();
        //$isShowName         = BaseJob::IS_SHOW_NAME;
        $year = date('Y', time());
        // 日期时间格式数据数组
        $timeArray = [
            'add_time',
            'period_date',
            'refresh_time',
            'release_time',
            'delete_time',
            'real_refresh_time',
            'first_release_time',
        ];

        foreach ($jobList as &$job) {
            $experienceTypeTitle = $experienceList[intval($job['experience_type'])];
            $educationTypeTitle  = $educationTypeList[$job['education_type']];
            if ($job['education_type'] < 1 || strlen($job['education_type']) < 1) {
                $educationTypeTitle = '学历不限';
            }
            if ($job['experience_type'] < 1 || strlen($job['experience_type']) < 1) {
                $experienceTypeTitle = '经验不限';
            }
            $city = Area::getAreaName($job['city_id']);
            //输出薪资
            $wage = BaseJob::formatWage($job['min_wage'], $job['max_wage'], $job['wage_type']);

            $job['basicInformation'] = $experienceTypeTitle . '|' . $educationTypeTitle . '|' . $city . "｜" . $wage;
            //关联公告
            $job['announcementTitle'] = $job['title'] ?: '';
            $job['status']            = intval($job['status']);
            $job['statusTitle']       = $jobStatusList[$job['status']];
            $job['auditStatus']       = intval($job['audit_status']);
            $job['auditStatusTitle']  = $jobAuditStatusList[$job['audit_status']];
            $job['deleteDate']        = "";
            $job['id']                = intval($job['id']);
            $job['uid']               = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $job['id']);
            $job['announcementId']    = intval($job['announcement_id']);
            $job['jobApplyNum']       = intval($job['jobApplyNum']);
            $job['jobInterviewNum']   = intval($job['jobInterviewNum']);
            $job['isArticle']         = intval($job['is_article']);
            $job['companyId']         = intval($job['company_id']);
            $job['click']             = intval($job['click']);
            $job['amount']            = intval($job['amount']);
            $job['downloadAmount']    = intval($job['downloadAmount']);

            // 日期时间类型处理
            foreach ($timeArray as $item) {
                if (strtotime($job[$item]) < 1) {
                    $job[$item] = "-";
                } else {
                    $job[$item] = date('Y/m/d', strtotime($job[$item]));
                }
            }

            if (empty($job['creator'])) {
                $job['creator'] = '-';
            }
            //获取职位的限制状态
            $deliveryLimitType = explode(',', $job['delivery_limit_type']);

            if (in_array(BaseJob::DELIVERY_LIMIT_EDUCATION, $deliveryLimitType)) {
                $job['isLimitEducation'] = 1;
            } else {
                $job['isLimitEducation'] = 2;
            }
            if (in_array(BaseJob::DELIVERY_LIMIT_MATERIAL, $deliveryLimitType)) {
                $job['isLimitFile'] = 1;
            } else {
                $job['isLimitFile'] = 2;
            }
            unset($job['delivery_limit_type'], $job['gender_type']);

            // 学科
            $job['majorTxt']     = BaseMajor::getAllMajorName(explode(',', $job['major_id']));
            $job['isMiniappTxt'] = self::IS_MINIAPP_LIST[$job['is_miniapp']];
        }

        return [
            'list' => $jobList,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];
    }

    /**
     * 校验搜索字段并返回查询职位列表
     * @param     $keywords
     * @return array
     * @throws Exception
     */
    public static function validateSearchKeyWords($keywords): array
    {
        $select = [
            'j.id',
            'j.name',
            //'j.update_time',
            'j.refresh_time',
            'j.first_release_time',
            'j.status',
            'j.department',
            'j.city_id',
            'j.period_date',
            'j.code',
            'j.education_type',
            'j.min_wage',
            'j.max_wage',
            'j.is_article',
            'j.audit_status',
            'j.release_time',
            'j.add_time',
            'j.wage_type',
            'j.province_id',
            //'j.remark',
            'j.experience_type',
            'j.offline_type',
            'j.age_type',
            'j.title_type',
            'j.company_id',
            'j.member_id',
            'j.period_date',
            'j.announcement_id',
            'j.click',
            'j.amount',
            'j.political_type',
            'j.abroad_type',
            'j.download_amount',
            'j.gender_type',
            //'j.create_type',
            //'j.create_id',
            'j.creator',
            'j.delete_time',
            'j.is_miniapp',
            'j.is_show',
            'j.major_id',
            'j.job_category_id',
            'j.real_refresh_time',
            'j.delivery_limit_type',
            'c.full_name as company',
            'art.status as announcementStatus',
            'an.title',
            'an.audit_status as announcementAuditStatus',
            //---单文超注释  前端搜索没有搜索到使用位置
            //'art.home_column_id',
            //'art.home_sub_column_ids',
            'art.status as articleStatus',
            'SUM(j.click) as allClick',
            //'GROUP_CONCAT(a.id SEPARATOR "|") as ids',
            //            'COUNT(a.job_id) as jobApplyNum',
            'jobApplyNum' => BaseJob::find()
                ->alias('jn')
                ->select(['count(jar.id)'])
                ->leftJoin(['jar' => BaseJobApplyRecord::tableName()], 'jn.id=jar.job_id')
                ->where('j.id=jn.id'),

            'COUNT(CASE WHEN a.is_invitation>0 THEN 1 ElSE null END) as jobInterviewNum',
        ];

        $query = self::find()
            ->alias('j')
            ->leftJoin(['a' => JobApply::tableName()], 'j.id = a.job_id')
            ->leftJoin(['an' => Announcement::tableName()], 'an.id = j.announcement_id')
            ->leftJoin(['art' => Article::tableName()], 'art.id=an.article_id')
            ->leftJoin(['c' => Company::tableName()], 'c.id=j.company_id')
            //->leftJoin(['jar'=>BaseJobApplyRecord::tableName()],'j.id=jar.job_id')
            ->select($select)
            ->groupBy(['j.id']);

        $query->andFilterCompare('j.status', [
            self::STATUS_ONLINE,
            self::STATUS_OFFLINE,
        ], 'in');

        if ($keywords['announcement']) {
            $keywords['announcement_title_num'] = $keywords['announcement'];
        }

        JobApply::uidJudgeWhere($keywords['announcement_title_num'], 'an.id', 'an.title', $query);
        $query->andFilterCompare('c.is_cooperation', BaseCompany::COOPERATIVE_UNIT_YES);
        $query->andFilterCompare('j.creator', $keywords['creator'], 'like');
        $query->andFilterCompare('j.nature_type', $keywords['nature_type']);
        $query->andFilterCompare('j.department', $keywords['department'], 'like');
        $query->andFilterCompare('j.abroad_type', $keywords['abroad_type']);
        $query->andFilterCompare('j.title_type', $keywords['title_type'], 'in');
        $query->andFilterCompare('j.audit_status', $keywords['audit_status']);
        $query->andFilterCompare('j.status', $keywords['status']);
        $query->andFilterCompare('j.age_type', $keywords['age_type']);
        $query->andFilterCompare('j.gender_type', $keywords['gender_type']);
        $query->andFilterCompare('j.political_type', $keywords['political_type']);
        $query->andFilterCompare('j.is_article', $keywords['is_article']);
        $query->andFilterCompare('j.is_miniapp', $keywords['is_miniapp']);
        //筛选投递类型
        if ($keywords['delivery_type']) {
            $query->andWhere([
                'or',
                ['j.delivery_type' => $keywords['delivery_type']],
                [
                    'and',
                    ['j.delivery_type' => 0],
                    ['an.delivery_type' => $keywords['delivery_type']],
                ],
            ]);
        }
        //筛选投递方式
        if ($keywords['delivery_way']) {
            $query->andWhere([
                    'or',
                    ['j.delivery_way' => $keywords['delivery_way']],
                    [
                        'and',
                        ['an.delivery_way' => $keywords['delivery_way']],
                        ['j.delivery_way' => 0],
                    ],
                ]

            );
        }
        //筛选报名方式
        if ($keywords['apply_type']) {
            $query->andWhere([
                    'or',
                    [
                        'like',
                        'j.apply_type',
                        $keywords['apply_type'],
                    ],
                    [
                        'like',
                        'an.apply_type',
                        $keywords['apply_type'],
                    ],
                ]

            );
        }
        if ($keywords['education_type']) {
            $educationType = explode(',', $keywords['education_type']);
            $query->andFilterCompare('j.education_type', $educationType, 'in');
        }

        $query->andFilterCompare('j.experience_type', $keywords['experience_type']);

        if ($keywords['city']) {
            $city = explode(',', $keywords['city']);
            $query->andFilterCompare('j.city_id', $city, 'in');
        }

        if ($keywords['job_category_id']) {
            $jobCategoryId = explode(',', $keywords['job_category_id']);
            $temp          = [];
            foreach ($jobCategoryId as $categoryId) {
                $temp[] = (int)$categoryId;
            }
            $query->andFilterCompare('j.job_category_id', $temp, 'in');
        }

        if ($keywords['name']) {
            if (is_numeric($keywords['name']) && strlen($keywords['name']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['name']);
                $query->andFilterCompare('j.id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('j.name', $keywords['name'], 'like');
            }
        }

        if ($keywords['major_id']) {
            $query->andWhere("find_in_set(" . $keywords['major_id'] . ",j.major_id)");
        }

        if ($keywords['company']) {
            $findIds = BaseCompany::find()
                ->select('id')
                ->where([
                    'like',
                    'concat(full_name,id)',
                    $keywords['company'],
                ])
                ->asArray()
                ->all();

            $companyIds = [];
            foreach ($findIds as $id) {
                $companyIds[] = $id['id'];
            }
            $query->andFilterCompare('j.company_id', $companyIds, 'in');
        }
        if ($keywords['add_time_start']) {
            $query->andWhere([
                '>=',
                'j.add_time',
                TimeHelper::dayToBeginTime($keywords['add_time_start']),
            ]);
        }
        if ($keywords['add_time_end']) {
            $query->andWhere([
                '<=',
                'j.add_time',
                TimeHelper::dayToEndTime($keywords['add_time_end']),
            ]);
        }
        if ($keywords['release_time_start']) {
            $query->andWhere([
                '>=',
                'j.release_time',
                TimeHelper::dayToBeginTime($keywords['release_time_start']),
            ]);
        }
        if ($keywords['release_time_end']) {
            $query->andWhere([
                '<=',
                'j.release_time',
                TimeHelper::dayToEndTime($keywords['release_time_end']),
            ]);
        }
        if ($keywords['refresh_time_start']) {
            $query->andWhere([
                '>=',
                'j.real_refresh_time',
                TimeHelper::dayToBeginTime($keywords['refresh_time_start']),
            ]);
        }
        if ($keywords['refresh_time_end']) {
            $query->andWhere([
                '<=',
                'j.real_refresh_time',
                TimeHelper::dayToEndTime($keywords['refresh_time_end']),
            ]);
        }
        if ($keywords['first_release_time_end'] && $keywords['first_release_time_start']) {
            $query->andWhere([
                'between',
                'j.first_release_time',
                TimeHelper::dayToBeginTime($keywords['first_release_time_start']),
                TimeHelper::dayToEndTime($keywords['first_release_time_end']),
            ]);
        }

        $orderBy = ' j.status desc,j.add_time desc';
        if ($keywords['sort_apply_num']) {
            $sort    = $keywords['sort_apply_num'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' jobApplyNum ' . $sort;
        }
        if ($keywords['sort_interview_num']) {
            $sort    = $keywords['sort_interview_num'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' jobInterviewNum ' . $sort;
        }
        if ($keywords['sort_refresh_time']) {
            $sort    = $keywords['sort_refresh_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.real_refresh_time ' . $sort;
        }
        if ($keywords['sort_release_time']) {
            $sort    = $keywords['sort_release_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.real_refresh_time ' . $sort;
        }
        if ($keywords['sort_amount']) {
            $sort    = $keywords['sort_amount'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.amount ' . $sort;
        }
        if ($keywords['sort_click']) {
            $sort    = $keywords['sort_click'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' allClick ' . $sort;
        }
        if ($keywords['sort_download_num']) {
            $sort    = $keywords['sort_download_num'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.download_amount ' . $sort;
        }
        if ($keywords['sort_add_time']) {
            $sort    = $keywords['sort_add_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.add_time ' . $sort;
        }
        if ($keywords['sort_period_date']) {
            $sort    = $keywords['sort_period_date'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.period_date ' . $sort;
        }
        if ($keywords['sort_delete_time']) {
            $sort    = $keywords['sort_delete_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.delete_time ' . $sort;
        }

        if ($keywords['establishment_type']) {
            // 这里会有几种可能性,-1和其他值(-1的情况下是没有选编制的类型,其他值是选了编制类型里面某个并且find_in_set
            $establishmentType = explode(',', $keywords['establishment_type']);
            $orWhere           = [
                'or',
            ];
            foreach ($establishmentType as $itemKey => $establishmentItem) {
                if ($establishmentItem == -1) {
                    $orWhere[] = ['j.is_establishment' => BaseJob::IS_ESTABLISHMENT_NO];
                } else {
                    $establishmentItemKey = 'establishmentItem' . $itemKey;
                    $orWhere[]            = new Expression("FIND_IN_SET(:" . $establishmentItemKey . ", j.establishment_type)",
                        [$establishmentItemKey => $establishmentItem]);
                }
            }
            $query->andWhere($orWhere);
        }

        $count    = $query->count();
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $jobList  = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        return [
            'list' => $jobList,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];
    }

    /**
     * 获取职位详情
     * @throws Exception
     * @throws \Exception
     */
    public static function getDetails($data)
    {
        //查询职位详情
        $jobSelect  = [
            'id',
            'status',
            'member_id',
            'company_id',
            'is_article',
            'name',
            'period_date',
            'is_stick',
            'code',
            'job_category_id',
            'education_type',
            'major_id',
            'nature_type',
            'is_negotiable',
            'wage_type',
            'min_wage',
            'max_wage',
            'experience_type',
            'age_type',
            'min_age',
            'max_age',
            'title_type',
            'political_type',
            'abroad_type',
            'amount',
            'department',
            'province_id',
            'city_id',
            'district_id',
            'address',
            'welfare_tag',
            'duty',
            'requirement',
            'remark',
            'audit_status',
            'is_show',
            'address',
            'apply_type',
            'apply_address',
            'release_time',
            'add_time',
            'file_ids',
            'delivery_limit_type',
            'delivery_type',
            'extra_notify_address',
            'delivery_way',
            'establishment_type',
            'announcement_id as announcementId',
            'is_establishment as isEstablishment',
            'establishment_type as establishmentType',
        ];
        $jobWhere   = ['id' => $data['id']];
        $jobDetails = self::selectInfo($jobWhere, $jobSelect);
        $jobDetails = self::transformation($jobDetails);
        //投递类型文本
        if (empty($jobDetails['delivery_type'])) {//职位没有就显示公告的投递类型
            if (!empty($jobDetails['announcementId'])) {
                $deliveryType                    = BaseAnnouncement::findOneVal(['id' => $jobDetails['announcementId']],
                    'delivery_type');
                $jobDetails['delivery_type_txt'] = BaseAnnouncement::DELIVERY_TYPE_NAME[$deliveryType] ?? '-';
            } else {
                $jobDetails['delivery_type_txt'] = '-';
            }
        } else {//显示职位的投递类型
            $jobDetails['delivery_type_txt'] = BaseJob::DELIVERY_TYPE_NAME[$jobDetails['delivery_type']] ?? '-';
        }
        //投递方式
        $jobDetails['delivery_way_txt'] = '-';
        if (empty($jobDetails['delivery_way'])) {//职位没有就显示公告的投递类型
            if (!empty($jobDetails['announcementId'])) {
                $delivery_way = BaseAnnouncement::findOneVal(['id' => $jobDetails['announcementId']], 'delivery_way');
                if ($delivery_way == BaseAnnouncement::DELIVERY_WAY_PLATFORM) {
                    $jobDetails['delivery_way_txt'] = BaseAnnouncement::DELIVERY_WAY_SELECT_NAME[$delivery_way];
                } elseif (in_array($delivery_way, BaseAnnouncement::DELIVERY_WAY_EMAIL_LINK_LIST)) {
                    $jobDetails['delivery_way_txt'] = BaseAnnouncement::DELIVERY_WAY_SELECT_NAME[BaseAnnouncement::DELIVERY_WAY_EMAIL_LINK];
                    $jobDetails['delivery_way']     = strval(BaseAnnouncement::DELIVERY_WAY_EMAIL_LINK);
                }
            }
        } else {//显示职位的投递类型
            if ($jobDetails['delivery_way'] == BaseJob::DELIVERY_WAY_PLATFORM) {
                $jobDetails['delivery_way_txt'] = BaseJob::DELIVERY_WAY_SELECT_NAME[BaseJob::DELIVERY_WAY_PLATFORM];
            } else {
                $jobDetails['delivery_way']     = strval(BaseJob::DELIVERY_WAY_EMAIL_LINK);
                $jobDetails['delivery_way_txt'] = BaseJob::DELIVERY_WAY_SELECT_NAME[BaseJob::DELIVERY_WAY_EMAIL_LINK];
                $jobDetails['delivery_way']     = strval(BaseJob::DELIVERY_WAY_EMAIL_LINK);
            }
        }

        if ($jobDetails['period_date'] == TimeHelper::ZERO_TIME) {
            $jobDetails['period_date'] = '详见正文';
        }
        if ($jobDetails['release_time'] == TimeHelper::ZERO_TIME) {
            $jobDetails['release_time'] = $jobDetails['add_time'];
        }

        if ($jobDetails['experience_type'] == '0') {
            $jobDetails['experience_type'] = '';
        }
        if ($jobDetails['political_type'] == '0') {
            $jobDetails['political_type'] = '';
        }
        if ($jobDetails['abroad_type'] == '0') {
            $jobDetails['abroad_type'] = '';
        }
        if ($jobDetails['nature_type'] == '0') {
            $jobDetails['nature_type'] = '';
        }
        if ($jobDetails['wage_type'] == '0') {
            $jobDetails['wage_type'] = '';
        }
        if ($jobDetails['age_type'] == '0') {
            $jobDetails['age_type'] = '';
        }
        if ($jobDetails['title_type'] == '0') {
            $jobDetails['title_type'] = '';
        }
        if ($jobDetails['min_wage'] == '0') {
            $jobDetails['min_wage'] = '';
        }
        if ($jobDetails['max_wage'] == '0') {
            $jobDetails['max_wage'] = '';
        }
        if ($jobDetails['min_age'] == '0') {
            $jobDetails['min_age'] = '';
        }
        if ($jobDetails['max_age'] == '0') {
            $jobDetails['max_age'] = '';
        }
        if ($jobDetails['district_id'] == '0') {
            $jobDetails['district_id'] = '';
        }
        if ($jobDetails['period_date'] == TimeHelper::ZERO_TIME) {
            $jobDetails['period_date'] = '';
        }
        if ($jobDetails['amount'] == '-1') {
            $jobDetails['amount'] = '若干';
        }

        if (empty($jobDetails['major_id'])) {
            $jobDetails['majorTitle'] = StringHelper::isEmpty($jobDetails['major_id']);
        }
        if (empty($jobDetails['nature_type'])) {
            $jobDetails['natureTypeTitle'] = StringHelper::isEmpty($jobDetails['nature_type']);
        }
        if (empty($jobDetails['age_type'])) {
            $jobDetails['age_type'] = StringHelper::isEmpty($jobDetails['age_type']);
        }
        if (empty($jobDetails['title_type'])) {
            $jobDetails['titleTypeTitle'] = StringHelper::isEmpty($jobDetails['title_type']);
        }
        if (empty($jobDetails['political_type'])) {
            $jobDetails['politicalTypeTitle'] = StringHelper::isEmpty($jobDetails['political_type']);
        }
        if (empty($jobDetails['abroad_type'])) {
            $jobDetails['abroadTypeTitle'] = StringHelper::isEmpty($jobDetails['abroad_type']);
        }
        if (empty($jobDetails['department'])) {
            $jobDetails['department'] = StringHelper::isEmpty($jobDetails['department']);
        }
        if (empty($jobDetails['remark'])) {
            $jobDetails['remark'] = StringHelper::isEmpty($jobDetails['remark']);
        }
        if (empty($jobDetails['code'])) {
            $jobDetails['code'] = StringHelper::isEmpty($jobDetails['code']);
        }
        if (empty($jobDetails['education_type'])) {
            $jobDetails['educationTypeTitle'] = StringHelper::isEmpty($jobDetails['education_type']);
        }
        if (empty($jobDetails['experience_type'])) {
            $jobDetails['experienceTypeTitle'] = StringHelper::isEmpty($jobDetails['experience_type']);
        }

        $jobDetails['applyAddress']   = $jobDetails['apply_address'] ?: '';
        $jobDetails['applyTypeTitle'] = BaseJob::getApplyTypeName($jobDetails['apply_type']) ?: '';

        //薪资wage_id回显
        if ($jobDetails['is_negotiable'] <> 1) {
            $jobDetails['wage_id'] = (string)BaseJob::getWageId($jobDetails['min_wage'], $jobDetails['max_wage']);
        }
        if (!$jobDetails['min_wage'] && !$jobDetails['max_wage']) {
            $jobDetails['wage'] = '面议';
        } else {
            $jobDetails['wage'] = BaseJob::formatWage($jobDetails['min_wage'], $jobDetails['max_wage'],
                $jobDetails['wage_type']) ?: '-';
        }

        //查询福利标签
        $welfareLabelWhere         = ['id' => explode(',', $jobDetails['welfare_tag']),];
        $welfareLabelSelect        = [
            'id',
            'name',
        ];
        $welfareLabelList          = BaseWelfareLabel::findList($welfareLabelWhere, $welfareLabelSelect);
        $welfareTag                = [];
        $jobDetails['welfareTage'] = [];
        foreach ($welfareLabelList as $k => $welfareLabel) {
            $welfareTag[$k]['k']       = $welfareLabel['id'];
            $welfareTag[$k]['v']       = $welfareLabel['name'];
            $jobDetails['welfareTage'] = $welfareTag;
        }

        //关联公司信息
        $companyTypeList                      = BaseDictionary::getCompanyTypeList();
        $companyInfo                          = BaseCompany::getCompanyInfo(intval($jobDetails['member_id']));
        $jobDetails['extra_notify_address']   = $jobDetails['extra_notify_address'] ?: '';
        $jobDetails['companyFullName']        = $companyInfo['full_name'];
        $jobDetails['companyNature']          = intval($companyInfo['nature']);
        $jobDetails['companyNatureTitle']     = Dictionary::getCompanyNatureName($companyInfo['nature']);
        $jobDetails['companyTypeTitle']       = $companyTypeList[$companyInfo['type']];
        $jobDetails['areaName']               = $jobDetails['provinceTitle'] . $jobDetails['cityTitle'];
        $jobDetails['isCooperation']          = $companyInfo['is_cooperation'];
        $jobDetails['companyDeliveryType']    = $companyInfo['delivery_type'];
        $jobDetails['companyDeliveryTypeTxt'] = BaseCompany::DELIVERY_TYPE_NAME[$companyInfo['delivery_type']];

        //投递限制
        $jobDetails['deliveryLimitTypeTxt'] = '';
        if ($jobDetails['delivery_limit_type']) {
            $deliveryLimitTypeArr = explode(',', $jobDetails['delivery_limit_type']);
            foreach ($deliveryLimitTypeArr as $val) {
                $jobDetails['deliveryLimitTypeTxt'] .= self::DELIVERY_LIMIT_LIST[$val] . ';';
            }

            $jobDetails['deliveryLimitTypeTxt'] = substr($jobDetails['deliveryLimitTypeTxt'], 0, -1);
        }

        //职位附件
        if ($jobDetails['file_ids']) {
            $jobDetails['fileList'] = BaseAnnouncement::getAppendixList($jobDetails['file_ids']);
        } else {
            if (!empty($jobDetails['announcementId'])) {
                $fileIds                = BaseAnnouncement::findOneVal(['id' => $jobDetails['announcementId']],
                    'file_ids');
                $jobDetails['fileList'] = BaseAnnouncement::getAppendixList($fileIds);
            } else {
                $jobDetails['fileList'] = [];
            }
        }

        // 编制相关文案拼接
        if ($jobDetails['isEstablishment'] == self::IS_ESTABLISHMENT_YES) {
            $jobDetails['establishmentTxt'] = self::getEstablishmentName($jobDetails['establishmentType']);
        }
        //单位子账号配置
        $company_sub_account_config               = BaseCompanyMemberConfig::findOne(['company_id' => $jobDetails['company_id']]);
        $jobDetails['company_sub_account_config'] = $company_sub_account_config ? $company_sub_account_config->toArray() : [];
        //职位联系人
        $contact                      = self::getJobContact($data['id']);
        $jobDetails['job_contact']    = $contact;
        $jobDetails['job_contact_id'] = $contact['company_member_info_id'];
        //职位协同账号
        $contact_synergy                       = self::getJobContactSynergy($data['id']);
        $jobDetails['job_contact_synergy']     = $contact_synergy;
        $jobDetails['job_contact_synergy_num'] = count($contact_synergy);
        $jobDetails['job_contact_synergy_ids'] = array_column($contact_synergy, 'company_member_info_id');

        return $jobDetails;
    }

    /**
     * 编辑职位
     * @param $data
     * @throws Exception
     * @throws \Exception
     */
    public static function jobEdit($data)
    {
        $adminId   = Yii::$app->user->id;
        $adminInfo = BaseAdmin::find()
            ->where(['id' => $adminId])
            ->select('username')
            ->asArray()
            ->one();
        $select    = [
            'duty',
            'requirement',
            'remark',
            'announcement_id',
            'status',
            'is_article',
            'delivery_type',
            'company_id',
        ];
        $jobInfo   = self::selectInfo(['id' => $data['job_id']], $select);

        //薪资范围
        if ($data['wage_type'] != BaseJob::WAGE_TYPE_YEAR) {
            if ($data['is_negotiable'] == 2) {
                if (!empty($data['wage_id'])) {
                    $wageInfo         = BaseDictionary::getMinAndMaxWage($data['wage_id']);
                    $data['min_wage'] = (int)$wageInfo['min'];
                    $data['max_wage'] = (int)$wageInfo['max'];
                }
            }
        }
        //判断投递方式
        // 应聘方式校验
        if ($data['apply_type']) {
            $applyTypeArr = explode(',', $data['apply_type']);
            $isEmail      = in_array(BaseJob::ATTRIBUTE_APPLY_EMAIL, $applyTypeArr);
            if ($isEmail) {
                BaseJob::checkEmailApplyAddress($data['apply_address']);
            } else {
                if (!ValidateHelper::isUrl($data['apply_address'])) {
                    throw new Exception('单位报名网址格式错误');
                }
            }
            //判断投递方式
            if (!isset($data['delivery_way']) || (isset($data['delivery_way']) && $data['delivery_way'] == BaseJob::DELIVERY_WAY_EMAIL_LINK)) {
                if ($isEmail) {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_EMAIL;
                } else {
                    $data['delivery_way'] = BaseJob::DELIVERY_WAY_LINK;
                }
            }
        } else {
            //判断投递方式
            if (!isset($data['delivery_way'])) {
                $data['delivery_way'] = BaseJob::DELIVERY_WAY_PLATFORM;
            }
        }
        $companyInfo = BaseCompany::findOne($jobInfo['company_id']);
        if ($companyInfo['is_cooperation'] == BaseAnnouncement::IS_COOPERATION_YES) {
            if ($data['delivery_way'] == BaseJob::DELIVERY_WAY_LINK) {
                $data['delivery_type'] = BaseJob::DELIVERY_TYPE_OUTER;
            } else {
                $data['delivery_type'] = BaseJob::DELIVERY_TYPE_OUTSIDE;
            }
        } else {
            $data['delivery_type'] = BaseJob::DELIVERY_TYPE_OUTER;
        }
        //判断职位投递性质
        if ($jobInfo['delivery_type'] != $data['delivery_type']) {
            throw new Exception('你编辑使职位投递类型站内外发生变化，导致修改失败');
        }
        if ($companyInfo->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES && empty($data['job_contact_id'])) {
            throw new Exception('请选择职位联系人');
        }
        //检查通知邮箱的格式
        if ($data['extra_notify_address']) {
            $data['extra_notify_address'] = BaseJob::checkEmailApplyAddress($data['extra_notify_address']);
        }

        // TODO 编辑职位
        $jobId = $data['job_id'];
        $model = self::findOne($jobId);

        // TODO 修改了【岗位职责、任职要求、其他说明】
        $editList       = [
            'duty'        => $data['duty'],
            'requirement' => $data['requirement'],
            'remark'      => $data['remark'],
        ];
        $announcementId = $jobInfo['announcement_id'];
        $jobStatus      = $jobInfo['status'];
        $isArticle      = $jobInfo['is_article'];
        unset($jobInfo['announcement_id'], $jobInfo['status'], $jobInfo['is_article']);
        unset($data['company_name'], $data['area_name'], $data['job_id'], $data['company_nature_title']);

        if (!in_array($jobStatus, BaseJob::JOB_HISTORY_STATUS)) {
            $model->duty         = $data['duty'];
            $model->requirement  = $data['requirement'];
            $model->remark       = $data['remark'];
            $model->status       = self::STATUS_WAIT_AUDIT;
            $model->audit_status = self::AUDIT_STATUS_WAIT_AUDIT;
        } else {
            $modifyAfterList  = array_diff_assoc($editList, $jobInfo);
            $modifyBeforeList = [];
            foreach ($modifyAfterList as $k => $list) {
                $modifyBeforeList[$k] = $jobInfo[$k];
            }

            if (sizeof($modifyAfterList) > 0) {
                $editContent = json_encode($modifyAfterList);
                $list        = [
                    'job_id'          => $jobId,
                    'add_time'        => CUR_DATETIME,
                    'status'          => BaseJobEdit::STATUS_ONLINE,
                    'edit_content'    => $editContent,
                    'editor_id'       => $adminId,
                    'editor_type'     => BaseJobEdit::EDITOR_TYPE_PLATFORM,
                    'editor'          => $adminInfo['username'],
                    'announcement_id' => $announcementId ?: (empty($data['announcement_id']) ? 0 : $data['announcement_id']),
                ];
                // 检测之前是否有职位编辑内容
                $jobEditInfo = BaseJobEdit::selectInfo(['job_id' => $jobId], ['id']);
                if ($jobEditInfo['id']) {
                    $condition = ['id' => $jobEditInfo['id']];
                    BaseJobEdit::updateAll($list, $condition);
                } else {
                    BaseJobEdit::createInfo($list);
                }

                //这里存职位操作表
                $changeModifyBeforeList = [];
                $changeModifyAfterList  = [];
                foreach ($modifyBeforeList as $k => $item) {
                    switch ($k) {
                        case 'duty':
                            $changeModifyBeforeList['岗位职责'] = $item;
                            break;
                        case 'requirement':
                            $changeModifyBeforeList['任职要求'] = $item;
                            break;
                        case 'remark':
                            $changeModifyBeforeList['其他说明'] = $item;
                            break;
                    }
                }

                foreach ($modifyAfterList as $k => $item) {
                    switch ($k) {
                        case 'duty':
                            $changeModifyAfterList['岗位职责'] = $item;
                            break;
                        case 'requirement':
                            $changeModifyAfterList['任职要求'] = $item;
                            break;
                        case 'remark':
                            $changeModifyAfterList['其他说明'] = $item;
                            break;
                    }
                }

                $handleBefore = json_encode($changeModifyBeforeList);
                $handleAfter  = json_encode($changeModifyAfterList);
                $jobHandleLog = [
                    'add_time'      => CUR_DATETIME,
                    'job_id'        => $jobId,
                    'handle_type'   => (string)BaseJobHandleLog::HANDLE_TYPE_EDIT,
                    'handler_type'  => BaseJobHandleLog::HANDLER_TYPE_PLAT,
                    'handler_id'    => $adminId,
                    'handler_name'  => $adminInfo['username'],
                    'handle_before' => $handleBefore,
                    'handle_after'  => $handleAfter,
                    'ip'            => IpHelper::getIpInt(),
                ];
                BaseJobHandleLog::createInfo($jobHandleLog);

                // 混合职位模式编辑
                if ($isArticle == self::IS_ARTICLE_YES) {
                    // 修改公告审核状态
                    $announcementModel               = BaseAnnouncement::findOne(['id' => $data['announcement_id']]);
                    $announcementModel->audit_status = BaseAnnouncement::STATUS_AUDIT_AWAIT;
                    if (!$announcementModel->save()) {
                        throw new Exception($announcementModel->getFirstErrorsMessage());
                    }

                    //修改公告下的职位审核状态
                    self::setJobAuditStatus($data['announcement_id']);
                    //创建公告操作日志
                    self::createAnnouncementHandleLog($data['announcement_id'], $handleBefore, $handleAfter);
                } else {
                    //移至待审核--只修改岗位职责、任职要求、其他说明
                    $model->audit_status     = self::AUDIT_STATUS_WAIT_AUDIT;
                    $model->apply_audit_time = CUR_DATETIME;
                }

                if ($jobStatus != BaseJob::STATUS_ONLINE) {
                    $model->status = self::STATUS_WAIT_AUDIT;
                }
            }
        }

        $model->update_time          = CUR_DATETIME;
        $model->period_date          = $data['period_date'] == '详见正文' ? '0000-00-00 00:00:00' : $data['period_date'];
        $model->major_id             = $data['major_id'];
        $model->nature_type          = $data['nature_type'];
        $model->is_negotiable        = $data['is_negotiable'];
        $model->wage_type            = $data['wage_type'];
        $model->min_wage             = $data['min_wage'];
        $model->max_wage             = $data['max_wage'];
        $model->experience_type      = $data['experience_type'];
        $model->title_type           = $data['title_type'];
        $model->age_type             = $data['age_type'];
        $model->political_type       = $data['political_type'];
        $model->abroad_type          = $data['abroad_type'];
        $model->amount               = $data['amount'];
        $model->province_id          = $data['province_id'];
        $model->city_id              = $data['city_id'];
        $model->district_id          = $data['district_id'] ?: 0;
        $model->address              = $data['address'];
        $model->welfare_tag          = $data['welfare_tag'];
        $model->department           = $data['department'];
        $model->announcement_id      = $data['announcement_id'];
        $model->job_category_id      = $data['job_category_id'];
        $model->education_type       = $data['education_type'];
        $model->name                 = $data['name'];
        $model->file_ids             = $data['file_ids'];
        $model->delivery_limit_type  = $data['delivery_limit_type'];
        $model->apply_type           = $data['apply_type'] ?: '';
        $model->apply_address        = $data['apply_address'] ?: '';
        $model->delivery_type        = $data['delivery_type'];
        $model->delivery_way         = $data['delivery_way'] ?: '';
        $model->extra_notify_address = $data['extra_notify_address'] ?: '';
        $model->establishment_type   = $data['establishment_type'] ?: '';
        $model->is_establishment     = $data['establishment_type'] ? BaseJob::IS_ESTABLISHMENT_YES : BaseJob::IS_ESTABLISHMENT_NO;

        if (!$model->save()) {
            throw new Exception($model->getFirstErrorsMessage());
        }
        //判断合作单位
        if ($companyInfo->is_cooperation == BaseAnnouncement::IS_COOPERATION_YES) {
            //写入职位联系人与职位协同
            $contact_insert                           = [
                'job_id'          => $data['id'],
                'company_id'      => $companyInfo->id,
                'announcement_id' => $data['announcement_id'],
            ];
            $contact_synergy_insert                   = $contact_insert;
            $contact_insert['company_member_info_id'] = $data['job_contact_id'];

            BaseJobContact::add($contact_insert);
            $contact_synergy_insert['company_member_info_id'] = $data['job_contact_synergy_ids'];
            BaseJobContactSynergy::addBatch($contact_synergy_insert);
        }
        self::afterUpdate($model->id);
    }

    /**
     * 创建公告操作日志
     * @param $announcementId
     * @param $handleBefore
     * @param $handleAfter
     * @throws Exception
     * @throws \yii\base\NotSupportedException
     */
    private static function createAnnouncementHandleLog($announcementId, $handleBefore, $handleAfter)
    {
        $adminId   = Yii::$app->user->id;
        $adminInfo = BaseAdmin::find()
            ->where(['id' => $adminId])
            ->select('username')
            ->asArray()
            ->one();

        //操作动作入表
        $handleLogArr = [
            'add_time'        => CUR_DATETIME,
            'announcement_id' => $announcementId,
            'handle_type'     => (string)BaseAnnouncementHandleLog::HANDLE_TYPE_EDIT,
            'editor_type'     => Announcement::TYPE_EDITOR_JOB,
            'handler_type'    => BaseAnnouncementHandleLog::HANDLER_TYPE_PLAT,
            'handler_id'      => $adminId,
            'handler_name'    => $adminInfo['username'] ?: '',
            'handle_before'   => $handleBefore,
            'handle_after'    => $handleAfter,
            'ip'              => IpHelper::getIpInt(),
        ];
        BaseAnnouncementHandleLog::createInfo($handleLogArr);
    }

    /**
     * 职位刷新
     * @throws Exception
     */
    public static function jobRefresh1($data)
    {
        try {
            $idArr      = explode(',', $data['id']);
            $jobWhere   = ['and'];
            $jobWhere[] = [
                'in',
                'id',
                $idArr,
            ];
            $jobSelect  = [
                'id',
                'refresh_time',
                'real_refresh_time',
                'name',
            ];
            $jobList    = self::selectInfos($jobWhere, $jobSelect);
            if (!$jobList || (count($jobList) != count($idArr))) {
                throw new Exception('非法请求');
            }

            //关联公告条件
            $nowDateTime = date("Y-m-d H-i-s");
            if (!self::updateAll([
                'refresh_time'      => $nowDateTime,
                'refresh_date'      => CUR_DATE,
                'real_refresh_time' => $nowDateTime,
            ], [
                'in',
                'id',
                $idArr,
            ])) {
                throw new Exception('失败');
            }
            foreach ($jobList as $job) {
                $list                  = [];
                $list['job_id']        = $job['id'];
                $list['handle_type']   = (string)BaseJobHandleLog::HANDLE_TYPE_REFRESH;
                $list['handle_before'] = ['刷新时间' => $job['refresh_time']];
                $list['handle_after']  = ['刷新时间' => $nowDateTime];
                self::createJobHandleLog($list);
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 刷新职位
     * @param $id
     * @return void
     * @throws Exception
     */
    public static function jobRefresh($id)
    {
        try {
            $jobModel = self::findOne(['id' => $id]);
            if (!$jobModel) {
                throw new Exception('职位不存在');
            }
            //取下旧数据
            $logData                  = [];
            $logData['handle_before'] = ['刷新时间' => $jobModel['refresh_time']];

            $jobModel->refresh_time      = CUR_DATETIME;
            $jobModel->refresh_date      = CUR_DATE;
            $jobModel->real_refresh_time = CUR_DATETIME;
            $jobModel->is_first_release  = BaseJob::IS_FIRST_RELEASE_NO;
            if (!$jobModel->save()) {
                throw new Exception('保存失败' . $jobModel->getFirstErrorsMessage());
            }

            $logData['job_id']       = $id;
            $logData['handle_type']  = (string)BaseJobHandleLog::HANDLE_TYPE_REFRESH;
            $logData['handle_after'] = ['刷新时间' => CUR_DATETIME];
            self::createJobHandleLog($logData);

            // 刷公告
            $announcementId = $jobModel->announcement_id;
            if ($announcementId) {
                Producer::afterAnnouncementUpdateJob($announcementId);
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 职位审核
     * @throws Exception
     */
    public static function JobExamine($data)
    {
        try {
            //校验职位数据
            $select = [
                'id',
                'refresh_time',
                'announcement_id',
                'status',
            ];
            $query  = BaseJob::find()
                ->select($select);
            $query->andFilterCompare('id', $data['id']);
            $job = $query->asArray()
                ->one();
            if (!$job) {
                throw new Exception('非法请求，职位数据不存在');
            }

            //修改职位审核状态
            if ($data['audit_status'] == 1) {
                //审核通过
                //将编辑表内容加入职位表
                $jobHandleLog = BaseJobEdit::find()
                    ->where([
                        'job_id' => $data['id'],
                    ])
                    ->select([
                        'edit_content',
                        'id',
                    ])
                    ->orderBy('id desc')
                    ->asArray()
                    ->one();

                $handleAfterMessage = json_decode($jobHandleLog['edit_content'], true);

                $temp = [];
                foreach ($handleAfterMessage as $k => $item) {
                    if (in_array($k, [
                        'duty',
                        'requirement',
                        'remark',
                    ])) {
                        $temp[$k] = $item;
                    }
                }
                if (sizeof($temp) > 0) {
                    BaseJob::updateAll($temp, ['id' => $data['id']]);
                }

                $updateArray = [
                    'status'       => BaseJob::STATUS_ONLINE,
                    'release_time' => CUR_DATETIME,
                    'audit_status' => $data['audit_status'],
                ];
            } else {
                //审核拒绝
                //在线不影响
                if ($job['status'] == BaseJob::STATUS_ONLINE) {
                    $updateArray = [
                        'audit_status' => $data['audit_status'],
                    ];
                } else {
                    $updateArray = [
                        'status'       => BaseJob::STATUS_REFUSE_AUDIT,
                        'audit_status' => $data['audit_status'],
                    ];
                }
            }

            if (!self::updateAll($updateArray, ['id' => (int)$data['id']])) {
                throw new Exception('修改失败');
            }

            //操作动作入表
            $adminId      = Yii::$app->user->id;
            $adminInfo    = BaseAdmin::find()
                ->where(['id' => $adminId])
                ->select('username')
                ->asArray()
                ->one();
            $handleBefore = ['审核状态' => BaseJob::JOB_AUDIT_STATUS_NAME[BaseJob::AUDIT_STATUS_WAIT_AUDIT]];

            $handleAfter  = [
                '审核状态' => BaseJob::JOB_AUDIT_STATUS_NAME[$data['audit_status']],
                'opinion'  => $data['opinion'],
            ];
            $jobHandleLog = [
                'add_time'      => date("Y-m-d H:i:s", time()),
                'job_id'        => $data['id'],
                'handle_type'   => (string)BaseJobHandleLog::HANDLE_TYPE_AUDIT,
                'handler_type'  => BaseUser::TYPE_ADMIN,
                'handler_id'    => $adminId,
                'handler_name'  => $adminInfo['username'],
                'handle_before' => json_encode($handleBefore),
                'handle_after'  => json_encode($handleAfter),
                'ip'            => IpHelper::getIpInt(),
            ];
            BaseJobHandleLog::createInfo($jobHandleLog);
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 职位下线
     * @throws Exception
     */
    public static function JobOffline($data)
    {
        try {
            $service  = new OnlineOfflineService();
            $adminId  = Yii::$app->user->id;
            $id       = $data['id'];
            $jobModel = self::findOne(['id' => $id]);
            if (!$jobModel) {
                throw new Exception('职位不存在');
            }

            $jobData = [
                'id'            => $id,
                'status'        => BaseJob::STATUS_OFFLINE,
                //'auditStatus'   => BaseJob::AUDIT_STATUS_OFFLINE,下线职位不要修改审核状态
                'offlineType'   => BaseJob::OFFLINE_TYPE_VIOLATION,
                'periodDate'    => CUR_DATETIME,
                'offlineReason' => $data['reason'],
            ];

            $jobStatus    = BaseJob::findOneVal(['id' => $id], 'status');
            $handleBefore = [
                '职位状态' => self::JOB_STATUS_NAME[$jobStatus],
            ];
            $handleAfter  = [
                '职位状态'     => self::JOB_STATUS_NAME[self::STATUS_OFFLINE],
                '下线方式'     => '违规下线',
                '下线时间'     => $jobData['periodDate'],
                '违规下线理由' => $data['reason'],
            ];

            $username = BaseAdmin::findOneVal(['id' => $adminId], 'username');

            $jobHandleLog = [
                'add_time'      => date("Y-m-d H:i:s", time()),
                'job_id'        => $id,
                'handle_type'   => (string)BaseJobHandleLog::HANDLE_TYPE_OFFLINE,
                'handler_type'  => BaseJobHandleLog::HANDLER_TYPE_PLAT,
                'handler_id'    => $adminId,
                'handler_name'  => $username,
                'handle_before' => json_encode($handleBefore),
                'handle_after'  => json_encode($handleAfter),
                'ip'            => IpHelper::getIpInt(),
            ];

            $service->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                ->setOffline()
                ->setData($jobData, $jobHandleLog)
                ->run();

            // 刷公告
            if ($jobModel->announcement_id) {
                Producer::afterAnnouncementUpdateJob($jobModel->announcement_id);
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 职位再发布
     * @throws Exception
     */
    public static function jobReleaseAgain1($data)
    {
        try {
            //校验职位数据
            $idArr       = explode(',', $data['id']);
            $jobWhere    = ['and'];
            $jobWhere [] = [
                'in',
                'id',
                $idArr,
            ];
            $jobSelect   = [
                'id',
                'release_time',
                'name',
                'period_date',
                'announcement_id',
            ];
            $jobList     = self::selectInfos($jobWhere, $jobSelect);
            if (!$jobList || (count($jobList) != count($idArr))) {
                throw new Exception('非法请求');
            }

            //此处关联公告，若公告为在线状态，再发布成功后，职位即变成”在线“状态；若公告下线，提示公告要先上线，
            //才能再发布职位；若公告处于待发布状态，提示公告需要审核通过后，才能再发布职位；
            foreach ($jobList as $item) {
                if ($item['announcement_id'] > 0) {
                    $temp = BaseAnnouncement::find()
                        ->alias('a')
                        ->leftJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
                        ->select([
                            'b.status',
                        ])
                        ->where([
                            'a.id' => $item['announcement_id'],
                        ])
                        ->asArray()
                        ->one();

                    if ($temp['status'] == BaseArticle::STATUS_OFFLINE) {
                        throw new Exception('职位关联的公告需先上线');
                    }

                    if ($temp['status'] == BaseArticle::STATUS_STAGING) {
                        throw new Exception('职位关联的公告需要审核通过后，才能再发布职位');
                    }
                }
            }

            $periodDate = Date("Y-m-d H-i-s", (time() + 60 * 60 * 24 * self::ADD_RELEASE_AGAIN_DAY));
            $nowTime    = CUR_DATETIME;
            if (!self::updateAll([
                'status'       => self::STATUS_ONLINE,
                'period_date'  => '0000-00-00 00:00:00',
                'refresh_time' => $nowTime,
                'refresh_date' => CUR_DATE,
            ], [
                'in',
                'id',
                $idArr,
            ])) {
                throw new Exception('失败');
            }
            foreach ($jobList as $job) {
                $list                  = [];
                $list['job_id']        = $job['id'];
                $list['handle_type']   = (string)BaseJobHandleLog::HANDLE_TYPE_RELEASE_AGAIN;
                $list['handle_before'] = ['职位有效期' => $job['period_date']];
                $list['handle_after']  = ['职位有效期' => $periodDate];
                self::createJobHandleLog($list);
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 职位再发布
     * @throws Exception
     */
    public static function jobReleaseAgain($id)
    {
        try {
            $jobModel = self::findOne(['id' => $id]);
            if (!$jobModel) {
                throw new Exception('职位不存在');
            }
            //此处关联公告，若公告为在线状态，再发布成功后，职位即变成”在线“状态；若公告下线，提示公告要先上线，
            //才能再发布职位；若公告处于待发布状态，提示公告需要审核通过后，才能再发布职位；
            if ($jobModel->announcement_id > 0) {
                $temp = BaseAnnouncement::find()
                    ->alias('a')
                    ->leftJoin(['b' => BaseArticle::tableName()], 'b.id=a.article_id')
                    ->select([
                        'b.status',
                    ])
                    ->where([
                        'a.id' => $jobModel->announcement_id,
                    ])
                    ->asArray()
                    ->one();
                if ($temp['status'] == BaseArticle::STATUS_OFFLINE) {
                    throw new Exception('职位关联的公告需先上线');
                }

                if ($temp['status'] == BaseArticle::STATUS_STAGING) {
                    throw new Exception('职位关联的公告需要审核通过后，才能再发布职位');
                }
            }

            $periodDate = Date("Y-m-d H-i-s", (time() + 60 * 60 * 24 * self::ADD_RELEASE_AGAIN_DAY));

            $jobModel->status       = self::STATUS_ONLINE;
            $jobModel->period_date  = '0000-00-00 00:00:00';
            $jobModel->refresh_time = CUR_DATETIME;
            $jobModel->refresh_date = CUR_DATE;
            $jobModel->audit_status = BaseJob::AUDIT_STATUS_PASS_AUDIT;
            $jobModel->offline_type = 0;
            $jobModel->offline_time = '0000-00-00 00:00:00';
            if (!$jobModel->save()) {
                throw new Exception('保存失败' . $jobModel->getFirstErrorsMessage());
            }

            //记录操作日志
            $logData                  = [];
            $logData['job_id']        = $id;
            $logData['handle_type']   = (string)BaseJobHandleLog::HANDLE_TYPE_RELEASE_AGAIN;
            $logData['handle_before'] = ['职位有效期' => $jobModel->period_date];
            $logData['handle_after']  = ['职位有效期' => $periodDate];
            self::createJobHandleLog($logData);

            // 刷公告
            if ($jobModel->announcement_id) {
                Producer::afterAnnouncementUpdateJob($jobModel->announcement_id);
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 业务收到简历列表
     * @param $keywords
     * @return array
     * @throws Exception
     */
    public static function jobApplyList($keywords): array
    {
        $select = [
            'jar.add_time',
            'ja.update_time as outside_update_time',
            'os.update_time as outer_update_time',
            'jar.apply_id',
            'jar.apply_site_id',
            'jar.resume_id',
            'r.name as resume_name',
            'r.member_id',
            'r.id as resume_id',
            'jar.source',
            'ja.is_check',
            'ja.resume_attachment_id as outside_attachment_id',
            'os.resume_attachment_id as outer_attachment_id',
            'ja.is_invitation',
            'jar.delivery_way',
            'ja.status',
        ];
        $query  = BaseJobApplyRecord::find()
            ->alias('jar')
            ->select($select)
            ->leftJoin(['ja' => BaseJobApply::tableName()], 'jar.apply_id = ja.id')
            ->leftJoin(['os' => BaseOffSiteJobApply::tableName()], 'jar.apply_site_id = os.id')
            ->leftJoin(['r' => BaseResume::tableName()], 'jar.resume_id = r.id')
            ->andWhere(['jar.job_id' => $keywords['job_id']]);
        //投递方式
        $query->andFilterCompare('jar.delivery_way', $keywords['delivery_way']);
        $query->andFilterCompare('jar.job_id', $keywords['job_id']);
        $query->andFilterCompare('jar.source', $keywords['source']);
        if ($keywords['resume_name']) {
            if (intval($keywords['resume_name']) > 0 && strlen(intval($keywords['resume_name'])) == 8) {
                $query->andWhere(['jar.resume_id' => UUIDHelper::decryption($keywords['resume_name'])]);
            } else {
                $query->andWhere([
                    'like',
                    'r.name',
                    $keywords['resume_name'],
                ]);
            }
        }
        if ($keywords['add_time_start']) {
            $query->andWhere([
                '>=',
                'jar.add_time',
                TimeHelper::dayToBeginTime($keywords['add_time_start']),
            ]);
        }
        if ($keywords['add_time_end']) {
            $query->andWhere([
                '<=',
                'jar.add_time',
                TimeHelper::dayToEndTime($keywords['add_time_end']),
            ]);
        }
        $orderBy      = ' jar.add_time desc';
        $count        = $query->count();
        $pageSize     = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages        = self::setPage($count, $keywords['page'], $pageSize);
        $jobApplyList = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        DebugHelper::writeLog($query->createCommand()
            ->getRawSql(), '非合作站外投递');
        foreach ($jobApplyList as $k => &$list) {
            $list['id']                    = $list['apply_id'] > 0 ? $list['apply_id'] : $list['apply_site_id'];
            $list['uid']                   = UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $list['resume_id']);
            $list['source']                = intval($list['source']);
            $list['sourceTitle']           = BaseJobApplyRecord::SOURCE_LIST[$list['source']];
            $list['resumeAttachmentId']    = $list['outside_attachment_id'] > 0 ? $list['outside_attachment_id'] : $list['outer_attachment_id'];
            $list['resumeAttachmentTitle'] = "";
            $list['delivery_way_txt']      = BaseJobApplyRecord::DELIVERY_WAY_NAME[$list['delivery_way']];
            $list['update_time']           = empty($list['outside_update_time']) ? $list['outer_update_time'] : $list['outside_update_time'];

            if ($list['resumeAttachmentId'] > 0) {
                $resumeAdditionalInfo = BaseResumeAttachment::find()
                    ->where(['id' => intval($list['resumeAttachmentId'])])
                    ->select(['file_name'])
                    ->asArray()
                    ->one();
                if ($resumeAdditionalInfo) {
                    $list['resumeAttachmentTitle'] = "(" . $resumeAdditionalInfo['file_name'] . ") " . $list['resumeAttachmentId'];
                }
            }

            //进度里程
            $schedule         = 1;
            $scheduleTimeList = [
                $list['add_time'],
            ];
            $checkTime        = BaseJobApplyHandleLog::find()
                                    ->select('add_time')
                                    ->where([
                                        'job_apply_id' => $list['apply_id'],
                                        'handle_type'  => BaseJobApplyHandleLog::TYPE_VIEW,
                                    ])
                                    ->orderBy('add_time desc')
                                    ->one()['add_time'] ?: $list['outside_update_time'];

            $throughTime = BaseJobApplyHandleLog::find()
                               ->select('add_time')
                               ->where([
                                   'job_apply_id' => $list['apply_id'],
                                   'handle_type'  => BaseJobApplyHandleLog::TYPE_THROUGH_FIRST,
                               ])
                               ->orderBy('add_time desc')
                               ->one()['add_time'] ?: $list['outside_update_time'];

            $interviewTime = BaseCompanyInterview::find()
                                 ->select('interview_time')
                                 ->where([
                                     'job_apply_id' => $list['apply_id'],
                                     'status'       => BaseActiveRecord::STATUS_ACTIVE,
                                 ])
                                 ->orderBy('add_time desc')
                                 ->one()['add_time'] ?: $list['outside_update_time'];

            switch ($list['status']) {
                case 1:
                    if ($list['is_check'] == 1) {
                        $schedule = 2;
                        array_push($scheduleTimeList, $checkTime);
                    }
                    break;
                case 2:
                    $schedule = 3;
                    array_push($scheduleTimeList, $checkTime);
                    array_push($scheduleTimeList, $throughTime);
                    break;
                default:
                    array_push($scheduleTimeList, $checkTime);
                    array_push($scheduleTimeList, $throughTime);
                    array_push($scheduleTimeList, $interviewTime);
                    $schedule = 4;
            }

            $list['schedule']         = $schedule;
            $list['scheduleTimeList'] = $scheduleTimeList;
            //释放资源
            unset($list['outside_attachment_id'], $list['outer_attachment_id'], $list['apply_id'], $list['apply_site_id']);
        }

        return [
            'list' => $jobApplyList,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];
    }

    /**
     * 面试邀约
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function jobInvitationList($params): array
    {
        $select = [
            'a.add_time',
            'a.update_time',
            'a.id',
            'w.id as interviewId',
            'a.resume_id',
            'a.resume_name',
            'a.resume_member_id',
            'a.company_id',
            'a.company_member_id',
            'c.full_name as companyName',
            'a.job_name as jobName',
            'a.job_id as jobId',
            'a.source',
            'a.is_check',
            'a.resume_attachment_id',
            'a.is_invitation',
            'w.interview_time',
            'w.status',
            'w.address',
            'an.title as announcementName',
            'an.id as announcementId',
        ];

        $query = JobApply::find()
            ->select($select)
            ->alias('a')
            ->innerJoin(['c' => Company::tableName()], 'c.id = a.company_id')
            ->innerJoin(['w' => CompanyInterview::tableName()], 'w.job_apply_id = a.id')
            ->leftJoin(['j' => Job::tableName()], 'j.id = a.job_id')
            ->leftJoin(['an' => Announcement::tableName()], 'an.id = j.announcement_id')
            ->where(['a.is_invitation' => 1]); // 必须是邀请过了

        $keyWordType = $params['keyword_type'];
        //搜索内容
        if ($keyWordType == 1) {
            //职位id
            $query->andFilterWhere([
                'like',
                'a.job_id',
                $params['keyword'],
            ]);
        } elseif ($keyWordType == 2) {
            //职位名称
            $query->andFilterWhere([
                'like',
                'a.job_name',
                $params['keyword'],
            ]);
        } elseif ($keyWordType == 3) {
            //公告id
            $query->andFilterWhere([
                'like',
                'an.id',
                $params['keyword'],
            ]);
        } elseif ($keyWordType == 4) {
            //公告名称
            $query->andFilterWhere([
                'like',
                'an.title',
                $params['keyword'],
            ]);
        } elseif ($keyWordType == 5) {
            //单位id
            $query->andFilterWhere([
                'like',
                'a.company_id',
                $params['keyword'],
            ]);
        } elseif ($keyWordType == 6) {
            //单位名称
            $query->andFilterWhere([
                'like',
                'c.full_name',
                $params['keyword'],
            ]);
        }

        $query->andFilterCompare('a.job_id', $params['job_id']);
        $query->andFilterCompare('a.company_member_id', $params['company_member_id']);
        $query->andFilterCompare('a.resume_member_id', $params['resume_member_id']);
        //$query->andFilterCompare('a.resume_name', $params['resume_name'], 'like');
        if ($params['resume_name']) {
            if (intval($params['resume_name']) > 0 && strlen(intval($params['resume_name'])) == 8) {
                $query->andWhere(['a.resume_id' => UUIDHelper::decryption($params['resume_name'])]);
            } else {
                $query->andWhere([
                    'like',
                    'a.resume_name',
                    $params['resume_name'],
                ]);
            }
        }
        if ($params['interview_status']) {
            if ($params['interview_status'] == 1) {
                // 未开始
                $query->andWhere([
                    '<',
                    'w.interview_time',
                    CUR_DATETIME,
                ]);
            }

            if ($params['interview_status'] == 2) {
                // 已结束
                $query->andWhere([
                    '>',
                    'w.interview_time',
                    CUR_DATETIME,
                ]);
            }
        }

        if ($params['invitation_time_start']) {
            $query->andWhere([
                '>=',
                'w.interview_time',
                TimeHelper::dayToBeginTime($params['invitation_time_start']),
            ]);
        }

        if ($params['invitation_time_end']) {
            $query->andWhere([
                '<=',
                'w.interview_time',
                TimeHelper::dayToEndTime($params['invitation_time_end']),
            ]);
        }

        //面试进度查询
        // 面试状态
        if ($params['interview_status'] == 1) {
            //未进行
            $query->andFilterWhere([
                '>',
                'w.interview_time',
                date('Y-m-d', time()),
            ]);
        } elseif ($params['interview_status'] == 2) {
            //已进行
            $query->andFilterWhere([
                '<',
                'w.interview_time',
                date('Y-m-d', time()),
            ]);
        }

        $count = $query->count();

        $pageSize = $params['limit'] ?: Yii::$app->params['defaultPageSize'];

        $pages = self::setPage($count, $params['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('w.id desc')
            ->asArray()
            ->indexBy('interviewId') //根据面试的id来倒序
            ->all();
        $list = array_values($list);
        foreach ($list as &$item) {
            if (!empty($item['jobId']) && !empty($item['jobName'])) {
                //名称后面拼接uid
                $jobUid          = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $item['jobId']);
                $item['jobName'] = $item['jobName'] . '(' . $jobUid . ')';
            }
            $item['uid'] = UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $item['resume_id']);
            if (!empty($item['announcementId']) && !empty($item['announcementName'])) {
                $announcementUid          = UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT, $item['announcementId']);
                $item['announcementName'] = $item['announcementName'] . '(' . $announcementUid . ')';
            }

            if (!empty($item['companyId']) && !empty($item['companyName'])) {
                $companyUid          = UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY, $item['companyId']);
                $item['companyName'] = $item['companyName'] . '(' . $companyUid . ')';
            }

            if ($item['interview_time'] > date('Y-m-d H:i:s', time())) {
                $item['interviewStatusTxt'] = '未进行';
            } else {
                $item['interviewStatusTxt'] = '已结束';
            }

            $item['id']          = intval($item['id']);
            $item['source']      = intval($item['source']);
            $item['sourceTitle'] = BaseJobApply::SOURCE_LIST[$item['source']];
            $jobApplyHandleList  = BaseJobApplyHandleLog::selectList(['job_apply_id' => $item['id']], [
                'handle_type',
                'add_time',
                'id',
                'content',
            ]);
            if ($jobApplyHandleList) {
                foreach ($jobApplyHandleList as $handleList) {
                    if ($handleList['handle_type'] = BaseJobApplyHandleLog::TYPE_SEND_INVITATION) {
                        $item['invitationTime'] = $handleList['add_time'];
                    }
                }
            }
        }

        $data = [
            'list' => $list,
            'page' => [
                'size'  => (int)$pageSize,
                'total' => (int)$count,
            ],
        ];

        return $data;
    }

    /**
     * 业务下载的简历
     * @param $keywords
     * @return array
     * @throws Exception
     */
    public static function jobResumeDownloadList($keywords): array
    {
        $select  = [
            'add_time',
            'id',
            'job_id',
            'resume_name',
            'resume_id',
            'resume_attachment_id',
        ];
        $where   = ['and'];
        $where[] = ['handler_type' => BaseResumeDownloadLog::HANDLE_TYPE_ADMIN];

        $query = ResumeDownloadLog::find()
            ->select($select)
            ->where($where);

        $query->andFilterCompare('job_id', $keywords['job_id']);
        if ($keywords['resume_name']) {
            if (intval($keywords['resume_name']) > 0 && strlen(intval($keywords['resume_name'])) == 8) {
                $query->andWhere(['resume_id' => UUIDHelper::decryption($keywords['resume_name'])]);
            } else {
                $query->andWhere([
                    'like',
                    'resume_name',
                    $keywords['resume_name'],
                ]);
            }
        }
        if ($keywords['download_time_start']) {
            $query->andWhere([
                '>=',
                'add_time',
                TimeHelper::dayToBeginTime($keywords['download_time_start']),
            ]);
        }
        if ($keywords['download_time_end']) {
            $query->andWhere([
                '<=',
                'add_time',
                TimeHelper::dayToEndTime($keywords['download_time_end']),
            ]);
        }
        $orderBy = ' add_time desc';

        $count    = $query->count();
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        //职位-下载简历列表
        $jobResumeDownloadList = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($jobResumeDownloadList as $k => $list) {
            $jobResumeDownloadList[$k]['id']                    = intval($list['id']);
            $jobResumeDownloadList[$k]['downloadTime']          = $list['add_time'];
            $jobResumeDownloadList[$k]['resumeAttachmentTitle'] = "";
            if ($list['resumeId'] > 0) {
                $jobResumeDownloadList[$k]['resumeTypeTitle']       = "在线简历";
                $jobResumeDownloadList[$k]['resumeAttachmentTitle'] = "简历" . $list['resumeId'];
            } else {
                $jobResumeDownloadList[$k]['resumeTypeTitle'] = "附件简历";
                $resumeAdditionalInfo                         = BaseResumeAdditionalInfo::find()
                    ->where(['id' => intval($list['resumeAttachmentId'])])
                    ->select(['theme_name'])
                    ->asArray()
                    ->one();
                if ($resumeAdditionalInfo) {
                    $jobResumeDownloadList[$k]['resumeAttachmentTitle'] = "(" . $resumeAdditionalInfo['theme_name'] . ") " . intval($list['resumeAttachmentId']);
                }
            }
        }

        return [
            'list' => $jobResumeDownloadList,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];
    }

    /**
     * 职位审核处理历史
     * @param $keywords
     * @return array
     */
    public static function jobHandleLog($keywords): array
    {
        $select  = [
            'add_time',
            'id',
            'handler_name',
            'handle_after',
        ];
        $where   = ['and'];
        $where[] = [
            'job_id'       => $keywords['job_id'],
            'handler_type' => BaseJobHandleLog::HANDLER_TYPE_PLAT,
            'handle_type'  => BaseJobHandleLog::HANDLE_TYPE_AUDIT,
        ];
        $query   = JobHandleLog::find()
            ->where($where)
            ->select($select);
        $orderBy = ' add_time desc';

        $count    = $query->count();
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        //职位-职位审核处理历史
        $jobHandleLog = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        $jobAuditStatusList = BaseJob::JOB_AUDIT_STATUS_NAME;
        foreach ($jobHandleLog as $k => $log) {
            $jobHandleLog[$k]['auditStatus']      = "";
            $jobHandleLog[$k]['auditStatusTitle'] = "";
            $jobHandleLog[$k]['opinion']          = "";
            $handleAfter                          = json_decode($log['handle_after'], true);
            $jobHandleLog[$k]['handle_after']     = $handleAfter;
            if ($handleAfter['audit_status']) {
                $jobHandleLog[$k]['auditStatus']      = intval($handleAfter['audit_status']);
                $jobHandleLog[$k]['auditStatusTitle'] = $jobAuditStatusList[$handleAfter['audit_status']];
            }
            if ($handleAfter['opinion']) {
                $jobHandleLog[$k]['opinion'] = $handleAfter['opinion'];
            }
            if ($handleAfter['审核状态']) {
                $jobHandleLog[$k]['status_title'] = $handleAfter['审核状态'];
            }
        }

        return [
            'list' => $jobHandleLog,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];
    }

    /**
     * 职位审核列表
     * @param $keywords
     * @return array
     * @throws Exception
     */
    public static function getJobAuditList($keywords): array
    {
        $select = [
            'j.id',
            'j.name',
            'j.update_time',
            'j.refresh_time',
            'j.status',
            'j.department',
            'j.city_id',
            'j.period_date',
            'j.code',
            'j.education_type',
            'j.min_wage',
            'j.max_wage',
            'j.is_article',
            'j.audit_status',
            'j.release_time',
            'j.add_time',
            'j.wage_type',
            'j.remark',
            'j.experience_type',
            'j.offline_type',
            'j.experience_type',
            'j.age_type',
            'j.title_type',
            'j.company_id',
            'j.member_id',
            'j.period_date',
            'j.announcement_id',
            'j.member_id',
            'j.click',
            'j.amount',
            'j.download_amount',
            'j.gender_type',
            'j.create_type',
            'j.create_id',
            'j.creator',
            'j.apply_audit_time',
            'j.is_article',
        ];

        $query = self::find()
            ->alias('j')
            ->leftJoin(['c' => Company::tableName()], 'c.id = j.company_id')
            ->select($select)
            ->where([
                'j.is_article'     => Job::IS_ARTICLE_NO,
                'c.is_cooperation' => Company::COOPERATIVE_UNIT_YES,
            ]);
        $query->andFilterCompare('j.audit_status', self::AUDIT_STATUS_WAIT_AUDIT);
        $query->andFilterCompare('j.status', self::STATUS_DELETE, '<>');
        $query->andFilterCompare('j.is_show', self::IS_SHOW_NO, '<>');
        $query->andFilterCompare(' concat ( j.name,j.id )', $keywords['name'], 'like');

        if ($keywords['announcement']) {
            $keywords['announcement_title_num'] = $keywords['announcement'];
        }

        JobApply::uidJudgeWhere($keywords['announcement_title_num'], 'an.id', 'an.title', $query);

        if ($keywords['audit_status'] != 10) {
            $query->andFilterCompare('j.audit_status', $keywords['audit_status']);
        }
        if ($keywords['audit_status'] != 2) {
            $query->andFilterCompare('j.is_article', $keywords['is_article']);
        }
        if ($keywords['release_time_start']) {
            $query->andWhere([
                '>=',
                'j.release_time',
                TimeHelper::dayToBeginTime($keywords['release_time_start']),
            ]);
        }
        if ($keywords['release_time_end']) {
            $query->andWhere([
                '<=',
                'j.release_time',
                TimeHelper::dayToEndTime($keywords['release_time_end']),
            ]);
        }
        if ($keywords['apply_time_start']) {
            $query->andWhere([
                '>=',
                'j.update_time',
                TimeHelper::dayToBeginTime($keywords['apply_time_start']),
            ]);
        }
        if ($keywords['apply_time_end']) {
            $query->andWhere([
                '<=',
                'j.update_time',
                TimeHelper::dayToEndTime($keywords['apply_time_end']),
            ]);
        }
        $orderBy = ' j.add_time desc';
        if ($keywords['sort_release_time']) {
            $sort    = $keywords['sort_release_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.release_time ' . $sort;
        }
        if ($keywords['sort_apply_time']) {
            $sort    = $keywords['sort_apply_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.update_time ' . $sort;
        }

        $count    = $query->count();
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $jobList  = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        $jobStatusList      = self::JOB_STATUS_NAME;
        $jobAuditStatusList = self::JOB_AUDIT_STATUS_NAME;
        $educationTypeList  = BaseDictionary::getEducationList();
        $experienceList     = BaseDictionary::getExperienceList();
        $jobWaitAmount      = 0;
        $jobRefuseAmount    = 0;
        foreach ($jobList as $k => $job) {
            //输出薪资
            $wage = BaseJob::formatWage($job['min_wage'], $job['max_wage'], $job['wage_type']);

            if ($job['member_id']) {
                $companyInfo            = Company::getCompanyInfo($job['member_id']);
                $jobList[$k]['company'] = $companyInfo['full_name'];
            }
            if ($job['isArticle'] == 1) {
                $jobList[$k]['isArticleTitle'] = "公告+职位模式";
            } else {
                $jobList[$k]['isArticleTitle'] = "纯职位模式";
            }
            if ($job['status'] == self::STATUS_ONLINE || $job['status'] == self::STATUS_OFFLINE) {
                $jobList[$k]['isAdopt'] = 1;
            } else {
                $jobList[$k]['isAdopt'] = 0;
            }
            if (strlen($job['creator']) < 1) {
                if ($job['create_id'] < 1) {
                    $job['create_id'] = $job['memberId'];
                }
                if ($job['createType'] == self::CREATE_TYPE_SELF) {
                    $memberInfo             = BaseMember::find()
                        ->where(['id' => $job['create_id']])
                        ->select('username')
                        ->asArray()
                        ->one();
                    $jobList[$k]['creator'] = $memberInfo['username'];
                } else {
                    $resumeInfo             = BaseAdmin::find()
                        ->where(['id' => $job['create_id']])
                        ->select('name')
                        ->asArray()
                        ->one();
                    $jobList[$k]['creator'] = $resumeInfo['name'];
                }
            }
            if ($job['auditStatus'] == self::STATUS_WAIT_AUDIT) {
                $jobWaitAmount++;
            }
            if ($job['auditStatus'] == self::STATUS_REFUSE_AUDIT) {
                $jobRefuseAmount++;
            }
            $city                             = Area::getAreaName($job['city_id']);
            $experienceTypeTitle              = $experienceList[intval($job['experience_type'])];
            $educationTypeTitle               = $educationTypeList[$job['education_type']];
            $jobList[$k]['basicInformation']  = $experienceTypeTitle . '|' . $educationTypeTitle . '|' . $city . "｜" . $wage;
            $jobList[$k]['announcementTitle'] = $job['title'];
            $jobList[$k]['status']            = intval($job['status']);
            $jobList[$k]['statusTitle']       = $jobStatusList[$job['status']];
            $jobList[$k]['auditStatusTitle']  = $jobAuditStatusList[$job['audit_status']];
            $jobList[$k]['creator']           = $job['creator'];
            $jobList[$k]['id']                = intval($job['id']);
            $jobList[$k]['announcementId']    = intval($job['announcementId']);
            $jobList[$k]['isArticle']         = intval($job['isArticle']);
            $jobList[$k]['companyId']         = intval($job['company_id']);
            $jobList[$k]['audit_status']      = intval($job['audit_status']);
            if (strtotime($job['apply_audit_time']) < 1) {
                $jobList[$k]['apply_audit_time'] = '-';
            }
        }

        if ($keywords['export']) {
            $headers = [
                '职位ID',
                '职位名称',
                '基本信息',
                '关联公告',
                '所属单位',
                '审核状态',
                '创建人',
                '发布模式',
                '发布时间',
                '申请审核时间',
            ];
            $data    = [];
            foreach ($jobList as $val) {
                $data[] = [
                    $val['id'] ?: '',
                    $val['name'] ?: '',
                    $val['basicInformation'] ?: '',
                    $val['announcementTitle'] ?: '',
                    $val['company'] ?: '',
                    $val['auditStatusTitle'] ?: '',
                    $val['creator'] ?: '',
                    $val['isArticleTitle'] ?: '',
                    $val['releaseTime'] ?: '',
                    $val['applyTime'] ?: '',
                ];
            }
            $excel    = new Excel();
            $fileName = $excel->export($data, $headers);

            return [
                'excelUrl' => $fileName,
            ];
        } else {
            $amount = [
                'jobAmount'       => (int)$count,
                'jobWaitAmount'   => $jobWaitAmount,
                'jobRefuseAmount' => $jobRefuseAmount,
            ];

            return [
                'list'   => $jobList,
                'amount' => $amount,
                'page'   => [
                    'count' => (int)$count,
                    'limit' => (int)$pageSize,
                    'page'  => (int)$keywords['page'],
                ],
            ];
        }
    }

    /**
     * 职位审核详情
     * @throws Exception
     * @throws \Exception
     */
    public static function jobAuditInfo($keywords): array
    {
        $select = [
            'id',
            'status',
            'audit_status',
            'name',
            'job_category_id',
            'education_type',
            'major_id',
            'nature_type',
            'experience_type',
            'age_type',
            'political_type',
            'abroad_type',
            'amount',
            'department',
            'district_id',
            'province_id',
            'city_id',
            'address',
            'duty',
            'requirement',
            'remark',
            'gender_type',
            'title_type',
            'announcement_id',
            'period_date',
            'experience_type',
            'nature_type',
            'is_negotiable',
            'wage_type',
            'min_wage',
            'max_wage',
            'political_type',
            'abroad_type',
            'welfare_tag',
            'apply_type',
            'apply_address',
            'file_ids',
            'delivery_limit_type',
            'delivery_type',
            'delivery_way',
            'extra_notify_address',
            'company_id',
            'is_establishment',
            'establishment_type',
        ];
        $query  = self::find()
            ->select($select);
        $query->andFilterCompare('id', $keywords['job_id']);
        $orderBy  = ' add_time desc';
        $count    = $query->count();
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        //职位-下载简历列表
        $jobInfo = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->one();

        $jobInfo['apply_type']            = BaseJob::getApplyTypeName($jobInfo['apply_type']) ?: '-';
        $jobInfo['apply_address']         = StringHelper::isEmpty($jobInfo['apply_address']);
        $jobInfo['company_delivery_type'] = BaseCompany::findOneVal(['id' => $jobInfo['company_id']], 'delivery_type');
        //投递限制
        $jobInfo['deliveryLimitTypeTxt'] = '';
        if ($jobInfo['delivery_limit_type']) {
            $deliveryLimitTypeArr = explode(',', $jobInfo['delivery_limit_type']);
            foreach ($deliveryLimitTypeArr as $val) {
                $jobInfo['deliveryLimitTypeTxt'] .= self::DELIVERY_LIMIT_LIST[$val] . ';';
            }

            $jobInfo['deliveryLimitTypeTxt'] = substr($jobInfo['deliveryLimitTypeTxt'], 0, -1);
        }
        //职位附件
        if (!empty($jobInfo['announcementId'])) {
            $fileIds             = Announcement::findOneVal(['id' => $jobInfo['announcementId']], 'file_ids');
            $jobInfo['fileList'] = Announcement::getAppendixList($fileIds);
        } else {
            $jobInfo['fileList'] = Announcement::getAppendixList($jobInfo['file_ids']);
        }
        $data = [];
        //考虑之前未存表的数据信息
        if (!$jobInfo) {
            throw new Exception('当前职位不存在');
        } else {
            if (in_array($jobInfo['status'], BaseJob::JOB_HISTORY_STATUS)) {
                $handleLog = BaseJobHandleLog::find()
                    ->where([
                        'and',
                        ['job_id' => $jobInfo['id']],
                        ['handle_type' => BaseJobHandleLog::HANDLE_TYPE_EDIT],
                    ])
                    ->select([
                        'id',
                        'handle_before',
                        'handle_after',
                    ])
                    ->limit(1)
                    ->orderBy('id desc')
                    ->asArray()
                    ->one();

                if ($jobInfo['announcement_id'] > 0) {
                    $data['announcementTitle'] = BaseAnnouncement::findOneVal(['id' => $jobInfo['announcement_id']],
                        'title');
                } else {
                    $data['announcementTitle'] = '';
                }
                $data['handleBefore'] = json_decode($handleLog['handle_before'], true) ?: '';
                $data['handleAfter']  = json_decode($handleLog['handle_after'], true) ?: '';
                //特殊处理
                if (!empty($data['handleBefore'] && isset($data['handleBefore'][BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS]))) {
                    $data['handleBefore']['fileList'] = [];
                    if (!empty($data['handleBefore'][BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS])) {
                        $file_ids_before = explode(',',
                            $data['handleBefore'][BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS]);
                        $file_data       = BaseFile::getIdsList($file_ids_before);
                        $fileArr         = [];
                        foreach ($file_data as &$value) {
                            if (!empty($value['path'])) {
                                $item           = [];
                                $item['path']   = FileHelper::getFullUrl($value['path']);
                                $item['name']   = $value['name'];
                                $item['suffix'] = FileHelper::getFileSuffixClassName($value['suffix']);
                                $item['id']     = $value['id'];
                                array_push($fileArr, $item);
                            }
                        }
                        $data['handleBefore']['fileList'] = $fileArr;
                    }
                    //                    $data['handleBefore'][BaseJobHandleLog::LOG_TEXT_LIST[BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS]] = $data['handleBefore'][BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS];
                    unset($data['handleBefore'][BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS]);
                }

                if (!empty($data['handleAfter']) && isset($data['handleAfter'][BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS])) {
                    $data['handleAfter']['fileList'] = [];
                    if (!empty($data['handleAfter'][BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS])) {
                        $file_ids_after = explode(',', $data['handleAfter'][BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS]);
                        $file_data      = BaseFile::getIdsList($file_ids_after);
                        $fileArr        = [];
                        foreach ($file_data as &$value) {
                            if (!empty($value['path'])) {
                                $item           = [];
                                $item['path']   = FileHelper::getFullUrl($value['path']);
                                $item['name']   = $value['name'];
                                $item['suffix'] = FileHelper::getFileSuffixClassName($value['suffix']);
                                $item['id']     = $value['id'];
                                array_push($fileArr, $item);
                            }
                        }
                        $data['handleAfter']['fileList'] = $fileArr;
                    }
                    //                    $data['handleAfter'][BaseJobHandleLog::LOG_TEXT_LIST[BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS]] = $data['handleAfter'][BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS];
                    unset($data['handleAfter'][BaseJobHandleLog::LOG_TEXT_LIST_FILE_IDS]);
                }
                $historyStatus      = 1;
                $historyStatusTitle = "有审核通过历史";
            } else {
                $data = Self::transformation($jobInfo);
                //投递类型
                $data['deliveryTypeTxt'] = empty($data['delivery_type']) ? '-' : BaseJob::DELIVERY_TYPE_NAME[$data['delivery_type']];
                //投递方式
                $data['deliveryWayTxt'] = '-';
                if ($data['delivery_way']) {
                    if ($data['delivery_way'] == BaseJob::DELIVERY_WAY_PLATFORM) {
                        $data['deliveryWayTxt'] = BaseJob::DELIVERY_WAY_SELECT_NAME[BaseJob::DELIVERY_WAY_PLATFORM];
                    } else {
                        $data['deliveryWayTxt'] = BaseJob::DELIVERY_WAY_SELECT_NAME[BaseJob::DELIVERY_WAY_EMAIL_LINK];
                    }
                }

                // 编制相关
                // 编制相关文案拼接
                if ($data['is_establishment'] == self::IS_ESTABLISHMENT_YES) {
                    $data['establishmentTxt'] = self::getEstablishmentName($data['establishment_type']);
                }
                //职位联系人
                $data['job_contact'] = self::getJobContact($jobInfo['id']);
                //职位协同账号
                $data['job_contact_synergy'] = self::getJobContactSynergy($jobInfo['id']);
                //此处关联是否公告模式
                $historyStatus      = 2;
                $historyStatusTitle = "无审核通过历史";
            }
        }

        return [
            'data'               => $data,
            'historyStatus'      => $historyStatus,
            'historyStatusTitle' => $historyStatusTitle,
        ];
    }

    /**
     * 获取已通过审核职位地址列表
     */
    public static function getCompanyAddressList($request): array
    {
        //已通过职位的相关信息列表
        $select  = [
            'province_id',
            'city_id',
            'district_id',
            'address',
        ];
        $query   = BaseJob::find()
            ->select($select);
        $company = BaseCompany::getCompanyInfo($request['member_id']);
        $query->andFilterCompare('status', self::STATUS_ONLINE);
        $query->andFilterCompare('company_id', $company['id']);
        $jobAddressInfos = $query->asArray()
            ->all();

        //转化省市区地址文字信息
        $result    = [];
        $areaCache = BaseArea::setAreaCache();
        foreach ($jobAddressInfos as $k => $jobAddressInfo) {
            $result[$k]['provinceId'] = $jobAddressInfo['province_id'];
            $result[$k]['cityId']     = $jobAddressInfo['city_id'];
            $result[$k]['districtId'] = $jobAddressInfo['district_id'];
            $result[$k]['provinceId'] = $jobAddressInfo['province_id'];
            $result[$k]['areaName']   = $areaCache[$jobAddressInfo['province_id']]['name'] . $areaCache[$jobAddressInfo['city_id']]['name'] . $areaCache[$jobAddressInfo['district_id']]['name'];
            $result[$k]['address']    = $jobAddressInfo['address'];
        }

        //格式数据返回
        return array_values(array_unique($result, SORT_REGULAR));
    }

    /**
     * 操作入表
     * @param  $date
     * @throws Exception
     * @throws \yii\base\NotSupportedException
     */
    public static function createJobHandleLog($data)
    {
        try {
            $adminId = Yii::$app->user->id ?: '0';
        } catch (Exception $e) {
            $adminId = '0';
        }
        $adminInfo = BaseAdmin::find()
            ->where(['id' => $adminId])
            ->select('username')
            ->asArray()
            ->one();

        $jobHandleLog = [
            'add_time'      => date("Y-m-d H:i:s", time()),
            'job_id'        => $data['job_id'],
            'handle_type'   => $data['handle_type'],
            'handler_type'  => 1,
            'handler_id'    => $adminId ?: 0,
            'handler_name'  => $adminInfo['username'] ?: '系统',
            'handle_before' => json_encode($data['handle_before']),
            'handle_after'  => json_encode($data['handle_after']),
            'ip'            => IpHelper::getIpInt(),
        ];
        BaseJobHandleLog::createInfo($jobHandleLog);
    }

    /**
     * 显示/隐藏职位
     * @throws Exception
     */
    public static function changeJobShow($data)
    {
        try {
            $model = self::findOne($data['id']);
            if (!$model) {
                throw new  Exception('职位不存在');
            }

            $model->is_show = $data['is_show'];
            //0425 产品确定都是下线
            $model->status = BaseJob::STATUS_OFFLINE;

            if (!$model->save()) {
                throw new Exception($model->getFirstErrorsMessage());
            }

            // 刷公告
            if ($model->announcement_id) {
                Producer::afterAnnouncementUpdateJob($model->announcement_id);
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 内容管理下职位列表
     * @throws Exception
     */
    public static function getAgentJobList($keywords): array
    {
        $select = [
            'j.id',
            'j.add_time',
            'j.status',
            'j.member_id',
            'j.company_id',
            'j.period_date',
            'j.name',
            'j.code',
            'j.job_category_id',
            'j.education_type',
            'j.major_id',
            'j.is_negotiable',
            'j.wage_type',
            'j.min_wage',
            'j.max_wage',
            'j.experience_type',
            'j.age_type',
            'j.title_type',
            'j.political_type',
            'j.abroad_type',
            'j.amount',
            'j.department',
            'j.city_id',
            'j.refresh_time',
            'j.audit_status',
            'j.click',
            'j.creator',
            'j.create_type',
            'j.department',
            'j.is_show',
            'j.is_miniapp',
            'c.full_name',
            'COUNT(jar.job_id) as jobApplyNum',
        ];

        $query = BaseJob::find()
            ->alias('j')
            ->leftJoin(['c' => BaseCompany::tableName()], 'c.id = j.company_id')
            ->leftJoin(['jar' => BaseJobApplyRecord::tableName()], 'jar.job_id = j.id')
            ->groupBy('j.id')
            ->select($select)
            ->where([
                'j.is_show'    => BaseJob::IS_SHOW_YES,
                'j.is_article' => BaseJob::IS_ARTICLE_NO,
            ]);

        $query->andFilterCompare('j.status', BaseJob::STATUS_DELETE, '<>');
        $query->andFilterCompare('j.create_type', BaseJob::CREATE_TYPE_AGENT);
        $query->andFilterCompare('c.is_cooperation', BaseCompany::COOPERATIVE_UNIT_NO);
        $query->andFilterCompare('j.is_miniapp', $keywords['is_miniapp']);

        //检测职位名称/编号并搜索
        if ($keywords['name']) {
            if (is_numeric($keywords['name']) && strlen($keywords['name']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['name']);
                $query->andFilterCompare('j.id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('concat(j.name,j.id )', $keywords['name'], 'like');
            }
        }
        //检测单位名称/编号并搜索
        if ($keywords['company_name']) {
            if (is_numeric($keywords['company_name']) && strlen($keywords['company_name']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['company_name']);
                $query->andFilterCompare('j.company_id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('c.full_name', $keywords['company_name'], 'like');
            }
        }

        $query->andFilterCompare('j.creator', $keywords['creator'], 'like');
        $query->andFilterCompare('j.education_type', $keywords['education_type'], 'in');
        $query->andFilterCompare('j.job_category_id', $keywords['job_category_id'], 'in');
        $query->andFilterCompare('j.nature_type', $keywords['nature_type']);
        $query->andFilterCompare('j.department', $keywords['department'], 'like');
        $query->andFilterCompare('j.abroad_type', $keywords['abroad_type']);
        $query->andFilterCompare('j.experience_type', $keywords['experience_type']);
        $query->andFilterCompare('j.title_type', $keywords['title_type'], 'in');
        $query->andFilterCompare('j.audit_status', $keywords['audit_status']);
        $query->andFilterCompare('j.status', $keywords['status']);
        $query->andFilterCompare('j.gender_type', $keywords['gender_type']);
        $query->andFilterCompare('j.political_type', $keywords['political_type']);
        $query->andFilterCompare('concat(j.city_id,j.address)', $keywords['city'], 'like');
        $query->andFilterCompare('j.city_id', $keywords['city'], 'in');
        $query->andFilterCompare('j.is_show', $keywords['is_show']);

        if ($keywords['major_id']) {
            $majorIds  = explode(',', $keywords['major_id']);
            $condition = ['or'];
            foreach ($majorIds as $item) {
                $condition[] = "find_in_set(" . $item . ",j.major_id)";
            }
            $query->andWhere($condition);
        }

        if ($keywords['release_time_start']) {
            $query->andWhere([
                '>=',
                'j.release_time',
                TimeHelper::dayToBeginTime($keywords['release_time_start']),
            ]);
        }
        if ($keywords['release_time_end']) {
            $query->andWhere([
                '<=',
                'j.release_time',
                TimeHelper::dayToEndTime($keywords['release_time_end']),
            ]);
        }
        if ($keywords['refresh_time_start']) {
            $query->andWhere([
                '>=',
                'j.refresh_time',
                TimeHelper::dayToBeginTime($keywords['refresh_time_start']),
            ]);
        }
        if ($keywords['refresh_time_end']) {
            $query->andWhere([
                '<=',
                'j.refresh_time',
                TimeHelper::dayToEndTime($keywords['refresh_time_end']),
            ]);
        }

        $orderBy = ' j.add_time desc';
        if ($keywords['sort_click']) {
            $sort    = $keywords['sort_click'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.click ' . $sort;
        }
        if ($keywords['sort_job_apply_amount']) {
            $sort    = $keywords['sort_job_apply_amount'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' jobApplyNum ' . $sort;
        }
        if ($keywords['sort_refresh_time']) {
            $sort    = $keywords['sort_refresh_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.refresh_time ' . $sort;
        }
        if ($keywords['sort_amount']) {
            $sort    = $keywords['sort_amount'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.amount ' . $sort;
        }

        if ($keywords['establishment_type']) {
            // 这里会有几种可能性,-1和其他值(-1的情况下是没有选编制的类型,其他值是选了编制类型里面某个并且find_in_set
            $establishmentType = explode(',', $keywords['establishment_type']);
            $orWhere           = [
                'or',
            ];
            foreach ($establishmentType as $itemKey => $establishmentItem) {
                if ($establishmentItem == -1) {
                    $orWhere[] = ['j.is_establishment' => BaseJob::IS_ESTABLISHMENT_NO];
                } else {
                    $establishmentItemKey = 'establishmentItem' . $itemKey;
                    $orWhere[]            = new Expression("FIND_IN_SET(:" . $establishmentItemKey . ", j.establishment_type)",
                        [$establishmentItemKey => $establishmentItem]);
                }
            }
            $query->andWhere($orWhere);
        }

        $count    = $query->count();
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $jobList  = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->groupBy('j.id')
            ->asArray()
            ->all();

        //todo 处理返回值
        $educationTypeList  = BaseDictionary::getEducationList();
        $experienceList     = BaseDictionary::getExperienceList();
        $areaCache          = BaseArea::setAreaCache();
        $jobStatusList      = self::JOB_STATUS_NAME;
        $jobAuditStatusList = self::JOB_AUDIT_STATUS_NAME;

        $changeTimeList = [
            'add_time',
            'period_date',
            'refresh_time',
        ];

        foreach ($jobList as $k => $list) {
            $jobList[$k]['uid']              = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $list['id']);
            $jobList[$k]['company_uid']      = UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY, $list['company_id']);
            $jobList[$k]['status_title']     = $jobStatusList[$list['status']];
            $jobList[$k]['auditStatusTitle'] = $jobAuditStatusList[$list['audit_status']];

            // 处理为空的时间格式
            foreach ($changeTimeList as $item) {
                if (strtotime($list[$item]) < 1) {
                    $jobList[$k][$item] = '-';
                }
            }

            // 信息展示，用人部门｜经验｜学历要求｜城市｜薪资
            $experienceTypeTitle = $experienceList[intval($list['experienceType'])];
            $educationTypeTitle  = $educationTypeList[$list['educationType']];
            if ($list['educationType'] == 0 || strlen($list['educationType']) < 1) {
                $educationTypeTitle = '教育不限';
            }
            if ($list['experienceType'] == 0 || strlen($list['experienceType']) < 1) {
                $experienceTypeTitle = '经验不限';
            }

            $wage = BaseJob::formatWage($list['min_wage'], $list['max_wage'], $list['wage_type']);
            $wage = explode('/', $wage)[0];

            $city = $areaCache[$list['city_id']]['name'];
            if (strlen($city) < 1) {
                $city = "-";
            }
            $jobList[$k]['message'] = $experienceTypeTitle . '|' . $educationTypeTitle . '|' . $city . '|' . $wage;

            //这里统计投机投递次数 站内、站外
            // $offSiteJobApplyAccount = BaseOffSiteJobApply::find()
            //     ->select(['id'])
            //     ->where(['job_id' => $list['id']])
            //     ->count();

            // $jobList[$k]['jobApplyNum'] = $item['jobApplyNum'] + $offSiteJobApplyAccount;
        }

        return [
            'list' => $jobList,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];
    }

    /**
     * 单位检索
     * @throws Exception
     */
    public static function getCompanyList($keywords): array
    {
        $select = [
            'id',
            'full_name',
            'type',
            'nature',
            'is_cooperation',
            'member_id',
            'delivery_type',
        ];

        $query = BaseCompany::find()
            ->select($select);

        $query->andFilterCompare('is_cooperation', $keywords['is_cooperation']);
        if ($keywords['full_name']) {
            if (is_numeric($keywords['full_name']) && strlen($keywords['full_name']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['full_name']);
                $query->andFilterCompare('id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('full_name', $keywords['full_name'], 'like');
            }
        }

        $natureList      = BaseCompany::getNatureList();
        $companyTypeList = BaseDictionary::getCompanyTypeList();

        $list = $query->limit(20)
            ->asArray()
            ->all();

        foreach ($list as $key => &$item) {
            $item['type_title']        = $companyTypeList[$item['type']];
            $item['nature_title']      = $natureList[$item['nature']];
            $item['delivery_type_txt'] = BaseCompany::DELIVERY_TYPE_NAME[$item['delivery_type']];
            //获取单位的配置
            $config_info             = BaseCompanyMemberConfig::getInfo($item['id']);
            $item['subaccount_used'] = $config_info ? $config_info['used'] : 0;
        }

        return $list;
    }

    /**
     * 发布/保存新职位
     * @throws Exception
     */
    public static function create($keywords)
    {
        $service = new AddService();
        $adminId = Yii::$app->user->id;
        try {
            // todo 这里区分发布、保存、编辑
            if ($keywords['auditStatus'] == BaseJob::AUDIT_STATUS_WAIT_AUDIT) {
                if (strlen($keywords['jobId']) < 1) {
                    // 发布--反之为编辑
                    $keywords['status'] = BaseJob::STATUS_WAIT_AUDIT;
                }
                $service->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setAudit()
                    ->setDate($keywords)
                    ->run();
            } else {
                // 保存
                $service->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setStaging()
                    ->setDate($keywords)
                    ->run();
            }
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 职位批量导入数据
     * @throws Exception
     */
    public static function jobBatchImport($keywords): array
    {
        $filePath = Yii::getAlias('@frontendPc') . '/web/' . $keywords['filePath'];

        // 读取数据
        $model = new JobBatchImport();
        $model->identify($filePath, JobBatchImport::PLATFORM_ADMIN);
        $data = $model->clearData;
        // 处理数据
        // 插入数据
        foreach ($data as $item) {
            $jobData = array_column($item, 'value', 'key');
            // 合并识别学科与自选学科
            if (!empty($jobData['majorId']) && !empty($jobData['majorAi'])) {
                $jobData['majorId'] = array_unique(array_merge($jobData['majorId'], $jobData['majorAi']));
            } elseif (empty($jobData['majorId']) && !empty($jobData['majorAi'])) {
                $jobData['majorId'] = $jobData['majorAi'];
            }
            $majorId    = empty($jobData['majorId']) ? [] : implode(',', $jobData['majorId']);
            $welfareTag = implode(',', $jobData['welfareTag']);

            //这里默认修改成待审核
            $jobData['status']         = BaseJob::STATUS_WAIT_AUDIT;
            $jobData['auditStatus']    = BaseJob::AUDIT_STATUS_WAIT_AUDIT;
            $jobData['applyAuditTime'] = CUR_DATETIME;
            $jobData['periodDate']     = $keywords['periodDate'];
            $jobData['companyId']      = $keywords['companyId'];
            $jobData['fileIds']        = $keywords['fileIds'];
            $jobData['majorId']        = $majorId;
            $jobData['welfareTag']     = $welfareTag;
            if (empty($jobData['applyType']) && empty($jobData['applyAddress']) && empty($jobData['extraNotifyAddress'])) {
                $jobData['applyType']          = $keywords['applyType'];
                $jobData['applyAddress']       = $keywords['applyAddress'];
                $jobData['extraNotifyAddress'] = $keywords['extraNotifyAddress'];
            }
            // 薪资范围
            if ($jobData['minWageMonth'] > 0 || $jobData['maxWageMonth'] > 0) {
                // 月薪
                $jobData['wageType']     = BaseJob::TYPE_WAGE_MONTH;
                $jobData['isNegotiable'] = BaseJob::IS_NEGOTIABLE_YES;
                $jobData['minWage']      = $jobData['minWageMonth'];
                $jobData['maxWage']      = $jobData['maxWageMonth'] ?: $jobData['minWageMonth'];
            } elseif ($jobData['minWageYear'] > 0 || $jobData['maxWageYear'] > 0) {
                // 年薪
                $jobData['wageType']     = BaseJob::TYPE_WAGE_YEARS;
                $jobData['isNegotiable'] = BaseJob::IS_NEGOTIABLE_YES;
                $jobData['minWage']      = $jobData['minWageYear'];
                $jobData['maxWage']      = $jobData['maxWageYear'] ?: $data['minWageYear'];
            } else {
                // 面议
                $jobData['wageType'] = BaseJob::TYPE_WAGE_NEGOTIABLE;
                $jobData['minWage']  = $jobData['minWageMonth'];
                $jobData['maxWage']  = $jobData['maxWageMonth'];
            }
            //处理协同子账号
            ///检验邮箱子账号是否存在
            $contactSynergyEmailArr          = explode(',', $jobData['contactSynergyEmail']);
            $jobData['jobContactSynergyIds'] = [];
            foreach ($contactSynergyEmailArr as $value) {
                $record_id = BaseCompanyMemberInfo::validateEmailMember($keywords['companyId'], $value);
                if ($record_id) {
                    array_push($jobData['jobContactSynergyIds'], $record_id);
                }
            }
            //处理职位联系人
            $jobData['jobContactId']      = count($jobData['jobContactSynergyIds']) > 0 ? $jobData['jobContactSynergyIds'][0] : BaseCompanyMemberInfo::findOneVal([
                'company_id'          => $keywords['companyId'],
                'company_member_type' => BaseCompanyMemberInfo::COMPANY_MEMBER_TYPE_MAIN,
            ], 'id');
            $jobData['deliveryLimitType'] = '';
            $deliveryLimitTypeArr         = [];
            if (isset($jobData['deliveryLimitTypeEducation']) && !empty($jobData['deliveryLimitTypeEducation'])) {
                array_push($deliveryLimitTypeArr, $jobData['deliveryLimitTypeEducation']);
            }
            if (isset($jobData['deliveryLimitTypeFile']) && !empty($jobData['deliveryLimitTypeFile'])) {
                array_push($deliveryLimitTypeArr, $jobData['deliveryLimitTypeFile']);
            }
            if (count($deliveryLimitTypeArr) > 0) {
                $jobData['deliveryLimitType'] = implode(',', $deliveryLimitTypeArr);
            }
            //删除没用字段
            unset($jobData['minWageMonth'], $jobData['maxWageMonth'], $jobData['minWageYear'], $jobData['maxWageYear'], $jobData['majorAi'], $jobData['deliveryLimitTypeEducation'], $jobData['deliveryLimitTypeFile'], $jobData['contactSynergyEmail']);
            $service = new JobService();
            $service->setPlatform(CommonService::PLATFORM_ADMIN)
                ->setOparetion(JobService::JOB_ADD)
                ->setData($jobData)
                ->run();
        }

        //删除文件
        unlink($filePath);

        return ['msgTxt' => '成功批量导入数据'];
    }

    /**
     * 删除职位
     * @throws Exception
     */
    public static function deleteAgentJob($data)
    {
        try {
            $idArr   = explode(',', $data['id']);
            $adminId = Yii::$app->user->id;
            $service = new DeleteJobService();
            foreach ($idArr as $id) {
                $list       = [];
                $list['id'] = $id;

                $service->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setData($list)
                    ->run();
            }
            $service->afterRun($idArr);
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * 模糊查找职位列表
     * @throws Exception
     */
    public static function searchJobList($keywords): array
    {
        $query = BaseJob::find()
            ->select([
                'id',
                'name',
            ]);

        if ($keywords['name']) {
            if (is_numeric($keywords['name'])) {
                if (strlen($keywords['name']) == 8) {
                    $nameChangeUid = UUIDHelper::decryption($keywords['name']);
                    $query->andFilterCompare('id', (int)$nameChangeUid);
                } else {
                    $query->andFilterCompare('id', $keywords['name']);
                }
            } else {
                $query->andFilterCompare('name', $keywords['name'], 'like');
            }
        }

        $query->andFilterCompare('status', BaseJob::STATUS_DELETE, '<>');
        $query->andFilterCompare('is_show', BaseJob::IS_SHOW_NO, '<>');
        $query->andFilterCompare('company_id', $keywords['companyId']);

        $list = $query->limit(20)
            ->asArray()
            ->all();

        $temp = [];
        foreach ($list as $item) {
            $temp[] = [
                'k' => $item['id'],
                'v' => $item['name'],
            ];
        }

        return $temp;
    }

    public static function searchJobInfoList($keywords)
    {
        $query = BaseJob::find()
            ->where([
                'status'  => BaseJob::STATUS_ACTIVE,
                'is_show' => BaseJob::IS_SHOW_YES,
            ])
            ->select([
                'id as jobId',
                'name as jobName',
                'job_category_id as jobCategoryId',
                'province_id as provinceId',
                'city_id as cityId',
            ]);

        if ($keywords['name']) {
            $query->andFilterWhere([
                'like',
                'name',
                $keywords['name'],
            ])
                ->orFilterWhere(['id' => $keywords['name']]);
        }

        $list = $query->limit(20)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['jobCategoryText'] = BaseCategoryJob::getFullLevelName($item['jobCategoryId']);
            if (in_array($item['provinceId'], BaseArea::HIGH_CROWN_ID_LIST)) {
                //如果是直辖的，设置为空
                $item['provinceText'] = '';
                $item['provinceId']   = '';
            } else {
                $item['provinceText'] = BaseArea::getAreaName($item['provinceId']);
            }
            $item['cityText'] = BaseArea::getAreaName($item['cityId']);
        }

        return $list;
    }

    /**
     * 获取回收站职位列表
     * @param $params
     * @return array
     * @throws Exception
     */
    public static function getRecycleJobList($params)
    {
        $select = [
            'j.id',
            'j.name',
            'j.status',
            'j.update_time',
            'j.education_type',
            'j.min_wage',
            'j.max_wage',
            'j.wage_type',
            'j.experience_type',
            'j.age_type',
            'j.company_id',
            'j.member_id',
            'j.announcement_id',
            'j.click',
            'j.gender_type',
            'j.creator',
            'j.delete_time',
            'j.city_id',
            'ar.title',
            'ar.home_column_id',
            'ar.home_sub_column_ids',
            'c.full_name',
        ];

        $query = self::find()
            ->alias('j')
            ->leftJoin(['a' => Announcement::tableName()], 'a.id = j.announcement_id')
            ->leftJoin(['ar' => Article::tableName()], 'ar.id = a.article_id')
            ->leftJoin(['c' => Company::tableName()], 'c.id = j.company_id')
            ->select($select)
            ->where([
                'j.status'         => self::STATUS_DELETE,
                'c.is_cooperation' => Announcement::IS_COOPERATION_NO,
            ])
            ->groupBy(['j.id']);

        JobApply::uidJudgeWhere($params['titleNum'], 'j.id', 'j.name', $query);

        // 所属栏目
        $query->andFilterCompare('ar.home_column_id', $params['homeColumnId']);
        // 所属单位
        if ($params['companyName']) {
            $companyId = Company::findOneVal([
                '=',
                'full_name',
                $params['companyName'],
            ], 'id');
            $query->andFilterCompare('a.company_id', $companyId);
        }

        // 发布人名称
        $query->andFilterCompare('j.creator', $params['creator']);

        // 删除时间
        if ($params['deleteTimeStart']) {
            $query->andWhere([
                '>=',
                'j.delete_time',
                TimeHelper::dayToBeginTime($params['deleteTimeStart']),
            ]);
        }
        if ($params['deleteTimeEnd']) {
            $query->andWhere([
                '<=',
                'j.delete_time',
                TimeHelper::dayToEndTime($params['deleteTimeEnd']),
            ]);
        }

        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $params['page'], $pageSize);

        $orderBy = 'j.delete_time desc';
        if ($params['sortDeleteTime']) {
            $sort    = $params['sortDeleteTime'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = 'j.delete_time' . $sort;
        }
        if ($params['sortUpdateTime']) {
            $sort    = $params['sortUpdateTime'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = 'j.update_time' . $sort;
        }

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        $educationTypeList = BaseDictionary::getEducationList();
        $experienceList    = BaseDictionary::getExperienceList();
        foreach ($list as &$item) {
            $experienceTypeTitle = $experienceList[intval($item['experience_type'])];
            $educationTypeTitle  = $educationTypeList[$item['education_type']];
            if ($item['education_type'] < 1 || strlen($item['education_type']) < 1) {
                $educationTypeTitle = '学历不限';
            }
            if ($item['experience_type'] < 1 || strlen($item['experience_type']) < 1) {
                $experienceTypeTitle = '经验不限';
            }
            $city = Area::getAreaName($item['city_id']);
            //输出薪资
            $wage = BaseJob::formatWage($item['min_wage'], $item['max_wage'], $item['wage_type']);

            $item['basicInformation'] = $experienceTypeTitle . '|' . $educationTypeTitle . '|' . $city . "｜" . $wage;
            //关联公告
            $item['title'] = $item['title'] . '(' . UUIDHelper::encrypt(UUIDHelper::TYPE_ANNOUNCEMENT,
                    $item['announcement_id']) . ')';

            $item['company'] = $item['full_name'] . '(' . UUIDHelper::encrypt(UUIDHelper::TYPE_COMPANY,
                    $item['company_id']) . ')';

            $item['jobUid'] = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $item['id']);

            $columnName = HomeColumn::findOneVal(['id' => $item['home_column_id']], 'name');
            if (!empty($item['home_sub_column_ids'])) {
                $columnIdsArr = explode(',', $item['home_sub_column_ids']);
                $columnArr    = HomeColumn::getSubColumnName($columnIdsArr);
                array_push($columnArr, $columnName);
                $item['columnName'] = implode(';', array_unique($columnArr));
            } else {
                $item['columnName'] = $columnName;
            }

            unset($item['education_type'], $item['min_wage'], $item['max_wage'], $item['wage_type'], $item['experience_type']);
            unset($item['age_type'], $item['full_name'], $item['member_id'], $item['gender_type'], $item['city_id']);
        }

        return [
            'list'  => $list,
            'pages' => [
                'size'  => (int)$pageSize,
                'total' => (int)$count,
            ],
        ];
    }

    /**
     * 获取公告详情的职位列表
     * @param $announcementId
     * @param $type
     * @return array|\yii\db\ActiveRecord[]
     * @throws \Exception
     */
    public static function getAnnouncementDetailJobList($announcementId, $type = 0)
    {
        $select = [
            'id',
            'name',
            'code',
            'major_id',
            'education_type',
            'amount',
            'department',
            'is_negotiable',
            'min_wage',
            'max_wage',
            'wage_type',
            'province_id',
            'city_id',
            'audit_status',
            'period_date',
            'status',
            'delivery_type',
            'delivery_way',
            'apply_address',
        ];

        if ($type == BaseAnnouncement::STATUS_STAGING) {
            $where = [
                'status' => self::STATUS_WAIT,
            ];
        } elseif ($type == BaseAnnouncement::STATUS_OFFLINE) {
            $where = [
                'status' => self::STATUS_OFFLINE,
            ];
        } else {
            $where = [
                'status' => [
                    self::STATUS_ONLINE,
                    self::STATUS_OFFLINE,
                ],
            ];
        }

        $jobList          = self::find()
            ->where(['announcement_id' => $announcementId])
            ->andWhere($where)
            ->select($select)
            // ->orderBy('add_time desc')
            ->orderBy('id asc')
            ->asArray()
            ->all();
        $announcementInfo = BaseAnnouncement::findOne($announcementId);
        $jobMajor         = [];
        $educationArr     = [];
        $provinceArr      = [];
        $cityArr          = [];
        foreach ($jobList as &$item) {
            if ($item['delivery_type'] != 0) {
                $item['applyInfo'] = BaseJob::DELIVERY_WAY_NAME[$item['delivery_way']] . '/' . $item['apply_address'];
            } else {
                $item['applyInfo'] = BaseAnnouncement::DELIVERY_WAY_NAME[$announcementInfo->delivery_way] . '/' . $announcementInfo->apply_address;
            }
            $majorArr            = explode(',', $item['major_id']);
            $item['majorTxt']    = Major::getAllMajorName($majorArr) ?: '-';
            $jobMajor[]          = $item['majorTxt'];
            $educationArr[]      = $item['education_type'];
            $item['provinceTxt'] = Area::getAreaName($item['province_id']) ?: '-';
            $item['cityTxt']     = Area::getAreaName($item['city_id']) ?: '-';
            if (!$item['min_wage'] && !$item['max_wage']) {
                $item['wage'] = '面议';
            } else {
                $item['wage'] = BaseJob::formatWage($item['min_wage'], $item['max_wage'], $item['wage_type']) ?: '-';
            }

            $item['educationTxt'] = Dictionary::getEducationName($item['education_type']) ?: '-';
            //薪资wage_id回显
            if ($item['is_negotiable'] <> 1) {
                $item['wage_id'] = (string)BaseJob::getWageId($item['min_wage'], $item['max_wage']);
            }
            if ($item['provinceTxt']) {
                $provinceArr[] = $item['provinceTxt'];
            }
            if ($item['cityTxt']) {
                $cityArr[] = $item['cityTxt'];
            }
            if ($item['period_date'] == TimeHelper::ZERO_TIME) {
                $item['period_date'] = '详见正文';
            } else {
                $item['period_date'] = substr($item['period_date'], 0, 10);
            }
            if ($item['amount'] == '若干') {
                $item['amount'] = '若干';
            }

            // 空转'-'
            $item['code']       = StringHelper::isEmpty($item['code']);
            $item['department'] = StringHelper::isEmpty($item['department']);
            $information        = [];
            if ($item['cityTxt']) {
                array_push($information, $item['cityTxt']);
            }
            if ($item['amount']) {
                array_push($information, "招{$item['amount']}人");
            }
            if ($item['educationTxt']) {
                array_push($information, $item['educationTxt']);
            }
            if ($item['wage']) {
                array_push($information, $item['wage']);
            }
            if ($item['major_id']) {
                array_push($information, $item['majorTxt']);
            }
            $item['information']             = implode(' | ', $information);
            $item['job_contact']             = Job::getJobContact($item['id']);
            $item['job_contact_synergy']     = Job::getJobContactSynergy($item['id']);
            $item['job_contact_synergy_num'] = count($item['job_contact_synergy']);

            unset($item['major_id'], $item['province_id'], $item['city_id'], $item['audit_status'], $item['education_type'], $item['is_negotiable']);
        }

        return [
            'list'          => $jobList,
            'job_major'     => $jobMajor,
            'education_arr' => $educationArr,
            'province_arr'  => $provinceArr,
            'city_arr'      => $cityArr,
        ];
    }

    /**
     * 获取公告职位列表（非合作单位）
     * @param $keywords
     * @return array
     * @throws Exception
     */
    public static function getAnnouncementJobListAdmin($keywords): array
    {
        $adminId = Yii::$app->user->id;
        //职位列表
        $jobList = self::announcementValidateSearchKeyWords($keywords);

        $jobStatusList      = self::JOB_STATUS_NAME;
        $jobAuditStatusList = self::JOB_AUDIT_STATUS_NAME;
        $educationTypeList  = BaseDictionary::getEducationList();
        $experienceList     = BaseDictionary::getExperienceList();
        $isShowName         = BaseJob::IS_SHOW_NAME;
        $apply              = 0;
        $click              = 0;
        $all                = 0;
        foreach ($jobList['list'] as $k => $job) {
            $experienceTypeTitle = $experienceList[intval($job['experience_type'])];
            $educationTypeTitle  = $educationTypeList[$job['education_type']];
            if ($job['education_type'] < 1 || strlen($job['education_type']) < 1) {
                $educationTypeTitle = '学历不限';
            }
            if ($job['experience_type'] < 1 || strlen($job['experience_type']) < 1) {
                $experienceTypeTitle = '经验不限';
            }
            $city = Area::getAreaName($job['city_id']);
            //输出薪资
            $wage = BaseJob::formatWage($job['min_wage'], $job['max_wage'], $job['wage_type']);

            $jobList['list'][$k]['basicInformation'] = $experienceTypeTitle . '|' . $educationTypeTitle . '|' . $city . "｜" . $wage;
            //关联公告
            $jobList['list'][$k]['announcementTitle'] = "";
            if ($job['member_id']) {
                $companyInfo                    = Company::getCompanyInfo($job['member_id']);
                $jobList['list'][$k]['company'] = $companyInfo['full_name'];
            }
            $articleId                                      = BaseAnnouncement::findOneVal(['id' => $job['announcement_id']],
                'article_id');
            $jobList['list'][$k]['announcementAuditStatus'] = intval(BaseAnnouncement::findOneVal(['id' => $job['announcement_id']],
                'audit_status'));
            $jobList['list'][$k]['announcementStatus']      = intval(BaseArticle::findOneVal(['id' => $articleId],
                'status'));
            $jobList['list'][$k]['is_show_title']           = $isShowName[$job['is_show']];
            $jobList['list'][$k]['status']                  = intval($job['status']);
            $jobList['list'][$k]['statusTitle']             = $jobStatusList[$job['status']];
            $jobList['list'][$k]['auditStatus']             = intval($job['audit_status']);
            $jobList['list'][$k]['auditStatusTitle']        = $jobAuditStatusList[$job['audit_status']];
            $jobList['list'][$k]['id']                      = intval($job['id']);
            $jobList['list'][$k]['uid']                     = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $job['id']);
            $jobList['list'][$k]['announcementId']          = intval($job['announcement_id']);
            $jobList['list'][$k]['jobApplyNum']             = intval($job['jobApplyNum']);
            $jobList['list'][$k]['isArticle']               = intval($job['is_article']);
            $jobList['list'][$k]['companyId']               = intval($job['company_id']);
            $jobList['list'][$k]['click']                   = intval($job['click']);
            $jobList['list'][$k]['amount']                  = intval($job['amount']);
            if (strtotime($job['refresh_time']) < 1) {
                $jobList['list'][$k]['refresh_time'] = '-';
            }
            if (strtotime($job['release_time']) < 1) {
                $jobList['list'][$k]['release_time'] = '-';
            }
            if (strtotime($job['real_refresh_time']) < 1) {
                $jobList['list'][$k]['real_refresh_time'] = '-';
            }

            if (strlen($job['creator']) < 1) {
                $jobList['list'][$k]['creator'] = '-';
            }
            //统计信息
            $apply += intval($job['jobApplyNum']);
            $click += intval($job['click']);
            $all++;

            //栏目信息
            $jobList['list'][$k]['home_column_title']     = '';
            $jobList['list'][$k]['home_sub_column_title'] = '';
            if ($job['home_column_id']) {
                $jobList['list'][$k]['home_column_title'] = BaseHomeColumn::findOneVal(['id' => $job['home_column_id']],
                    'name');
            }
            if ($job['home_sub_column_ids']) {
                $homeSubColumnIds = explode(',', $job['home_sub_column_ids']);
                $homeSubColumn    = BaseHomeColumn::find()
                    ->select(['name'])
                    ->where([
                        'in',
                        'id',
                        $homeSubColumnIds,
                    ])
                    ->asArray()
                    ->all();
                foreach ($homeSubColumn as $item) {
                    $jobList['list'][$k]['home_sub_column_title'] .= $item['name'] . ',';
                }

                $jobList['list'][$k]['home_sub_column_title'] = substr($jobList['list'][$k]['home_sub_column_title'], 0,
                    -1);
            }

            // 学科
            $jobList['list'][$k]['majorTxt'] = BaseMajor::getAllMajorName(explode(',', $job['major_id']));
        }

        if ($keywords['export']) {
            $realKey  = Cache::PC_ADMIN_TABLE_STAGING_FIELD_KEY . ':' . 'announcementJobQuery' . ':' . $adminId;
            $keyValue = Cache::get($realKey) ?: '';
            if (sizeof($keyValue) > 0) {
                $keyValueArray = json_decode($keyValue, true);
                $keyHeaders    = [];
                $headers       = [];
                foreach ($keyValueArray as $key => $keyList) {
                    if ($keyList['select'] && $keyList['name'] != 'operation') {
                        $keyHeaders[] = $keyList['name'];
                        $headers[]    = $keyList['v'];
                    }
                }
                if (!$keyHeaders || !$headers || sizeof($keyHeaders) <> sizeof($headers)) {
                    throw new Exception('当前不可下载');
                }

                $data = [];
                foreach ($jobList['list'] as $k => $val) {
                    $temp = [];
                    foreach ($keyHeaders as $keys) {
                        $temp [] = $val[$keys];
                    }
                    $data[] = $temp;
                }

                $excel    = new Excel();
                $fileName = $excel->export($data, $headers);

                return [
                    'excelUrl' => $fileName,
                ];
            } else {
                throw new Exception('未知下载列');
            }
        } else {
            $jobList['amount'] = [
                'apply' => $apply,
                'click' => $click,
                'all'   => $all,
            ];

            return $jobList;
        }
    }

    public static function getAnnouncementSimpleJobList($keywords)
    {
        $select = [
            'j.id',
            'j.name',
            'j.update_time',
            'j.refresh_time',
            'j.status',
            'j.department',
            'j.city_id',
            'j.period_date',
            'j.code',
            'j.education_type',
            'j.min_wage',
            'j.max_wage',
            'j.is_article',
            'j.audit_status',
            'j.release_time',
            'j.add_time',
            'j.wage_type',
            'j.remark',
            'j.offline_type',
            'j.experience_type',
            'j.age_type',
            'j.title_type',
            'j.company_id',
            'j.member_id',
            'j.period_date',
            'j.announcement_id',
            'j.member_id',
            'j.click',
            'j.amount',
            'j.gender_type',
            'j.create_type',
            'j.create_id',
            'j.creator',
            'j.delete_time',
            'j.is_show',
            'j.major_id',
            'j.job_category_id',
            'j.first_release_time',
            'j.real_refresh_time',
            'j.is_miniapp',
            'c.full_name as companyName',
            'an.title',
            'art.home_column_id',
            'art.home_sub_column_ids',
            'art.status as articleStatus',
        ];

        $query = self::find()
            ->alias('j')
            ->leftJoin(['an' => Announcement::tableName()], 'an.id = j.announcement_id')
            ->leftJoin(['art' => Article::tableName()], 'art.id=an.article_id')
            ->innerJoin(['c' => Company::tableName()], 'c.id=j.company_id')
            ->select($select);

        $query->where([
            'j.status' => [
                self::STATUS_ONLINE,
                self::STATUS_OFFLINE,
            ],
        ]);

        JobApply::uidJudgeWhere($keywords['announcement_title_num'], 'an.id', 'an.title', $query);
        $query->andFilterCompare('c.is_cooperation', $keywords['is_cooperation']);
        $query->andFilterCompare('j.announcement_id', $keywords['announcement'], 'like');
        $query->andFilterCompare('j.creator', $keywords['creator'], 'like');
        $query->andFilterCompare('j.nature_type', $keywords['nature_type']);
        $query->andFilterCompare('j.department', $keywords['department'], 'like');
        $query->andFilterCompare('j.abroad_type', $keywords['abroad_type']);
        $query->andFilterCompare('j.title_type', $keywords['title_type'], 'in');
        $query->andFilterCompare('j.audit_status', $keywords['audit_status']);
        $query->andFilterCompare('j.status', $keywords['status']);
        $query->andFilterCompare('j.age_type', $keywords['age_type']);
        $query->andFilterCompare('j.gender_type', $keywords['gender_type']);
        $query->andFilterCompare('j.political_type', $keywords['political_type']);
        $query->andFilterCompare('j.is_article', $keywords['is_article']);
        $query->andFilterCompare('j.is_miniapp', $keywords['is_miniapp']);
        $query->andFilterCompare('concat(j.city_id,j.address)', $keywords['city'], 'like');

        if ($keywords['education_type']) {
            $educationType = explode(',', $keywords['education_type']);
            $query->andFilterCompare('j.education_type', $educationType, 'in');
        }

        if ($keywords['experience_type']) {
            $experience = explode(',', $keywords['experience_type']);
            $query->andFilterCompare('j.experience_type', $experience, 'in');
        }

        if ($keywords['city']) {
            $city = explode(',', $keywords['city']);
            $query->andFilterCompare('j.city_id', $city, 'in');
        }

        if ($keywords['job_category_id']) {
            $jobCategoryId = explode(',', $keywords['job_category_id']);
            $temp          = [];
            foreach ($jobCategoryId as $categoryId) {
                $temp[] = (int)$categoryId;
            }
            $query->andFilterCompare('j.job_category_id', $temp, 'in');
        }

        if ($keywords['name']) {
            if (is_numeric($keywords['name']) && strlen($keywords['name']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['name']);
                $query->andFilterCompare('j.id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('j.name', $keywords['name'], 'like');
            }
        }

        if ($keywords['major_id']) {
            $majorIds  = explode(',', $keywords['major_id']);
            $condition = ['or'];
            foreach ($majorIds as $item) {
                $condition[] = "find_in_set(" . $item . ",j.major_id)";
            }
            $query->andWhere($condition);
        }

        if ($keywords['company_name']) {
            $findIds = BaseCompany::find()
                ->select('id')
                ->where([
                    'like',
                    'concat(full_name,id)',
                    $keywords['company_name'],
                ])
                ->asArray()
                ->all();

            $companyIds = [];
            foreach ($findIds as $id) {
                $companyIds[] = $id['id'];
            }

            $query->andFilterCompare('j.company_id', $companyIds, 'in');
        }
        if ($keywords['add_time_start']) {
            $query->andWhere([
                '>=',
                'j.add_time',
                TimeHelper::dayToBeginTime($keywords['add_time_start']),
            ]);
        }
        if ($keywords['add_time_end']) {
            $query->andWhere([
                '<=',
                'j.add_time',
                TimeHelper::dayToEndTime($keywords['add_time_end']),
            ]);
        }
        if ($keywords['release_time_start']) {
            $query->andWhere([
                '>=',
                'j.release_time',
                TimeHelper::dayToBeginTime($keywords['release_time_start']),
            ]);
        }
        if ($keywords['release_time_end']) {
            $query->andWhere([
                '<=',
                'j.release_time',
                TimeHelper::dayToEndTime($keywords['release_time_end']),
            ]);
        }
        if ($keywords['refresh_time_start']) {
            $query->andWhere([
                '>=',
                'j.real_refresh_time',
                TimeHelper::dayToBeginTime($keywords['refresh_time_start']),
            ]);
        }
        if ($keywords['refresh_time_end']) {
            $query->andWhere([
                '<=',
                'j.real_refresh_time',
                TimeHelper::dayToEndTime($keywords['refresh_time_end']),
            ]);
        }

        if ($keywords['first_release_time_start']) {
            $query->andWhere([
                '>=',
                'j.first_release_time',
                TimeHelper::dayToBeginTime($keywords['first_release_time_start']),
            ]);
        }
        if ($keywords['first_release_time_end']) {
            $query->andWhere([
                '<=',
                'j.first_release_time',
                TimeHelper::dayToEndTime($keywords['first_release_time_end']),
            ]);
        }

        if ($keywords['refresh_time_start']) {
            $query->andWhere([
                '>=',
                'j.refresh_time',
                TimeHelper::dayToBeginTime($keywords['refresh_time_start']),
            ]);
        }
        if ($keywords['refresh_time_end']) {
            $query->andWhere([
                '<=',
                'j.refresh_time',
                TimeHelper::dayToEndTime($keywords['refresh_time_end']),
            ]);
        }

        $orderBy = ' j.status desc,j.id desc';
        if ($keywords['sort_apply_num']) {
            $sort    = $keywords['sort_apply_num'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' jobApplyNum ' . $sort;
        }
        if ($keywords['sort_refresh_time']) {
            $sort    = $keywords['sort_refresh_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.refresh_time ' . $sort;
        }
        if ($keywords['sort_first_release_time']) {
            $sort    = $keywords['sort_first_release_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.first_release_time ' . $sort;
        }
        if ($keywords['sort_real_refresh_time']) {
            $sort    = $keywords['sort_real_refresh_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.real_refresh_time ' . $sort;
        }
        if ($keywords['sort_release_time']) {
            $sort    = $keywords['sort_release_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.release_time ' . $sort;
        }
        if ($keywords['sort_amount']) {
            $sort    = $keywords['sort_amount'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.amount ' . $sort;
        }
        if ($keywords['sort_click']) {
            $sort    = $keywords['sort_click'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' allClick ' . $sort;
        }
        if ($keywords['sort_add_time']) {
            $sort    = $keywords['sort_add_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.add_time ' . $sort;
        }
        if ($keywords['sort_period_date']) {
            $sort    = $keywords['sort_period_date'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.period_date ' . $sort;
        }

        if ($keywords['establishment_type']) {
            // 这里会有几种可能性,-1和其他值(-1的情况下是没有选编制的类型,其他值是选了编制类型里面某个并且find_in_set
            $establishmentType = explode(',', $keywords['establishment_type']);
            $orWhere           = [
                'or',
            ];
            foreach ($establishmentType as $itemKey => $establishmentItem) {
                if ($establishmentItem == -1) {
                    $orWhere[] = ['j.is_establishment' => BaseJob::IS_ESTABLISHMENT_NO];
                } else {
                    $establishmentItemKey = 'establishmentItem' . $itemKey;
                    $orWhere[]            = new Expression("FIND_IN_SET(:" . $establishmentItemKey . ", j.establishment_type)",
                        [$establishmentItemKey => $establishmentItem]);
                }
            }
            $query->andWhere($orWhere);
        }

        $count    = $query->count();
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $jobList  = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        $jobStatusList      = self::JOB_STATUS_NAME;
        $jobAuditStatusList = self::JOB_AUDIT_STATUS_NAME;
        $educationTypeList  = BaseDictionary::getEducationList();
        $experienceList     = BaseDictionary::getExperienceList();
        $isShowName         = BaseJob::IS_SHOW_NAME;
        foreach ($jobList as &$job) {
            $experienceTypeTitle = $experienceList[intval($job['experience_type'])];
            $educationTypeTitle  = $educationTypeList[$job['education_type']];
            if ($job['education_type'] < 1 || strlen($job['education_type']) < 1) {
                $educationTypeTitle = '学历不限';
            }
            if ($job['experience_type'] < 1 || strlen($job['experience_type']) < 1) {
                $experienceTypeTitle = '经验不限';
            }
            $city = Area::getAreaName($job['city_id']);
            //输出薪资
            $wage = BaseJob::formatWage($job['min_wage'], $job['max_wage'], $job['wage_type']);

            $job['basicInformation'] = $experienceTypeTitle . '|' . $educationTypeTitle . '|' . $city . "｜" . $wage;
            //关联公告
            $job['announcementTitle'] = "";
            if ($job['member_id']) {
                $companyInfo    = Company::getCompanyInfo($job['member_id']);
                $job['company'] = $companyInfo['full_name'];
            }
            $articleId                      = BaseAnnouncement::findOneVal(['id' => $job['announcement_id']],
                'article_id');
            $job['announcementAuditStatus'] = intval(BaseAnnouncement::findOneVal(['id' => $job['announcement_id']],
                'audit_status'));
            $job['announcementStatus']      = intval(BaseArticle::findOneVal(['id' => $articleId], 'status'));
            $job['is_show_title']           = $isShowName[$job['is_show']];
            $job['status']                  = intval($job['status']);
            $job['statusTitle']             = $jobStatusList[$job['status']];
            $job['auditStatus']             = intval($job['audit_status']);
            $job['auditStatusTitle']        = $jobAuditStatusList[$job['audit_status']];
            $job['id']                      = intval($job['id']);
            $job['uid']                     = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $job['id']);
            $job['announcementId']          = intval($job['announcement_id']);
            $job['jobApplyNum']             = intval($job['jobApplyNum']);
            $job['isArticle']               = intval($job['is_article']);
            $job['companyId']               = intval($job['company_id']);
            $job['click']                   = intval($job['click']);
            $job['amount']                  = intval($job['amount']);
            if (strtotime($job['refresh_time']) < 1) {
                $job['refresh_time'] = '-';
            }
            if (strtotime($job['release_time']) < 1) {
                $job['release_time'] = '-';
            }
            if (strtotime($job['real_refresh_time']) < 1) {
                $job['real_refresh_time'] = '-';
            }

            if (strlen($job['creator']) < 1) {
                $job['creator'] = '-';
            }

            //栏目信息
            $job['home_column_title']     = '';
            $job['home_sub_column_title'] = '';
            if ($job['home_column_id']) {
                $job['home_column_title'] = BaseHomeColumn::findOneVal(['id' => $job['home_column_id']], 'name');
            }
            if ($job['home_sub_column_ids']) {
                $homeSubColumnIds = explode(',', $job['home_sub_column_ids']);
                $homeSubColumn    = BaseHomeColumn::find()
                    ->select(['name'])
                    ->where([
                        'in',
                        'id',
                        $homeSubColumnIds,
                    ])
                    ->asArray()
                    ->all();
                foreach ($homeSubColumn as $item) {
                    $job['home_sub_column_title'] .= $item['name'] . ',';
                }

                $job['home_sub_column_title'] = substr($job['home_sub_column_title'], 0, -1);
            }

            // 学科
            $job['majorTxt'] = BaseMajor::getAllMajorName(explode(',', $job['major_id']));
        }

        return [
            'list' => $jobList,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];
    }

    /**
     * 校验搜索公告职位字段（非合作单位）
     * @param     $keywords
     * @return array
     * @throws Exception
     */
    public static function announcementValidateSearchKeyWords($keywords): array
    {
        $select = [
            'j.id',
            'j.name',
            'j.update_time',
            'j.refresh_time',
            'j.status',
            'j.department',
            'j.city_id',
            'j.period_date',
            'j.code',
            'j.education_type',
            'j.min_wage',
            'j.max_wage',
            'j.is_article',
            'j.audit_status',
            'j.release_time',
            'j.add_time',
            'j.wage_type',
            'j.remark',
            'j.offline_type',
            'j.experience_type',
            'j.age_type',
            'j.title_type',
            'j.company_id',
            'j.member_id',
            'j.period_date',
            'j.announcement_id',
            'j.member_id',
            'j.click',
            'j.amount',
            'j.gender_type',
            'j.create_type',
            'j.create_id',
            'j.creator',
            'j.delete_time',
            'j.is_show',
            'j.major_id',
            'j.job_category_id',
            'j.first_release_time',
            'j.real_refresh_time',
            'an.title',
            'art.home_column_id',
            'art.home_sub_column_ids',
            'art.status as articleStatus',
            'SUM(j.click) as allClick',
            'GROUP_CONCAT(a.id SEPARATOR "|") as ids',
            //站外投递
            'COUNT(a.job_id) as jobApplyNum',
        ];

        $query = self::find()
            ->alias('j')
            ->leftJoin(['a' => OffSiteJobApply::tableName()], 'a.job_id = j.id')
            ->leftJoin(['an' => Announcement::tableName()], 'an.id = j.announcement_id')
            ->leftJoin(['art' => Article::tableName()], 'art.id=an.article_id')
            ->leftJoin(['c' => Company::tableName()], 'c.id=j.company_id')
            ->select($select)
            ->groupBy(['j.id']);

        $query->where([
            'j.status' => [
                self::STATUS_ONLINE,
                self::STATUS_OFFLINE,
            ],
        ]);

        JobApply::uidJudgeWhere($keywords['announcement_title_num'], 'an.id', 'an.title', $query);
        $query->andFilterCompare('c.is_cooperation', $keywords['is_cooperation']);
        $query->andFilterCompare('j.announcement_id', $keywords['announcement'], 'like');
        $query->andFilterCompare('j.creator', $keywords['creator'], 'like');
        $query->andFilterCompare('j.nature_type', $keywords['nature_type']);
        $query->andFilterCompare('j.department', $keywords['department'], 'like');
        $query->andFilterCompare('j.abroad_type', $keywords['abroad_type']);
        $query->andFilterCompare('j.title_type', $keywords['title_type'], 'in');
        $query->andFilterCompare('j.audit_status', $keywords['audit_status']);
        $query->andFilterCompare('j.status', $keywords['status']);
        $query->andFilterCompare('j.age_type', $keywords['age_type']);
        $query->andFilterCompare('j.gender_type', $keywords['gender_type']);
        $query->andFilterCompare('j.political_type', $keywords['political_type']);
        $query->andFilterCompare('j.is_article', $keywords['is_article']);
        $query->andFilterCompare('concat(j.city_id,j.address)', $keywords['city'], 'like');

        if ($keywords['education_type']) {
            $educationType = explode(',', $keywords['education_type']);
            $query->andFilterCompare('j.education_type', $educationType, 'in');
        }

        if ($keywords['experience_type']) {
            $experience = explode(',', $keywords['experience_type']);
            $query->andFilterCompare('j.experience_type', $experience, 'in');
        }

        if ($keywords['city']) {
            $city = explode(',', $keywords['city']);
            $query->andFilterCompare('j.city_id', $city, 'in');
        }

        if ($keywords['job_category_id']) {
            $jobCategoryId = explode(',', $keywords['job_category_id']);
            $temp          = [];
            foreach ($jobCategoryId as $categoryId) {
                $temp[] = (int)$categoryId;
            }
            $query->andFilterCompare('j.job_category_id', $temp, 'in');
        }

        if ($keywords['name']) {
            if (is_numeric($keywords['name']) && strlen($keywords['name']) == 8) {
                $nameChangeUid = UUIDHelper::decryption($keywords['name']);
                $query->andFilterCompare('j.id', (int)$nameChangeUid);
            } else {
                $query->andFilterCompare('j.name', $keywords['name'], 'like');
            }
        }

        if ($keywords['major_id']) {
            $majorIds  = explode(',', $keywords['major_id']);
            $condition = ['or'];
            foreach ($majorIds as $item) {
                $condition[] = "find_in_set(" . $item . ",j.major_id)";
            }
            $query->andWhere($condition);
        }

        if ($keywords['company']) {
            $findIds = BaseCompany::find()
                ->select('id')
                ->where([
                    'like',
                    'concat(full_name,id)',
                    $keywords['company'],
                ])
                ->asArray()
                ->all();

            $companyIds = [];
            foreach ($findIds as $id) {
                $companyIds[] = $id['id'];
            }
            $query->andFilterCompare('j.company_id', $companyIds, 'in');
        }
        if ($keywords['add_time_start']) {
            $query->andWhere([
                '>=',
                'j.add_time',
                TimeHelper::dayToBeginTime($keywords['add_time_start']),
            ]);
        }
        if ($keywords['add_time_end']) {
            $query->andWhere([
                '<=',
                'j.add_time',
                TimeHelper::dayToEndTime($keywords['add_time_end']),
            ]);
        }
        if ($keywords['release_time_start']) {
            $query->andWhere([
                '>=',
                'j.release_time',
                TimeHelper::dayToBeginTime($keywords['release_time_start']),
            ]);
        }
        if ($keywords['release_time_end']) {
            $query->andWhere([
                '<=',
                'j.release_time',
                TimeHelper::dayToEndTime($keywords['release_time_end']),
            ]);
        }
        if ($keywords['refresh_time_start']) {
            $query->andWhere([
                '>=',
                'j.real_refresh_time',
                TimeHelper::dayToBeginTime($keywords['refresh_time_start']),
            ]);
        }
        if ($keywords['refresh_time_end']) {
            $query->andWhere([
                '<=',
                'j.real_refresh_time',
                TimeHelper::dayToEndTime($keywords['refresh_time_end']),
            ]);
        }

        if ($keywords['first_release_time_start']) {
            $query->andWhere([
                '>=',
                'j.first_release_time',
                TimeHelper::dayToBeginTime($keywords['first_release_time_start']),
            ]);
        }
        if ($keywords['first_release_time_end']) {
            $query->andWhere([
                '<=',
                'j.first_release_time',
                TimeHelper::dayToEndTime($keywords['first_release_time_end']),
            ]);
        }

        if ($keywords['refresh_time_start']) {
            $query->andWhere([
                '>=',
                'j.refresh_time',
                TimeHelper::dayToBeginTime($keywords['refresh_time_start']),
            ]);
        }
        if ($keywords['refresh_time_end']) {
            $query->andWhere([
                '<=',
                'j.refresh_time',
                TimeHelper::dayToEndTime($keywords['refresh_time_end']),
            ]);
        }

        $orderBy = ' j.status desc,j.add_time desc';
        if ($keywords['sort_apply_num']) {
            $sort    = $keywords['sort_apply_num'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' jobApplyNum ' . $sort;
        }
        if ($keywords['sort_refresh_time']) {
            $sort    = $keywords['sort_refresh_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.refresh_time ' . $sort;
        }
        if ($keywords['sort_first_release_time']) {
            $sort    = $keywords['sort_first_release_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.first_release_time ' . $sort;
        }
        if ($keywords['sort_real_refresh_time']) {
            $sort    = $keywords['sort_real_refresh_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.real_refresh_time ' . $sort;
        }
        if ($keywords['sort_release_time']) {
            $sort    = $keywords['sort_release_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.release_time ' . $sort;
        }
        if ($keywords['sort_amount']) {
            $sort    = $keywords['sort_amount'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.amount ' . $sort;
        }
        if ($keywords['sort_click']) {
            $sort    = $keywords['sort_click'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' allClick ' . $sort;
        }
        if ($keywords['sort_add_time']) {
            $sort    = $keywords['sort_add_time'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.add_time ' . $sort;
        }
        if ($keywords['sort_period_date']) {
            $sort    = $keywords['sort_period_date'] == self::ORDER_BY_DESC ? ' desc ' : ' asc ';
            $orderBy = ' j.period_date ' . $sort;
        }

        if ($keywords['establishment_type']) {
            // 这里会有几种可能性,-1和其他值(-1的情况下是没有选编制的类型,其他值是选了编制类型里面某个并且find_in_set
            $establishmentType = explode(',', $keywords['establishment_type']);
            $orWhere           = [
                'or',
            ];
            foreach ($establishmentType as $itemKey => $establishmentItem) {
                if ($establishmentItem == -1) {
                    $orWhere[] = ['j.is_establishment' => BaseJob::IS_ESTABLISHMENT_NO];
                } else {
                    $establishmentItemKey = 'establishmentItem' . $itemKey;
                    $orWhere[]            = new Expression("FIND_IN_SET(:" . $establishmentItemKey . ", j.establishment_type)",
                        [$establishmentItemKey => $establishmentItem]);
                }
            }
            $query->andWhere($orWhere);
        }

        $count    = $query->count();
        $pageSize = $keywords['limit'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $jobList  = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        DebugHelper::writeLog($query->createCommand()
            ->getRawSql(), '非合作单位公告职位');

        return [
            'list' => $jobList,
            'page' => [
                'count' => (int)$count,
                'limit' => (int)$pageSize,
                'page'  => (int)$keywords['page'],
            ],
        ];
    }

    /**
     * 站外投递列表
     * @param $keywords
     * @return array
     * @throws Exception
     */
    public static function offJobApplyList($keywords): array
    {
        $select = [
            'o.id',
            'o.job_id',
            'o.job_name',
            'o.resume_id',
            'o.resume_attachment_id',
            'o.source',
            'o.apply_date',
            'o.apply_status',
            'r.name as resume_name',
            'ra.file_name as resume_attachment_name',
        ];
        $query  = OffSiteJobApply::find()
            ->alias('o')
            ->leftJoin(['r' => Resume::tableName()], 'r.id=o.resume_id')
            ->leftJoin(['ra' => ResumeAttachment::tableName()], 'ra.id=o.resume_attachment_id')
            ->select($select)
            ->where([
                'announcement_id' => $keywords['announcement_id'],
            ]);

        if ($keywords['name_num']) {
            if (strlen($keywords['name_num']) == 8 && UUIDHelper::decryption($keywords['name_num'])) {
                $query->andWhere([
                    'or',
                    [
                        'o.job_id' => UUIDHelper::decryption($keywords['name_num']),
                    ],
                    [
                        'o.resume_id' => UUIDHelper::decryption($keywords['name_num']),
                    ],
                ]);
            } else {
                $query->andWhere([
                    'or',
                    [
                        'like',
                        'o.job_name',
                        $keywords['name_num'],
                    ],
                    [
                        'like',
                        'r.name',
                        $keywords['name_num'],
                    ],
                ]);
            }
        }

        if ($keywords['apply_date_start']) {
            $query->andWhere([
                '>=',
                'apply_date',
                $keywords['apply_date_start'],
            ]);
        }
        if ($keywords['apply_date_end']) {
            $query->andWhere([
                '<=',
                'apply_date',
                $keywords['apply_date_end'],
            ]);
        }

        $orderBy  = 'apply_date desc';
        $count    = $query->count();
        $pageSize = $keywords['page_size'] ?: Yii::$app->params['defaultPageSize'];
        $pages    = self::setPage($count, $keywords['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy($orderBy)
            ->asArray()
            ->all();

        foreach ($list as &$item) {
            $item['jobUid']         = UUIDHelper::encrypt(UUIDHelper::TYPE_JOB, $item['job_id']);
            $item['resumeUid']      = UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $item['resume_id']);
            $item['applyStatusTxt'] = BaseOffSiteJobApply::APPLY_STATUS_LIST[$item['apply_status']] ?: '';
        }

        return [
            'list' => $list,
            'page' => [
                'count'    => (int)$count,
                'pageSize' => (int)$pageSize,
                'page'     => (int)$keywords['page'],
            ],
        ];
    }

    /**
     * 职位审核详情上下页切换列表
     * @param $params
     * @return array|\yii\db\ActiveRecord|null
     * @throws \Exception
     */
    public static function jobAuditDetailList($params)
    {
        // 获取待审核职位列表
        $query = self::find()
            ->where([
                'announcement_id' => $params['id'],
                'is_show'         => self::IS_SHOW_YES,
            ])
            ->andWhere([
                'status' => [
                    self::STATUS_WAIT,
                    self::STATUS_ONLINE,
                ],
            ])
            ->andWhere([
                'in',
                'audit_status',
                [
                    self::AUDIT_STATUS_WAIT_AUDIT,
                    self::AUDIT_STATUS_REFUSE_AUDIT,
                    self::AUDIT_STATUS_WAIT,
                ],
            ]);

        $count    = $query->count();
        $pageSize = $params['pageSize'] ?: 1;
        $pages    = self::setPage($count, $params['page'], $pageSize);
        $list     = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->orderBy('add_time desc')
            ->asArray()
            ->all();

        $detailList = [];
        foreach ($list as $item) {
            $detailList[] = self::getDetails(['id' => $item['id']]);
        }

        return [
            'list' => $detailList,
            'page' => [
                'count'    => (int)$count,
                'pageSize' => (int)$pageSize,
                'page'     => (int)$params['page'],
            ],
        ];
    }

    /**
     * 修改学历限制
     * @param $id
     * @param $isLimit
     * @return void
     * @throws Exception
     */
    public static function changeEducationLimit($data)
    {
        $id      = $data['id'];
        $isLimit = $data['isLimit'];
        $type    = $data['type'];

        //获取实际上存在表里的值
        if ($type == BaseJobHandleLog::HANDLE_TYPE_LIMIT_EDUCATION || $type == BaseJobHandleLog::HANDLE_TYPE_CANCEL_LIMIT_EDUCATION) {
            $limitValue = self::DELIVERY_LIMIT_EDUCATION;
        } elseif ($type == BaseJobHandleLog::HANDLE_TYPE_LIMIT_FILE || $type == BaseJobHandleLog::HANDLE_TYPE_CANCEL_LIMIT_FILE) {
            $limitValue = self::DELIVERY_LIMIT_MATERIAL;
        }

        //这里会比较麻烦，因为两个限制在同一个字段，所以需要先化数组，改字段，再转字符串
        try {
            $jobModel = self::findOne(['id' => $id]);
            if (!$jobModel) {
                throw new Exception('职位不存在');
            }

            //取下旧数据，获取修改前的状态，写入日志
            $logData = [];
            if (!empty($jobModel['delivery_limit_type'])) {
                $deliveryLimitTypeArr = explode(',', $jobModel['delivery_limit_type']);
                foreach ($deliveryLimitTypeArr as $val) {
                    $beforeDeliveryLimitTypeText .= self::DELIVERY_LIMIT_LIST[$val] . ';';
                }
                $logData['handle_before'] = ['职位限制' => $beforeDeliveryLimitTypeText];
            } else {
                $deliveryLimitTypeArr     = [];
                $logData['handle_before'] = ['职位限制' => ''];
            }

            //判断是要加上限制，还是去除限制
            if ($isLimit == 1) {
                if (!in_array($limitValue, $deliveryLimitTypeArr)) {
                    array_push($deliveryLimitTypeArr, $limitValue);
                    //数组转为字符串
                    sort($deliveryLimitTypeArr);
                }

                $newDeliveryLimitTypeFieldText = implode(',', $deliveryLimitTypeArr);
            } else {
                //去除限制，删除这个值
                if (!empty($deliveryLimitTypeArr)) {
                    foreach ($deliveryLimitTypeArr as $key => $item) {
                        if ($item == $limitValue) {
                            unset($deliveryLimitTypeArr[$key]);
                        }
                    }
                }
                //数组转为字符串
                sort($deliveryLimitTypeArr);
                $newDeliveryLimitTypeFieldText = implode(',', $deliveryLimitTypeArr);
            }

            //获取修改后的限制值，写入日志
            $afterDeliveryLimitTypeText = '';
            foreach ($deliveryLimitTypeArr as $val) {
                $afterDeliveryLimitTypeText .= self::DELIVERY_LIMIT_LIST[$val] . ';';
            }

            $jobModel->delivery_limit_type = $newDeliveryLimitTypeFieldText ?: '';
            if (!$jobModel->save()) {
                throw new Exception('保存失败' . $jobModel->getFirstErrorsMessage());
            }

            //写入记录
            $logData['job_id']        = $id;
            $logData['handle_before'] = ['职位限制' => $afterDeliveryLimitTypeText];
            $logData['handle_type']   = (string)$type;
            self::createJobHandleLog($logData);
        } catch (\Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @param $code
     * 这个方法是专门给编制在运营后台展示的,和求职者那边隔离开来,因为两边的文案要求可能是不一致的
     */
    public static function getEstablishmentName($code)
    {
        // 首先转成数组,本来是逗号隔开的
        $codeArr = explode(',', $code);
        // 循环去字典表里面取
        $establishmentNameArr = [];
        foreach ($codeArr as $item) {
            $establishmentNameArr[] = BaseDictionary::getEstablishmentName($item);
        }

        // 最后/隔开
        return implode('/', $establishmentNameArr);
    }

    public static function majorMessageNotice($jobId)
    {
        $majorId = BaseMajor::HIDE_MAJOR_ID;
        if (BaseJobMajorRelation::find()
            ->where([
                'job_id'   => $jobId,
                'major_id' => $majorId,
            ])
            ->exists()) {
            (new JobAutoClassify($jobId))->updateJobMajorTable();

            return [
                'msg' => '该职位关联学科【电子信息】已不存在,职位ID:' . (BaseJob::findOneVal(['id' => $jobId], 'uuid')),
                'res' => false,
            ];
        }

        return [
            'res' => true,
        ];
    }
}