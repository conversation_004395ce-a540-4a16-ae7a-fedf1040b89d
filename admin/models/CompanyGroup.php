<?php
/**
 * create user：shannon
 * create time：2024/7/1 16:48
 */
namespace admin\models;

use common\base\BaseActiveRecord;
use common\base\models\BaseAdmin;
use common\base\models\BaseCompanyGroup;
use common\helpers\ArrayHelper;
use common\helpers\TimeHelper;
use queue\Producer;
use Yii;
use yii\base\Exception;

class CompanyGroup extends BaseCompanyGroup
{
    /**
     * 获取列表
     * @param $params
     * @return array
     */
    public static function index($params)
    {
        $query = self::find()
            ->alias('cg')
            ->leftJoin(['a' => BaseAdmin::tableName()], 'a.id=cg.admin_id')
            ->where(['cg.is_delete' => self::IS_DELETE_NO]);

        //        //搜索群组ID
        //        $query->andFilterWhere(['cg.id' => $params['id']]);
        //        //搜索群组名称
        //        $query->andFilterWhere([
        //            'like',
        //            'cg.group_name',
        //            $params['group_name'],
        //        ]);
        //关键字搜索
        $query->andFilterWhere([
            'or',
            ['cg.id' => $params['keyword']],
            [
                'like',
                'cg.group_name',
                $params['keyword'],
            ],
        ]);
        //搜索创建时间
        if (!empty($params['addTimeStart']) && !empty($params['addTimeEnd'])) {
            $query->andFilterWhere([
                'between',
                'cg.add_time',
                TimeHelper::dayToBeginTime($params['addTimeStart']),
                TimeHelper::dayToEndTime($params['addTimeEnd']),
            ]);
        }
        //获取总数
        $total = $query->count();
        //分页
        $page_size = $params['page_size'] ?: Yii::$app->params['defaultPageSize'];
        $pages     = BaseActiveRecord::setPage($total, $params['page'], $page_size);
        $list      = $query->select([
            'cg.id',
            'cg.group_name',
            'cg.sort_number',
            'cg.weight',
            'cg.company_number',
            'cg.add_time',
            'a.name',
        ])
            ->offset($pages['offset'])
            ->orderBy('cg.sort_number asc')
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        return [
            'list'  => $list,
            'total' => (int)$total,
        ];
    }

    /**
     * 下载群组数据
     */

    /**
     * 创建群组
     * @throws Exception
     */
    public static function add($params)
    {
        //验证参数
        // group_name-请录入群组名称/限录入100个字符/名称已存在
        // sort_number-请录入群组序号/序号已存在
        // description-限录入200个字符
        if (empty($params['groupName'])) {
            throw new Exception('群组名称不能为空');
        }
        if (empty($params['sortNumber'])) {
            throw new Exception('排序不能为空');
        }
        if (mb_strlen($params['groupName']) > 100) {
            throw new Exception('群组名称不能超过100个字符');
        }
        if (mb_strlen($params['description']) > 200) {
            throw new Exception('群组说明不能超过200个字符');
        }
        if (self::find()
            ->where([
                'group_name' => $params['groupName'],
                'is_delete'  => self::IS_DELETE_NO,
            ])
            ->exists()) {
            throw new Exception('群组名称已存在');
        }
        if (self::find()
            ->where([
                'sort_number' => $params['sortNumber'],
                'is_delete'   => self::IS_DELETE_NO,
            ])
            ->exists()) {
            throw new Exception('排序序号已存在');
        }
        $model              = new self();
        $model->group_name  = $params['groupName'];
        $model->sort_number = $params['sortNumber'];
        $model->description = $params['description'];
        $model->admin_id    = Yii::$app->user->id;
        $model->add_time    = date('Y-m-d H:i:s');
        $model->update_time = date('Y-m-d H:i:s');
        $model->is_delete   = self::IS_DELETE_NO;
        if ($model->save()) {
            //推一次队列计算群组分值系统
            Producer::companyGroupScoreSystem($model->id);
            //成功后整个列表重新计算分值
            self::scoreCalculate();

            return true;
        } else {
            throw new Exception('创建失败');
        }
    }

    /**
     * 编辑群组
     * @throws Exception
     */
    public static function edit($params)
    {
        //验证参数
        // group_name-请录入群组名称/限录入100个字符/名称已存在
        // sort_number-请录入群组序号/序号已存在
        // description-限录入200个字符
        if (empty($params['groupName'])) {
            throw new Exception('群组名称不能为空');
        }
        if (empty($params['sortNumber'])) {
            throw new Exception('排序不能为空');
        }
        if (mb_strlen($params['groupName']) > 100) {
            throw new Exception('群组名称不能超过100个字符');
        }
        if (mb_strlen($params['description']) > 200) {
            throw new Exception('群组说明不能超过200个字符');
        }
        if (self::find()
            ->where([
                'group_name' => $params['groupName'],
                'is_delete'  => self::IS_DELETE_NO,
            ])
            ->andWhere([
                '<>',
                'id',
                $params['id'],
            ])
            ->exists()) {
            throw new Exception('群组名称已存在');
        }
        if (self::find()
            ->where([
                'sort_number' => $params['sortNumber'],
                'is_delete'   => self::IS_DELETE_NO,
            ])
            ->andWhere([
                '<>',
                'id',
                $params['id'],
            ])
            ->exists()) {
            throw new Exception('排序序号已存在');
        }
        $model = self::findOne($params['id']);
        if (!$model) {
            throw new Exception('群组不存在');
        }
        $oldSortNumber      = $model->sort_number;
        $model->group_name  = $params['groupName'];
        $model->sort_number = $params['sortNumber'];
        $model->description = $params['description'];
        if ($model->save()) {
            if ($oldSortNumber != $model->sort_number) {
                //推一次队列计算群组分值系统
                Producer::companyGroupScoreSystem($model->id);
                //成功后整个列表重新计算分值
                self::scoreCalculate();
            }

            return true;
        } else {
            throw new Exception('编辑失败');
        }
    }

    /**
     * 编辑初始化群组
     * @throws Exception
     */
    public static function editInit($params)
    {
        $info = self::find()
            ->select([
                'id',
                'group_name',
                'sort_number',
                'description',
            ])
            ->where(['id' => $params['id']])
            ->asArray()
            ->one();
        if (!$info) {
            throw new Exception('群组不存在');
        }

        return $info;
    }

    /**
     * 删除群组
     * @throws Exception
     */
    public static function del($id)
    {
        $model = self::findOne($id);
        if (!$model) {
            throw new Exception('群组不存在');
        }
        //验证单位数量大于0不允许删除
        if ($model->company_number > 0) {
            throw new Exception('该群组下有单位，不能删除');
        }
        if ($model->is_delete == self::IS_DELETE_YES) {
            throw new Exception('该群组已删除');
        }

        $model->is_delete   = self::IS_DELETE_YES;
        $model->update_time = date('Y-m-d H:i:s');
        if ($model->save()) {
            //推一次队列计算群组分值系统
            Producer::companyGroupScoreSystem($model->id);
            //成功后整个列表重新计算分值
            self::scoreCalculate();

            return true;
        }

        throw new Exception('删除失败');
    }

    /**
     * 筛选字段
     */
    public static function filter()
    {
        $list = self::find()
            ->select([
                'id',
                'group_name',
            ])
            ->where(['is_delete' => self::IS_DELETE_NO])
            ->asArray()
            ->all();

        return ArrayHelper::arr2KV($list, 'id', 'group_name');
    }
}