<?php

namespace admin\models;

use common\base\models\BaseResumeAttachment;
use common\base\models\BaseResumeIntention;
use common\helpers\ArrayHelper;
use common\helpers\FileHelper;
use common\helpers\TimeHelper;
use common\helpers\UUIDHelper;
use common\models\ResumeIntention;
use frontendPc\models\Dictionary;

class ResumeAttachment extends BaseResumeAttachment
{
    public static function getSearchList($searchData)
    {
        $query = self::find()
            ->alias('ra')
            ->leftJoin(['r' => Resume::tableName()], 'r.id = ra.resume_id')
            ->leftJoin(['m' => Member::tableName()], 'm.id = ra.member_id');

        //
        $statusWhere = [
            'or',
            [
                'in',
                'ra.status',
                [
                    self::STATUS_ACTIVE,
                    self::STATUS_DISABLE,
                ],
            ],
            [
                'and',
                ['ra.status' => self::STATUS_DELETE],
                [
                    'or',
                    [
                        '>',
                        'on_site_apply_amount',
                        0,
                    ],
                    [
                        '>',
                        'off_site_apply_amount',
                        0,
                    ],
                ],
            ],
        ];
        $query->andWhere($statusWhere);

        //上传时间
        if ($searchData['createTimeFrom'] && $searchData['createTimeTo']) {
            $query->andFilterWhere([
                'between',
                'm.add_time',
                TimeHelper::dayToBeginTime($searchData['createTimeFrom']),
                TimeHelper::dayToEndTime($searchData['createTimeTo']),
            ]);
        }
        //最近使用
        if ($searchData['lastApplyJobTimeFrom'] && $searchData['lastApplyJobTimeTo']) {
            $query->andFilterWhere([
                'between',
                'r.last_apply_job_time',
                TimeHelper::dayToBeginTime($searchData['lastApplyJobTimeFrom']),
                TimeHelper::dayToEndTime($searchData['lastApplyJobTimeTo']),
            ]);
        }
        //人才检索
        if (strlen($searchData['userKeyword']) == 8 && !empty(UUIDHelper::decryption($searchData['userKeyword']))) {
            $userKeyWordId = UUIDHelper::decryption($searchData['userKeyword']);
            if ($userKeyWordId) {
                $query->andFilterWhere([
                    '=',
                    'm.id',
                    $userKeyWordId,

                ]);
            }
        } else {
            $query->andFilterWhere([
                'or',
                [
                    'like',
                    'r.name',
                    $searchData['userKeyword'],
                ],
                [
                    'like',
                    'm.username',
                    $searchData['userKeyword'],
                ],

            ]);
        }

        //附件检索
        $query->andFilterWhere([
            'or',
            [
                'like',
                'ra.file_name',
                $searchData['attachmentKeyword'],
            ],
            [
                'ra.id' => $searchData['attachmentKeyword'],
            ],
        ]);

        $query->select([
            'ra.id',
            'ra.token',
            'ra.status',
            'ra.file_name',
            'ra.member_id',
            'r.id as resumeId',
            'r.title_id',
            'r.name',
            'm.username',
            'r.age',
            'r.gender',
            'r.work_experience',
            'r.household_register_id',
            'r.work_status',
            'r.arrive_date',
            'm.add_time as createTime',
            'm.last_login_time',
            'm.source_type',
            'r.arrive_date_type',
            'r.last_apply_job_time',
            'ra.add_time',
            'ra.on_site_apply_amount',
            'ra.off_site_apply_amount',
            'ra.download_amount as resumeDownloadAmount',
            'r.residence',
        ]);

        $count = $query->count();
        $sort  = '';
        //根据创建时间排序
        if (!empty($searchData['sortUploadAmount'])) {
            if ($searchData['sortUploadAmount'] == self::SORT_UPLOAD_TIME_ASC) {
                $sort = 'ra.add_time asc';
            } elseif ($searchData['sortUploadAmount'] == self::SORT_UPLOAD_TIME_DESC) {
                $sort = 'ra.add_time desc';
            }
        }

        //根据最近使用时间排序
        if (!empty($searchData['sortLastApplyTime'])) {
            if ($searchData['sortLastApplyTime'] == self::SORT_LAST_APPLY_TIME_ASC) {
                $sort = 'r.last_apply_job_time asc';
            } elseif ($searchData['sortLastApplyTime'] == self::SORT_LAST_APPLY_TIME_DESC) {
                $sort = 'r.last_apply_job_time desc';
            }
        }

        //根据站内投递数量排序
        if (!empty($searchData['sortOnSiteApplyAmount'])) {
            if ($searchData['sortOnSiteApplyAmount'] == self::SORT_ON_SITE_APPLY_AMOUNT_ASC) {
                $sort = 'ra.on_site_apply_amount asc';
            } elseif ($searchData['sortOnSiteApplyAmount'] == self::SORT_ON_SITE_APPLY_AMOUNT_DESC) {
                $sort = 'ra.on_site_apply_amount desc';
            }
        }
        //根据站外投递数量排序
        if (!empty($searchData['sortOffSiteApplyAmount'])) {
            if ($searchData['sortOffSiteApplyAmount'] == self::SORT_OFF_SITE_APPLY_AMOUNT_ASC) {
                $sort = 'ra.off_site_apply_amount asc';
            } elseif ($searchData['sortOffSiteApplyAmount'] == self::SORT_OFF_SITE_APPLY_AMOUNT_DESC) {
                $sort = 'ra.off_site_apply_amount desc';
            }
        }

        //根据下载数量排序
        if (!empty($searchData['sortDownloadAmount'])) {
            if ($searchData['sortDownloadAmount'] == self::SORT_RESUME_DOWNLOAD_AMOUNT_ASC) {
                $sort = 'ra.download_amount asc';
            } elseif ($searchData['sortDownloadAmount'] == self::SORT_RESUME_DOWNLOAD_AMOUNT_DESC) {
                $sort = 'ra.download_amount desc';
            }
        }
        if (!empty($sort)) {
            if (empty($searchData['sortUploadAmount'])) {
                $sort .= ',ra.add_time desc';
            }
        } else {
            $sort = 'ra.add_time desc';
        }

        $query->orderBy($sort);

        $pageSize = $searchData['pageSize'] ?: \Yii::$app->params['defaultPageSize'];

        $pages = self::setPage($count, $searchData['page'], $pageSize);

        $list = $query->offset($pages['offset'])
            ->limit($pages['limit'])
            ->asArray()
            ->all();

        foreach ($list as $k => &$record) {
            $record['id']  = UUIDHelper::encrypt(UUIDHelper::TYPE_RESUME_ATTACHMENT, $record['id']);
            $record['uid'] = UUIDHelper::encrypt(UUIDHelper::TYPE_PERSON, $record['resumeId']);
            //获取最高学历
            $educationId   = ResumeEducation::findOneVal(['id' => $record['last_education_id']], 'education_id');
            $educationName = Dictionary::getEducationName($educationId);
            //获取专业id
            $majorId   = ResumeEducation::findOneVal(['id' => $record['last_education_id']], 'major_id');
            $majorName = Major::getMajorName($majorId);
            //获取现居住地
            $residence = Area::getAreaName($record['residence']);
        }

        //获取总的投递次数
        $totalApplyAmount = self::find()
            ->alias('ra')
            ->where($statusWhere)
            ->sum('off_site_apply_amount+on_site_apply_amount');

        $data = [
            'list'             => $list,
            'totalApplyAmount' => $totalApplyAmount,
            'page'             => [
                'limit' => $pages['limit'],
                'count' => (int)$count,
                'page'  => $searchData['page'],
            ],
        ];

        return $data;
    }

    /**
     * 获取用户求职简历列表
     * @param $memberId
     * @return array|\yii\db\ActiveRecord[]
     */
    public static function getPersonList($memberId)
    {
        $list = self::find()
            ->where(['member_id' => $memberId])
            ->andWhere(['status' => self::STATUS_ACTIVE])
            ->select([
                'add_time',
                'file_name',
                'token',
                'resume_id',
            ])
            ->orderBy('id desc')
            ->asArray()
            ->all();

        return $list;
    }

}