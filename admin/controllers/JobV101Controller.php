<?php

namespace admin\controllers;

use admin\models\Job;
use admin\models\JobHandleLog;
use admin\models\UploadForm;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyMemberInfo;
use common\base\models\BaseJob;
use common\base\models\BaseJobContact;
use common\base\models\BaseJobContactSynergy;
use common\base\models\BaseJobHandleLog;
use common\base\models\BaseMember;
use common\helpers\ArrayHelper;
use common\helpers\FormatConverter;
use common\helpers\ValidateHelper;
use common\service\CommonService;
use common\service\downloadTask\DownLoadTaskApplication;
use common\service\job\AuditService;
use common\service\job\BaseService;
use common\service\job\JobService;
use common\service\v2\job\AddBatchImportService;
use common\service\v2\job\AddService;
use common\service\v2\job\DetailService;
use common\service\v2\job\EditInitService;
use common\service\v2\job\EditService;
use common\service\v2\job\TemplateInitAddService;
use common\service\v2\job\TemplateListService;
use queue\Producer;
use Yii;
use yii\base\Exception;
use yii\console\Response;

class JobV101Controller extends BaseAdminController
{
    /**
     * 新增、添加纯职位
     * @return Response|\yii\web\Response
     */
    public function actionAdd()
    {
        $tran = Yii::$app->db->beginTransaction();
        try {
            (new AddService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $tran->commit();

            return $this->success();
        } catch (\Exception $e) {
            $tran->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位模板列表
     * @return Response|\yii\web\Response
     */
    public function actionTemplateList()
    {
        try {
            return $this->success((new TemplateListService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位模板列表
     * @return Response|\yii\web\Response
     */
    public function actionTemplateInitAdd()
    {
        try {
            return $this->success((new TemplateInitAddService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量导入新增职位
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAddBatchImport()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            $transaction->commit();
            (new AddBatchImportService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();

            return $this->success(['msgTxt' => '成功批量导入数据']);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 编辑初始化职位
     * @return Response|\yii\web\Response
     */
    public function actionEditInit()
    {
        $tran = Yii::$app->db->beginTransaction();
        try {
            (new EditInitService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $tran->commit();

            return $this->success();
        } catch (\Exception $e) {
            $tran->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 编辑职位
     * 编辑纯职位、公告下职位
     * @return Response|\yii\web\Response
     */
    public function actionEdit()
    {
        $tran = Yii::$app->db->beginTransaction();
        try {
            (new EditService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $tran->commit();

            return $this->success();
        } catch (\Exception $e) {
            $tran->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位详情
     * @return Response|\yii\web\Response
     */
    public function actionDetail()
    {
        $tran = Yii::$app->db->beginTransaction();
        try {
            (new DetailService())->setPlatform(CommonService::PLATFORM_ADMIN)
                ->run();
            $tran->commit();

            return $this->success();
        } catch (\Exception $e) {
            $tran->rollBack();

            return $this->fail($e->getMessage());
        }
    }
}