<?php

namespace admin\controllers;

use admin\models\NginxLog;
use admin\models\Seo;
use admin\models\SeoSitemapFile;
use common\base\models\BaseAdminDownloadTask;
use common\base\models\BaseBaiduZzPushLog;
use common\helpers\ArrayHelper;
use common\libs\IndexNow;
use common\service\downloadTask\DownLoadTaskApplication;
use Yii;

class SeoController extends BaseAdminController
{

    // 这里获取一些参数,用于帮助前端展示
    public function actionLoadParams()
    {
        return $this->success([
            'wayTypeList' => ArrayHelper::obj2Arr(Seo::getWayTypeList()),
            'urlTypeList' => ArrayHelper::obj2Arr(Seo::getUrlTypeList()),
        ]);
    }

    public function actionLoadBaiduZzPushList()
    {
        return $this->success(Seo::getBaiduZzPushList(Yii::$app->request->get()));
    }

    public function actionLoadBaiduZzPushDetail()
    {
        $id = Yii::$app->request->get('id');
        if (!$id) {
            return $this->fail();
        }

        return $this->success(Seo::getBaiduZzPushDetail($id));
    }

    public function actionLoadBaiduZzPushDaily()
    {
        $type = Yii::$app->request->get('type', BaseBaiduZzPushLog::TYPE_BAIDU);

        return $this->success(Seo::getBaiduZzPushDaily($type));
    }

    public function actionBaiduZzPush()
    {
        $data    = Yii::$app->request->post();
        $type    = $data['type'];
        $urls    = $data['urls'];
        $urlType = $data['urlType'];

        // urls是以换行符分割的字符串,要变为数组
        $urls = explode("\n", $urls);
        // 去掉空的内容
        $urls = array_filter($urls);
        if ($type == BaseBaiduZzPushLog::TYPE_BAIDU) {
            $site = Seo::getSiteByUrlType($urlType);
            if (!$site) {
                return $this->fail();
            }
        }

        try {
            if ($type == BaseBaiduZzPushLog::TYPE_BAIDU) {
                $rs = Seo::baiduZzPush($urls, $site, Seo::WAY_TYPE_MANUAL);
            } else {
                $rs = Seo::indexNowPush($urls, $urlType, Seo::WAY_TYPE_MANUAL);
            }

            return $this->success($rs);
        } catch (\Exception $e) {
            $message = $e->getMessage();
            if ($message == 'over quota') {
                return $this->fail('百度推送额度已满');
            }

            return $this->fail($e->getMessage());
        }
    }

    public function actionSearchParams()
    {
        return $this->success([
            'typeList'         => ArrayHelper::obj2Arr(SeoSitemapFile::TYPE_LIST),
            'platformTypeList' => ArrayHelper::obj2Arr(SeoSitemapFile::PLATFORM_TYPE_LIST),
        ]);
    }

    public function actionXmlFileList()
    {
        $params = Yii::$app->request->get();

        return $this->success(SeoSitemapFile::getXmlFileList($params));
    }

    public function actionXmlFileEdit()
    {
        $id = Yii::$app->request->get('id');
        if (!$id) {
            return $this->fail();
        }

        try {
            return $this->success(SeoSitemapFile::getXmlFileEdit($id));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionXmlFileSave()
    {
        $id      = Yii::$app->request->post('id');
        $content = Yii::$app->request->post('content');
        if (!$id || !$content) {
            return $this->fail();
        }

        try {
            return $this->success(SeoSitemapFile::setXmlFileSave($id, $content), '保存成功');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionXmlFileDelete()
    {
        $id = Yii::$app->request->post('id');
        if (!$id) {
            return $this->fail();
        }

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $data = SeoSitemapFile::deleteXmlFile($id);

            $transaction->commit();

            return $this->success($data, '删除成功');
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    public function actionGetNginxLogList()
    {
        $model = new NginxLog();

        return $this->success([
            'list' => $model->getAllList(),
        ]);
    }

    public function actionDownloadNginxLog()
    {
        $data = Yii::$app->request->post();

        try {
            $adminId = Yii::$app->user->id;
            $app     = DownLoadTaskApplication::getInstance();
            $app->createAdmin($adminId, BaseAdminDownloadTask::TYPE_NGINX_LOG, $data);

            return $this->success('数据开始导出,成功下载后会在企业微信通知,请后续留意');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}