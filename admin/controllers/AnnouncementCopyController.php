<?php

namespace admin\controllers;

use admin\models\Announcement;
use admin\models\Article;
use admin\models\ArticleAttribute;
use admin\models\CategoryJob;
use admin\models\Company;
use admin\models\Dictionary;
use admin\models\HomeColumn;
use admin\models\Job;
use admin\models\JobTemp;
use admin\models\Major;
use admin\models\UploadForm;
use common\base\models\BaseJobTemp;
use common\helpers\ArrayHelper;
use common\helpers\FormatConverter;
use common\libs\ColumnAuto\AnnouncementAutoClassify;
use common\libs\JobBatchImport;
use common\service\announcement\AddJobTempService;
use common\service\announcement\AddService;
use common\service\announcement\AuditHandleService;
use common\service\announcement\AuditService;
use common\service\announcement\BaseService;
use common\service\announcement\BatchDeleteService;
use common\service\announcement\ChangeAttributeService;
use common\service\announcement\CopyService;
use common\service\announcement\DeleteJobService;
use common\service\announcement\DeleteService;
use common\service\announcement\HiddenJobService;
use common\service\announcement\HiddenService;
use common\service\announcement\OfflineJobService;
use common\service\announcement\OfflineService;
use common\service\announcement\OnlineJobService;
use common\service\announcement\OnlineService;
use common\service\announcement\RefreshService;
use common\service\announcement\RemoveService;
use common\service\announcement\ShowJobService;
use common\service\announcement\ShowService;
use Yii;
use yii\base\Exception;
use yii\console\Response;

class AnnouncementCopyController extends BaseAdminController
{
    /**
     * 获取公告发布参数
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetAddParams()
    {
        $columnList = HomeColumn::getAllList();

        return $this->success([
            // 栏目列表
            'columnList'                => $columnList,
            // 副栏目
            'subColumnList'             => $columnList,
            // 合作单位类型
            'companyCooperationList'    => ArrayHelper::obj2Arr(Company::COOPERATIVE_UNIT_LIST),
            // 文档属性
            'documentList'              => ArrayHelper::obj2Arr(ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST),
            // 非海外属性文档
            'notOverseasAttributeList'  => ArrayHelper::obj2Arr(ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_NOT_HAW_WAI_LIST),
            //海外属性文档
            'overseasAttributeList'     => ArrayHelper::obj2Arr(ArticleAttribute::ANNOUNCEMENT_ATTRIBUTE_ABROAD_LIST),
            // 应聘方式
            'applyList'                 => ArrayHelper::obj2Arr(Dictionary::getSignUpList()),
            // 特色标签
            'tagList'                   => ArrayHelper::obj2Arr(Article::ATTRIBUTE_TAG_LIST),
            // 推荐位
            'recommendList'             => ArrayHelper::obj2Arr(Article::ATTRIBUTE_RECOMMEND_LIST),
            // 页面模版
            'templateList'              => ArrayHelper::obj2Arr(Announcement::TEMPLATE_LIST),
            // 背景图类型
            'backgroundImgFileTypeList' => ArrayHelper::obj2Arr(Announcement::BACKGROUND_IMG_FILE_TYPE_LIST),
        ]);
    }

    /**
     * 获取公告列表检索参数
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetSearchParams()
    {
        return $this->success([
            // 栏目列表
            'columnList'               => HomeColumn::getAllList(),
            // 审核状态
            'auditStatusList'          => ArrayHelper::obj2Arr(Announcement::STATUS_AUDIT_LISTS),
            // 职位类型
            'jobTypeList'              => ArrayHelper::objMoreArr(CategoryJob::getCompanyCategoryJobList()),
            // 招聘状态
            'statusRecruitList'        => ArrayHelper::obj2Arr(Article::STATUS_LIST),
            // 需求专业
            'majorList'                => ArrayHelper::objMoreArr(Major::getAllMajorList()),
            // 学历要求
            'educationTypeList'        => ArrayHelper::obj2Arr(Dictionary::getEducationList()),
            // 公告属性
            'attributeDocument'        => ArrayHelper::obj2Arr(ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_LIST),
            //海外公告属性
            'overseasAttributeList'    => ArrayHelper::obj2Arr(ArticleAttribute::ANNOUNCEMENT_ATTRIBUTE_ABROAD_LIST),
            // 非海外属性文档
            'notOverseasAttributeList' => ArrayHelper::obj2Arr(ArticleAttribute::ATTRIBUTE_ANNOUNCEMENT_NOT_HAW_WAI_LIST),
            // 显示状态
            'showStatusList'           => ArrayHelper::obj2Arr(Article::IS_SHOW_LIST),
        ]);
    }

    /**
     * 获取类型单位
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionSearchCompanyList()
    {
        $params = Yii::$app->request->get();

        try {
            if (!$params['type']) {
                throw new Exception('合作类型不能为空');
            }

            return $this->success(Announcement::searchCompanyList($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取所有单位
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetAllCompanyList()
    {
        $params = Yii::$app->request->get();

        return $this->success(Announcement::getAllCompanyList($params));
    }

    /**
     * 识别富文本的内容
     */
    public function actionIdentityEditor()
    {
        $content = Yii::$app->request->post('content');
        try {
            $transaction = Yii::$app->db->beginTransaction();
            $model       = new Announcement();
            $rs          = $model->identityEditor($content);
            $transaction->commit();

            return $this->success($rs);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 检查公告标题唯一性
     * @return Response|\yii\web\Response
     */
    public function actionCheckTitleOnly()
    {
        $params = Yii::$app->request->get();
        if (!$params['title']) {
            return $this->fail('标题不能为空');
        }

        return $this->success(Article::checkTitleOnly($params['title'], $params['id']));
    }

    /**
     * 公告保存/提交
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionSubmitAdd()
    {
        //开辟零时内存
        ini_set('memory_limit', '2048M');

        $data = Yii::$app->request->post();

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $addService = new AddService();

            if ($data['announcementId']) {
                // 编辑发布
                if ($data['submitType'] == $addService::SAVE_TYPE_AUDIT) {
                    $addService->setOperator($adminId, AddService::OPERATOR_TYPE_ADMIN)
                        ->setStaging()
                        ->setEditData($data)
                        ->run();
                } else {
                    $addService->setOperator($adminId, AddService::OPERATOR_TYPE_ADMIN)
                        ->setAudit()
                        ->setEditData($data)
                        ->run();
                }
            } else {
                // 保存发布
                $addService->setOperator($adminId, AddService::OPERATOR_TYPE_ADMIN)
                    ->setStaging($data['submitType'])
                    ->setAddData($data)
                    ->run();
            }

            $transaction->commit();
            if ($data['submitType'] == $addService::SAVE_TYPE_STAGING) {
                return $this->success(['id' => $addService->announcementId ?: 0], '保存成功');
            } else {
                return $this->success();
            }
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 获取公告列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetList()
    {
        $params = Yii::$app->request->get();

        $params['admin'] = \Yii::$app->user->id;

        return $this->success(Announcement::getList($params));
    }

    /**
     * 获取公告列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetSimpleList()
    {
        $params = Yii::$app->request->get();

        $params['admin'] = \Yii::$app->user->id;

        return $this->success(Announcement::getsSimpleList($params));
    }


    //===================公告发布时的临时职位信息与操作 start===================

    /**
     * 临时职位-信息列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionTemporaryJobList()
    {
        $params = Yii::$app->request->get();
        if (!$params['jobTempId']) {
            return $this->fail();
        }

        return $this->success(JobTemp::getJobTempList($params));
    }

    /**
     * 选择职位模版填充编辑数据
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionJobEditData()
    {
        $id = Yii::$app->request->get('id');
        if (!$id) {
            return $this->fail();
        }

        return $this->success(Job::getJobEditData($id));
    }

    /**
     * 获取职位名称列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionJobNameList()
    {
        $params = Yii::$app->request->get();

        return $this->success(Job::getJobNameList($params));
    }

    /**
     * 临时职位-复制添加
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionTemporaryJobCopy()
    {
        $params = Yii::$app->request->post();
        if (!$params['id']) {
            return $this->fail();
        }

        $adminId = Yii::$app->user->id;
        try {
            $addJobTempService = new AddJobTempService();
            $addJobTempService->setOperator($adminId, AddService::OPERATOR_TYPE_ADMIN)
                ->setCopy($params)
                ->run();

            return $this->success(['jobTempData' => $addJobTempService->jobTempData]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 临时职位-编辑
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionTemporaryJobEdit()
    {
        $params = Yii::$app->request->get();
        if (!$params['id']) {
            return $this->fail();
        }

        try {
            if ($params['isTemp'] == BaseJobTemp::IS_TEMP_YES) {
                $data = JobTemp::getTemporaryJobEdit($params);
            } elseif ($params['isTemp'] == BaseJobTemp::IS_TEMP_NO) {
                $data = Job::getEdit($params);
            } else {
                throw new Exception('非法请求');
            }

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 临时职位-添加/编辑保存
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionTemporaryJobSave()
    {
        $data = Yii::$app->request->post();
        $data = FormatConverter::convertHump($data);

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $addJobTempService = new AddJobTempService();

            $addJobTempService->setOperator($adminId, AddService::OPERATOR_TYPE_ADMIN)
                ->setSingle()
                ->setData($data)
                ->run();

            $responseData = $addJobTempService->jobTempData;

            $transaction->commit();

            return $this->success(['jobTempData' => $responseData]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 临时职位-删除
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionTemporaryJobDelete()
    {
        $params = Yii::$app->request->post();
        if (!$params['id']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $deleteJobService = new DeleteJobService();

            $deleteJobService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                ->setDelete()
                ->setData($params)
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 上传临时Excel文件
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionUploadExcel()
    {
        $model = new UploadForm();
        $model->setUploadType('file');
        $transaction = \Yii::$app->db->beginTransaction();

        try {
            $path = 'job_temp_excel';
            $data = $model->temporaryUploadExcel('file', $path);

            $transaction->commit();

            return $this->success([
                'url'     => $data['path'],
                'fullUrl' => \Yii::$app->params['homeUrl'] . '/' . $data['path'],
            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->result($e->getMessage());
        }
    }

    /**
     * 职位批量导入
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionJobTemporaryBatchImport()
    {
        $request = Yii::$app->request->post();

        $filePath = $request['filePath'];
        if (!$filePath || $request['companyId'] <= 0) {
            return $this->fail('参数缺失');
        }
        $transaction = \Yii::$app->db->beginTransaction();

        try {
            $data = JobTemp::jobTemporaryBatchImport($request, JobBatchImport::PLATFORM_ADMIN);

            $transaction->commit();

            return $this->success($data, '成功批量导入数据');
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    //===================公告发布时的临时职位信息与操作 end===================

    /**
     * 公告详情
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDetail()
    {
        $params = Yii::$app->request->get();
        if (!$params['id'] || !$params['type']) {
            return $this->fail('参数缺失');
        }

        try {
            return $this->success(Announcement::getDetail($params));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 审核详情
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAuditDetail()
    {
        $params = Yii::$app->request->get();
        if (!$params['id']) {
            return $this->fail();
        }

        try {
            return $this->success(Announcement::getAuditDetail($params));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 职位审核详情列表
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionJobAuditDetailList()
    {
        $params = Yii::$app->request->get();
        if (!$params['id']) {
            return $this->fail('公告id参数缺失');
        }

        return $this->success(Job::jobAuditDetailList($params));
    }

    /**
     * 公告操作-审核(通过、拒绝、审核拒绝并编辑)
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAuditHandle()
    {
        $params  = Yii::$app->request->post();
        $adminId = Yii::$app->user->id;

        if (!$params['id']) {
            return $this->fail('参数缺失');
        }
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $auditHandleService = new AuditHandleService();
            if ($params['auditStatus'] == $auditHandleService::TYPE_AUDIT_PASS) {
                $auditHandleService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setAuditPass()
                    ->setData($params)
                    ->run();
            } elseif ($params['auditStatus'] == $auditHandleService::TYPE_AUDIT_REFUSE) {
                $auditHandleService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setAuditRefuse()
                    ->setData($params)
                    ->run();
            } else {
                throw new Exception('审核类型非法');
            }

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-编辑（非合作/合作）
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionEdit()
    {
        $id = Yii::$app->request->get('id');
        if (!$id) {
            return $this->fail();
        }

        try {
            return $this->success(Announcement::getEditInfo($id));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-再发布/下线
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionOnlineOffline()
    {
        $params = Yii::$app->request->post();
        if (!$params['id']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $onlineService  = new OnlineService();
            $offlineService = new OfflineService();
            if ($params['actionType'] == 1) {
                // 操作再发布
                $onlineService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setOnline()
                    ->setData($params)
                    ->run();
                $res = Announcement::majorMessageNotice($params['id']);
                if ($res['res'] === false) {
                    $transaction->commit();

                    return $this->success($res['msg']);
                }
            } elseif ($params['actionType'] == 2) {
                // 操作下线
                $offlineService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setOffline()
                    ->setData($params)
                    ->run();
            } else {
                throw new Exception('非法操作');
            }

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告批量操作-再发布/下线
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionBatchOnlineOffline()
    {
        $params = Yii::$app->request->post();
        if (!$params['ids']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $ids = explode(',', $params['ids']);

            $onlineService  = new OnlineService();
            $offlineService = new OfflineService();
            $msg            = '';
            if ($params['actionType'] == 1) {
                // 操作再发布
                foreach ($ids as $id) {
                    $params = [
                        'id' => $id,
                    ];
                    $onlineService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                        ->setOnline()
                        ->setData($params)
                        ->run();
                    $res = Announcement::majorMessageNotice($id);
                    if ($res['res'] === false) {
                        $msg .= $res['msg'] . "。";
                    }
                }
                if (!empty($msg)) {
                    $transaction->commit();

                    return $this->success($msg);
                }
            } elseif ($params['actionType'] == 2) {
                // 操作下线
                foreach ($ids as $id) {
                    $params = [
                        'id'     => $id,
                        'reason' => $params['reason'],
                    ];
                    $offlineService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                        ->setOffline()
                        ->setData($params)
                        ->run();
                }
            } else {
                throw new Exception('非法操作');
            }

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告职位操作-再发布/下线
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionOnlineOfflineJob()
    {
        $params = Yii::$app->request->post();
        if (!$params['id'] || !$params['actionType']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $onlineJobService  = new OnlineJobService();
            $offlineJobService = new OfflineJobService();
            if ($params['actionType'] == 1) {
                // 操作再发布
                $onlineJobService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setOnline()
                    ->setData($params)
                    ->run();
            } elseif ($params['actionType'] == 2) {
                // 操作下线
                $offlineJobService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setOffline()
                    ->setData($params)
                    ->run();
            } else {
                throw new Exception('非法操作');
            }

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告职位批量操作-再发布/下线
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionBatchOnlineJob()
    {
        $params = Yii::$app->request->post();
        if (!$params['ids'] || !$params['actionType']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $ids = explode(',', $params['ids']);

            $onlineJobService  = new OnlineJobService();
            $offlineJobService = new OfflineJobService();
            if ($params['actionType'] == 1) {
                // 操作再发布
                foreach ($ids as $id) {
                    $params = [
                        'id' => $id,
                    ];
                    $onlineJobService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                        ->setOnline()
                        ->setData($params)
                        ->run();
                }
            } elseif ($params['actionType'] == 2) {
                // 操作下线
                foreach ($ids as $id) {
                    $params = [
                        'id' => $id,
                    ];
                    $offlineJobService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                        ->setOffline()
                        ->setData($params)
                        ->run();
                }
            } else {
                throw new Exception('非法操作');
            }
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-隐藏/显示
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionHiddenShow()
    {
        $params = Yii::$app->request->post();
        if (!$params['id'] || !$params['actionType']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $hiddenService = new HiddenService();
            $showService   = new ShowService();

            if ($params['actionType'] == 1) {
                // 操作显示
                $showService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setShow()
                    ->setData($params)
                    ->run();
            } elseif ($params['actionType'] == 2) {
                // 操作隐藏
                $hiddenService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setHidden()
                    ->setData($params)
                    ->run();
            } else {
                throw new Exception('非法操作');
            }
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告批量操作-隐藏/显示
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionBatchHiddenShow()
    {
        $params = Yii::$app->request->post();
        if (!$params['ids'] || !$params['actionType']) {
            return $this->fail();
        }

        $adminId = Yii::$app->user->id;

        $ids = explode(',', $params['ids']);

        $hiddenService = new HiddenService();
        $showService   = new ShowService();
        $errorContent  = '';
        foreach ($ids as $id) {
            $transaction = Yii::$app->db->beginTransaction();
            try {
                if ($params['actionType'] == 1) {
                    // 操作显示
                    $showService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                        ->setShow()
                        ->setData(['id' => $id])
                        ->run();
                } elseif ($params['actionType'] == 2) {
                    // 操作隐藏
                    $hiddenService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                        ->setHidden()
                        ->setData(['id' => $id])
                        ->run();
                } else {
                    throw new Exception('非法操作');
                }
                $transaction->commit();
            } catch (\Exception $e) {
                if ($params['actionType'] == 1) {
                    $action = '显示';
                } elseif ($params['actionType'] == 2) {
                    $action = '隐藏';
                }
                $errorContent .= '公告id：' . $id . ' 执行' . $action . '失败，原因：' . $e->getMessage() . "<br/>";

                $transaction->rollBack();
            }
        }
        if (!empty($errorContent)) {
            return $this->success(['content' => $errorContent], '');
        } else {
            return $this->success();
        }
    }

    /**
     * 公告职位操作-隐藏/显示
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionHiddenShowJob()
    {
        $params = Yii::$app->request->post();
        if (!$params['id'] || !$params['actionType']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $hiddenJobService = new HiddenJobService();
            $showJobService   = new ShowJobService();
            if ($params['actionType'] == 1) {
                // 操作显示
                $showJobService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setShow()
                    ->setData($params['id'])
                    ->run();
            } elseif ($params['actionType'] == 2) {
                // 操作隐藏
                $hiddenJobService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setHidden()
                    ->setData($params['id'])
                    ->run();
            } else {
                throw new Exception('非法操作');
            }

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-刷新
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionRefresh()
    {
        $params = Yii::$app->request->post();
        if (!$params['id']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $refreshService = new RefreshService();
            $refreshService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                ->setRefresh()
                ->setData($params)
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告批量操作-刷新
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionBatchRefresh()
    {
        $params = Yii::$app->request->post();
        if (!$params['ids']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $ids = explode(',', $params['ids']);

            $refreshService = new RefreshService();
            foreach ($ids as $id) {
                $params = [
                    'id' => $id,
                ];
                $refreshService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setRefresh()
                    ->setData($params)
                    ->run();
            }
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告职位操作-刷新
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionRefreshJob()
    {
        $params = Yii::$app->request->post();
        if (!$params['id']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $refreshService = new RefreshService();
            $refreshService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                ->setJobRefresh()
                ->setJobData($params)
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告职位批量操作-刷新
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionBatchRefreshJob()
    {
        $params = Yii::$app->request->post();
        if (!$params['ids']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $ids            = explode(',', $params['ids']);
            $refreshService = new RefreshService();
            foreach ($ids as $id) {
                $params = [
                    'id' => $id,
                ];
                $refreshService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setJobRefresh()
                    ->setJobData($params)
                    ->run();
            }
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告职位列表（非合作单位）
     */
    public function actionJobList()
    {
        $request = Yii::$app->request->get();

        try {
            $list = Job::getAnnouncementJobListAdmin(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告职位列表（简版）
     */
    public function actionSimpleJobList()
    {
        $request = Yii::$app->request->get();

        try {
            $list = Job::getAnnouncementSimpleJobList(FormatConverter::convertHump($request));

            return $this->success($list);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 审核列表（非合作单位）
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionNoncooperationAuditList()
    {
        $params = Yii::$app->request->get();

        try {
            return $this->success(Announcement::getNoncooperationAuditList($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量编辑属性
     * @return Response|\yii\web\Response
     */
    public function actionChangeAttribute()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $request   = Yii::$app->request->post();
            $checkData = [
                'ids',
                'attribute',
            ];
            foreach ($checkData as $list) {
                if (!isset($request[$list])) {
                    throw new Exception('参数不能为空');
                }
            }
            $ids     = explode(',', $request['ids']);
            $adminId = Yii::$app->user->id;

            foreach ($ids as $id) {
                $changeAttributeService = new ChangeAttributeService();
                $params                 = [
                    'announcementId' => $id,
                    'attribute'      => $request['attribute'],
                ];
                $changeAttributeService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setData($params)
                    ->run();
            }

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量复制
     * @return Response|\yii\web\Response
     */
    public function actionBatchCopy()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $request   = Yii::$app->request->post();
            $checkData = [
                'ids',
                'homeColumnId',
            ];
            foreach ($checkData as $list) {
                if (!isset($request[$list])) {
                    throw new Exception('参数不能为空');
                }
            }
            $ids     = explode(',', $request['ids']);
            $adminId = Yii::$app->user->id;

            foreach ($ids as $id) {
                for ($i = 0; $i < $request['amount']; $i++) {
                    $copyService = new CopyService();
                    $params      = [
                        'announcementId' => $id,
                        'homeColumnId'   => $request['homeColumnId'],
                    ];
                    $copyService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                        ->setData($params)
                        ->run();
                }
            }

            $list = Announcement::getTemplateAmount($ids, $request['homeColumnId']);
            $transaction->commit();

            return $this->success($list);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量移动
     * @return Response|\yii\web\Response
     */
    public function actionBatchMove()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $request   = Yii::$app->request->post();
            $checkData = [
                'ids',
                'homeColumnId',
            ];
            foreach ($checkData as $list) {
                if (!isset($request[$list])) {
                    throw new Exception('参数不能为空');
                }
            }
            $ids     = explode(',', $request['ids']);
            $adminId = Yii::$app->user->id;

            foreach ($ids as $id) {
                $copyService = new RemoveService();
                $params      = [
                    'announcementId' => $id,
                    'homeColumnId'   => $request['homeColumnId'],
                ];
                $copyService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setData($params)
                    ->run();
            }

            $list = Announcement::getTemplateAmount($ids, $request['homeColumnId']);
            $transaction->commit();

            return $this->success($list);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量审核
     * @return Response|\yii\web\Response
     */
    public function actionBatchAudit()
    {
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $request   = Yii::$app->request->post();
            $checkData = [
                'ids',
                'status',
            ];
            foreach ($checkData as $list) {
                if (!isset($request[$list])) {
                    throw new Exception('参数不能为空');
                }
            }
            $ids     = explode(',', $request['ids']);
            $adminId = Yii::$app->user->id;

            foreach ($ids as $id) {
                $auditService = new AuditService();
                $params       = [
                    'announcementId' => $id,
                    'status'         => $request['status'],
                    'opinion'        => $request['opinion'],
                ];

                $auditService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setData($params)
                    ->run();
            }
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 审核列表（合作单位）
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionAuditList()
    {
        $params = Yii::$app->request->get();

        try {
            return $this->success(Announcement::getAuditList($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 站外投递列表(非合作单位)
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionOffJobApplyList()
    {
        $params = Yii::$app->request->get();

        return $this->success(Job::offJobApplyList(FormatConverter::convertHump($params)));
    }

    /**
     * 公告操作日志
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetAnnouncementHandleLog()
    {
        $params = Yii::$app->request->get();

        try {
            if (!$params['id']) {
                throw new Exception('公告id不能为空');
            }

            return $this->success(Announcement::getAnnouncementHandleLog($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 业务--站内投递(收到的简历变更站内投递)
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetAnnouncementJobApplyList()
    {
        $params = Yii::$app->request->get();

        try {
            if (!$params['id']) {
                throw new Exception('公告id不能为空');
            }

            return $this->success(Announcement::getAnnouncementJobApplyList(FormatConverter::convertHump($params)));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 业务--站外投递
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetOutsideApplyList()
    {
        $params = Yii::$app->request->get();

        try {
            if (!$params['id']) {
                throw new Exception('公告id不能为空');
            }

            return $this->success(Announcement::getOutsideApplyList(FormatConverter::convertHump($params)));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 业务--面试邀约
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetAnnouncementInterviewList()
    {
        $params = Yii::$app->request->get();

        try {
            if (!$params['id']) {
                throw new Exception('公告id不能为空');
            }

            return $this->success(Announcement::getAnnouncementInterviewList(FormatConverter::convertHump($params)));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 业务--下载的简历
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetResumeDownloadLog()
    {
        $params = Yii::$app->request->get();

        try {
            if (!$params['id']) {
                throw new Exception('公告id不能为空');
            }

            return $this->success(Announcement::getResumeDownloadLog(FormatConverter::convertHump($params)));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 业务--公告操作日志下操作类型
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetHandleTypeList()
    {
        try {
            return $this->success(Announcement::getHandleTypeList());
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告文档属性数据
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionAttributeData()
    {
        $params = Yii::$app->request->get();

        try {
            if (!$params['articleId']) {
                throw new Exception('参数缺失');
            }

            return $this->success(ArticleAttribute::getAttributeData($params['articleId']));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-文档属性排序时间刷新
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionRefreshSortTime()
    {
        $params = Yii::$app->request->post();

        $transaction = Yii::$app->db->beginTransaction();
        try {
            if (!$params['articleId'] && !$params['attributeId']) {
                throw new Exception('参数缺失');
            }

            $transaction->commit();

            return $this->success(ArticleAttribute::refreshSortTime($params['articleId'], $params['attributeId']),
                '文档属性刷新成功');
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告操作-删除
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionDelete()
    {
        $params = Yii::$app->request->post();
        if (!$params['id']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $deleteService = new DeleteService();
            $deleteService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                ->setDelete()
                ->setDeleteData($params)
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 批量删除
     * @return Response|\yii\web\Response
     */
    public function actionBatchDelete()
    {
        $transaction = Yii::$app->db->beginTransaction();

        try {
            $request   = Yii::$app->request->post();
            $checkData = [
                'ids',
            ];
            foreach ($checkData as $list) {
                if (!isset($request[$list])) {
                    throw new Exception('参数不能为空');
                }
            }
            $ids     = explode(',', $request['ids']);
            $adminId = Yii::$app->user->id;

            foreach ($ids as $id) {
                $deleteService = new BatchDeleteService();
                $params        = [
                    'id' => $id,
                ];
                $deleteService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setData($params)
                    ->run();
            }
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 回收站列表
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionRecycleList()
    {
        $params = Yii::$app->request->get();

        return $this->success(Announcement::getRecycleList($params));
    }

    /**
     * 回收站操作-还原
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionRecycleReduction()
    {
        $params = Yii::$app->request->post();
        if (!$params['id']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $deleteService = new DeleteService();
            $deleteService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                ->setReduction()
                ->setReductionData($params)
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 回收站操作-批量还原
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionRecycleBatchReduction()
    {
        $params = Yii::$app->request->post();
        if (!$params['ids']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $ids = explode(',', $params['ids']);

            $deleteService = new DeleteService();
            foreach ($ids as $id) {
                $params = [
                    'id' => $id,
                ];
                $deleteService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setReduction()
                    ->setReductionData($params)
                    ->run();
            }
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 回收站职位列表
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionRecycleJobList()
    {
        $params = Yii::$app->request->get();

        return $this->success(Job::getRecycleJobList($params));
    }

    /**
     * 回收站职位操作-还原
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionRecycleJobReduction()
    {
        $params = Yii::$app->request->post();
        if (!$params['id']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $deleteService = new DeleteJobService();
            $deleteService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                ->setReduction()
                ->setReductionData($params)
                ->run();

            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 回收站职位操作-批量还原
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionRecycleJobBatchReduction()
    {
        $params = Yii::$app->request->post();
        if (!$params['ids']) {
            return $this->fail();
        }

        $adminId     = Yii::$app->user->id;
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $ids = explode(',', $params['ids']);

            $deleteService = new DeleteJobService();
            foreach ($ids as $id) {
                $params = [
                    'id' => $id,
                ];
                $deleteService->setOperator($adminId, BaseService::OPERATOR_TYPE_ADMIN)
                    ->setReduction()
                    ->setReductionData($params)
                    ->run();
            }
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 栏目自动归类
     * @return Response|\yii\web\Response
     */
    public function actionAutoClassify()
    {
        $id = Yii::$app->request->post('id');

        $announcement = Announcement::findOne($id);
        if (!$announcement) {
            return $this->fail('公告不存在');
        }
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $model = new AnnouncementAutoClassify($id);
            $model->run();
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 公告修改单位主页排序值
     * @return void
     */
    public function actionChangeHomeSort()
    {
        $id = Yii::$app->request->post('id');

        $announcement = Announcement::findOne($id);
        if (!$announcement) {
            return $this->fail('公告不存在');
        }

        try {
            $announcement->home_sort = Yii::$app->request->post('homeSort');
            if (!$announcement->save()) {
                throw new Exception($announcement->getFirstErrorsMessage());
            }

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}