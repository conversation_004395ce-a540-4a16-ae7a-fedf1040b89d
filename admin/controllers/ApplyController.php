<?php
/**
 * 投递逻辑
 * create user：伍彦川
 * create time：2025/5/12 17:38
 */
namespace admin\controllers;

use common\components\MessageException;
use common\service\v2\job\ApplyOffSiteService;
use common\service\v2\job\ApplyService;
use common\service\v2\job\BaseApplyService;
use yii\base\Exception;

class ApplyController extends BaseAdminController
{
    /**
     * 获取投递初始信息
     */
    public function actionGetApplyInit()
    {
        $params = \Yii::$app->request->get();

        try {
            if (!$params['jobId'] && !$params['announcementId']) {
                throw new MessageException('必传参数不能为空');
            }

            return $this->success((new BaseApplyService())->getApplyInit($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 站内投递
     */
    public function actionInStationApplyIndex()
    {
        $params = \Yii::$app->request->get();

        try {
            if (!$params['jobId'] && !$params['announcementId']) {
                throw new MessageException('必传参数不能为空');
            }

            return $this->success((new ApplyService())->run($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 站内投递-统计
     */
    public function actionInStationApplyStat()
    {
        $params = \Yii::$app->request->get();

        try {
            if (!$params['jobId'] && !$params['announcementId']) {
                throw new Exception('必传参数不能为空');
            }

            return $this->success((new ApplyService())->runStat($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 站外投递
     */
    public function actionOffSiteApplyIndex()
    {
        $params = \Yii::$app->request->get();

        try {
            if (!$params['jobId'] && !$params['announcementId']) {
                throw new MessageException('必传参数不能为空');
            }

            return $this->success((new ApplyOffSiteService())->run($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 站外投递-统计
     */
    public function actionOffSiteApplyStat()
    {
        $params = \Yii::$app->request->get();

        try {
            if (!$params['jobId'] && !$params['announcementId']) {
                throw new Exception('必传参数不能为空');
            }

            return $this->success((new ApplyOffSiteService())->runStat($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 投递相关--面试邀约
     */
    public function actionInterviewIndex()
    {
        $params = \Yii::$app->request->get();

        try {
            if (!$params['announcementId'] && !$params['jobId']) {
                throw new Exception('关键id不能为空');
            }

            return $this->success((new ApplyOffSiteService())->runInterview($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 投递相关--面试邀约统计
     */
    public function actionInterviewStat()
    {
        $params = \Yii::$app->request->get();

        try {
            if (!$params['announcementId'] && !$params['jobId']) {
                throw new Exception('关键id不能为空');
            }

            return $this->success((new ApplyOffSiteService())->runInterviewStat($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 投递相关--面试邀约详情
     */
    public function actionInterviewDetail()
    {
        $params = \Yii::$app->request->get();

        try {
            if (!$params['id']) {
                throw new Exception('面试邀约id不能为空');
            }

            return $this->success((new ApplyOffSiteService())->getInterviewDetail($params['id']));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 投递相关--下载的简历
     */
    public function actionResumeDownloadLogIndex()
    {
        $params = \Yii::$app->request->get();

        try {
            if (!$params['announcementId'] && !$params['jobId']) {
                throw new Exception('关键id不能为空');
            }

            return $this->success((new BaseApplyService())->getResumeDownloadLogIndex($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 投递相关--下载的简历
     */
    public function actionResumeDownloadLogStat()
    {
        $params = \Yii::$app->request->get();

        try {
            if (!$params['announcementId'] && !$params['jobId']) {
                throw new Exception('关键id不能为空');
            }

            return $this->success((new BaseApplyService())->getResumeDownloadLogStat($params));
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}