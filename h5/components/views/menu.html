<?php
use Yii;
?>

<div id="navigationContainer" class="navigation-container">
    <header class="navigation-header">
        <?=@h5\components\HomeNavItemWidget::Widget()?>
        <!-- 未登录 -->
        <?php if($isLogin):?>
        <!-- 已登录 -->
             <a href="/person" class="navigation-avatar">
                <img src="<?=$avatar?>" alt="">
                <?php if($isVip):?>
                <div class="vip-logo"></div>
                <?php endif;?>
            </a>
        <?php else:?>

            <a href="javascript:;" class="navigation-login">登录丨注册</a>
        <?php endif;?>

        <a href="javascript:;" class="navigation-close"></a>
    </header>

    <section class="navigation-main">
        <ul class="navigation-nav">
            <?php foreach($allAnnouncementColumn as $k=>$column):?>
            <li><span><?=$column['name']?></span></li>
            <?php endforeach;?>
            <li><span>资讯</span></li>
            <li><span>专题</span></li>
            <li><span>求职神器</span></li>
            <li><span>关于我们</span></li>
        </ul>

        <div class="navigation-box">
            <?php foreach($allAnnouncementColumn as $k=>$column):?>
                <?php if($column['type'] == 'column'):?>
                    <ul class="navigation-panel">
                        <li class="panel-title"><span><?=$column['name']?></span></li>
                        <li class="panel-links"><a href="<?=$column['url']?>" <?php if($selectId == $column['id']):?>class="is-active"<?php endif?>>栏目首页</a></li>
                        <?php foreach($column['list'] as $secondColumn):?>
                            <li class="panel-links">
                                <a href="<?=$secondColumn['url']?>" <?php if($selectId == $secondColumn['id']):?>class="is-active"<?php endif?>><?= $secondColumn['name']?></a>
                            </li>
                        <?php endforeach;?>

                    </ul>
                <?php elseif($column['type'] == 'area'):?>
                    <ul class="navigation-panel is-area">
                    <li class="panel-title"><span>热门地区</span></li>
                    <li class="panel-links">
                        <?php foreach($column['hotAreaColumnList'] as $item):?>
                        <a href="<?=$item['url']?>" <?php if($selectId == $item['id']):?>class="is-active"<?php endif?>><?=$item['name']?></a>
                        <?php endforeach?>
                    </li>

                    <li class="panel-title"><span>选择省份</span></li>
                    <li class="panel-links">
                        <?php foreach($column['allProvinceColumnList'] as $item):?>
                        <a href="<?=$item['url']?>" <?php if($selectId == $item['id']):?>class="is-active"<?php endif?>><?=$item['name']?></a>
                        <?php endforeach?>
                    </li>

                    <li class="panel-title"><span>选择城市</span></li>
                    <?php foreach($column['allCityColumnList'] as $item):?>
                        <li class="panel-subtitle"><span> <?=$item['label']?></span></li>
                        <li class="panel-links">
                            <?php foreach($item['list'] as $city):?>
                            <a href="<?=$city['url']?>" class="pane-item <?php if($selectId == $city['id']):?>is-active<?php endif?>">
                                <?=$city['name']?>
                            </a>
                            <?php endforeach;?>
                        </li>
                    <?php endforeach?>
                </ul>
                <?php elseif($column['type'] == 'major'):?>
                    <ul class="navigation-panel is-major">
                        <?php foreach($column['allMajorColumn'] as $k=>$item):?>
                            <li class="panel-title"><span> <?=$item['name']?></span></li>
                                <li class="panel-links">
                                    <?php foreach($item['list'] as $major):?>
                                    <a href="<?=$major['url']?>" <?php if($selectId == $major['id']):?>class="is-active"<?php endif?>><?=$major['name']?></a>
                                    <?php endforeach?>
                                </li>
                        <?php endforeach?>
                    </ul>
                <?php endif;?>
            <?php endforeach;?>

            <ul class="navigation-panel">
                <li class="panel-title"><span>资讯</span></li>
                <li class="panel-links">
                    <a href="<?=$moduleUrl['newsUrl']?>" <?php if($selectId == 10):?>class="is-active"<?php endif?>>栏目首页</a>
                    <?php foreach($newsColumn as $news):?>
                    <a href="<?=$news['url']?>" <?php if($selectId == $news['id']):?>class="is-active"<?php endif?>><?=$news['name']?></a>
                    <?php endforeach?>
                </li>
            </ul>

            <ul class="navigation-panel">
                <li class="panel-title"><span>专题</span></li>
                <li class="panel-links">
                    <a href="<?=$moduleUrl['specialSubjectUrl']?>" >栏目首页</a>
                    <?php foreach($specialSubject as $item):?>
                    <a href="<?=$item['url']?>"><?=$item['name']?></a>
                    <?php endforeach?>
                </li>
            </ul>
            <ul class="navigation-panel">
                <li class="panel-title"><span>求职神器</span></li>
                <li class="panel-links">
                    <?php foreach($buyUrl as $item):?>
                    <a href="<?=$item['url']?>"><?=$item['name']?></a>
                    <?php endforeach?>
                </li>
            </ul>
            <ul class="navigation-panel">
                <li class="panel-title"><span>关于我们</span></li>
                <li class="panel-links">
                    <a href="//gaocai.gaoxiaojob.com" >高才科技官网</a>
                </li>
            </ul>





        </div>
    </section>
</div>

<script>
    $(function () {
        var $navigationTrigger = $('#navigationTrigger')
        var $navigationContainer = $('#navigationContainer')
        var $layoutLoginBtn = $('.navigation-login')

        var $navigationClostButton = $navigationContainer.find('.navigation-close')
        var $navigationNav = $navigationContainer.find('.navigation-nav')
        var $navigationBox = $navigationContainer.find('.navigation-box')

        var $navigationNavItem = $navigationNav.find('li')
        var $navigationPanel = $navigationBox.find('.navigation-panel')

        var triggerClick = false

        $navigationTrigger.on('click', function () {
            $navigationContainer.addClass('is-active')
        })

        $navigationClostButton.on('click', function () {
            $navigationContainer.removeClass('is-active')
        })

        $layoutLoginBtn.on('click', function () {
            window.signupPopup.show()
        })

        $navigationNav.on('click', 'li', function () {
            var index = $(this).index()
            var curScrollTop = $navigationBox.scrollTop()
            var boxMarginTop = $navigationBox.offset().top
            var marginTop = $navigationPanel.eq(index).offset().top
            var scrollTop = curScrollTop + marginTop - boxMarginTop

            if ($(this).hasClass('is-active')) return

            triggerClick = true

            $navigationNavItem.removeClass('is-active')
            $(this).addClass('is-active')

            $navigationBox.scrollTo({
                toT: scrollTop,
                callback: function () {
                    setTimeout(function () {
                        triggerClick = false
                    }, 300)
                }
            })
        })

        $navigationBox.on('scroll', function () {
            var indexList = []

            if (triggerClick) return

            $.each($navigationPanel, function (index, item) {
                var height = $(item).height()
                var marginTop = $(item).offset().top - $navigationBox.offset().top

                if (height + marginTop > 0) {
                    indexList.push(index)
                }
            })

            $navigationNavItem.removeClass('is-active')
            $navigationNavItem.eq(indexList[0]).addClass('is-active')
        })

        $navigationBox.on('click', '.is-major > .panel-title', function () {
            $(this).toggleClass('is-reverse')
            $(this).next('.panel-links').toggleClass('is-active')
        })

        // nav 选中效果
        !(function () {
            var $checkLink = $navigationBox.find('.panel-links > a.is-active')

            if ($checkLink.length) {
                var $parentPanel = $checkLink.parents('.navigation-panel')
                var isMajor = $parentPanel.hasClass('is-major')
                var index = $parentPanel.index()

                if (isMajor) {
                    $checkLink.parents('.panel-links').addClass('is-active').prev('.panel-title').addClass('is-reverse')
                }

                $navigationNavItem.eq(index).click()
                return
            }
            $navigationNavItem.eq(0).click()
        })()
    })
</script>