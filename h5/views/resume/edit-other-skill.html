<link rel="stylesheet" href="/static/css/resumeCommon.min.css">

<div class="main-title">
    <div class="tltle-left">其他技能</div>
    <img src="/static/assets/resume/header.png" alt="">
</div>


<form id="form" class="form">
    <div class="weui-cell">
        <div class="weui-cell__hd">
            <label class="weui-label is-require">技能名称</label>
        </div>
        <div class="weui-cell__bd">
            <input class="weui-input" name="name" placeholder="请输入" autocomplete="off" required value="<?=$info['name']?:'' ?>">
            <i class="weui-icon-clear" style="display: none;"></i>
        </div>
    </div>

    <div class="weui-cell weui-cell_select">
        <div class="weui-cell__hd">
            <label class="weui-label">掌握程度</label>
        </div>
        <div class="weui-cell__bd">
            <input class="weui-input weui-input__select" id="degreeType" name="degreeType" type="text"
                    value="<?=$info['degreeType']?:'' ?>" data-values="<?=$info['degreeTypeId']?:'' ?>" readonly placeholder="请选择">
        </div>
    </div>

    <div class="weui-cell">
        <div class="weui-cell__hd">
            <label class="weui-label is-require">技能描述</label>
        </div>
        <div class="weui-cell__bd textarea">
            <div class="textarea-cell">
                        <textarea class="weui-textarea" rows="3" name="description" maxlength="500"
                                    placeholder="请填写至少50字以上的技能描述，更容易受到用人单位的青睐哦~" autocomplete="off"><?=$info['description']?:'' ?></textarea>
            </div>
            <div class="weui-textarea-counter">
                <span>0</span> / <i>500</i>
            </div>
        </div>
    </div>
    <div class="resume-container">
        <?php if(!empty($info['id'])):?>
        <button class="delete-button" id="delete">删除</button>
        <?php endif?>
        <button class="save-button" id="confirm">保存</button>
    </div>
</form>


<script>
    $(function () {
        var id = "<?= $info['id']?>"
        var addApi = '/resume/add-other-skill'
        var editApi = '/resume/edit-other-skill'
        let postApi

        var $textarea = $('.weui-textarea')
        var $degreeType = $('#degreeType')

        var degreeTypeSelector = $degreeType.select({
            closeText: '取消',
            items: [
                <?php foreach($degreeTypeList as $k=>$v):?>
                {title: "<?=$v?>", value: "<?=$k?>"},
                <?php endforeach?>

            ]
        })

        textAreaLimitSubmit($textarea)

        resumeOptimization(function (formEl, formData) {

            if (formData.description.length < 50) {
                toastText('请输入至少50字的技能描述')
                return
            }

            if (id) {
                postApi = editApi
                formData.id = id
            } else {
                postApi = addApi
            }


            httpPost(postApi, formData).then(function () {
                backResumeEdit()
            })
        }, function () {
            var api = '/resume/del-other-skill'
            httpPost(api, {id}).then(function () {
                backResumeEdit()
            })
        }, '.form', '.save-button', '.delete-button')

    })
</script>