var v="Function.prototype.bind called on incompatible ",i=Array.prototype.slice,f=Object.prototype.toString,s="[object Function]",y=function(c){var t=this;if(typeof t!="function"||f.call(t)!==s)throw new TypeError(v+t);for(var o=i.call(arguments,1),n,u=function(){if(this instanceof n){var r=t.apply(this,o.concat(i.call(arguments)));return Object(r)===r?r:this}else return t.apply(c,o.concat(i.call(arguments)))},l=Math.max(0,t.length-o.length),p=[],e=0;e<l;e++)p.push("$"+e);if(n=Function("binder","return function ("+p.join(",")+"){ return binder.apply(this,arguments); }")(u),t.prototype){var a=function(){};a.prototype=t.prototype,n.prototype=new a,a.prototype=null}return n},b=y,g=Function.prototype.bind||b;export{g as f};
