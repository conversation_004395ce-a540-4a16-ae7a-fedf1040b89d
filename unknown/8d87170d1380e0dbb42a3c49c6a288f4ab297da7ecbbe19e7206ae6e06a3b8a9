<?php
namespace common\libs\ColumnAuto;

use admin\models\RuleAnnouncement;
use common\base\BaseActiveRecord;
use common\base\models\BaseAnnouncement;
use common\base\models\BaseAnnouncementAreaRelation;
use common\base\models\BaseAnnouncementBoshihou;
use common\base\models\BaseAnnouncementEducationRelation;
use common\base\models\BaseAnnouncementExtra;
use common\base\models\BaseArticle;
use common\base\models\BaseArticleAttribute;
use common\base\models\BaseArticleColumn;
use common\base\models\BaseCategoryJob;
use common\base\models\BaseCommon;
use common\base\models\BaseCompany;
use common\base\models\BaseCompanyFeatureTag;
use common\base\models\BaseCompanyFeatureTagRelation;
use common\base\models\BaseCompanyStatData;
use common\base\models\BaseDictionary;
use common\base\models\BaseHomeColumn;
use common\base\models\BaseHomePosition;
use common\base\models\BaseJob;
use common\base\models\BaseShowcase;
use common\libs\Cache;
use common\models\AnnouncementAutoClassifyLog;
use common\models\Area;
use common\models\CategoryJob;
use common\models\Major;
use Faker\Provider\Base;
use frontendPc\models\Announcement as FrontendPcAnnouncement;
use h5\models\Announcement as H5Announcement;
use miniApp\models\Announcement as MiniAppAnnouncement;
use queue\MeilisearchJob;
use queue\Producer;
use yii\base\Exception;
use yii\db\conditions\AndCondition;

/**
 * 公告自动规则
 *
 * 触发重新计算
 * 所有计算规则必须是在审核通过后在生效（包含系统自动审核通过或者人工审核通过）
 * 单位的变动（类型/性质）
 * 触发下面所有的公告重新计算
 * 公告的变动不会触发重新计算
 * 特色标签不影响
 *  职位的变动
 *      下线不触发计算
 *      上线不触发计算
 *      删除触发重新计算
 *      新增触发重新计算
 *      变更触发重新计算
 *      隐藏触发重新计算
 *      显示触发重新计算
 *      撤回触发重新计算
 *
 * 文档
 * https://docs.qq.com/sheet/DSkl4TVZzZmZ2d2Fj?tab=BB08J2
 */
class AnnouncementAutoClassify extends AnnouncementRule
{

    private $announcementId;

    /**
     * @var BaseCompany
     */
    private $companyModel;
    /**
     * @var BaseAnnouncement
     */
    private $announcementModel;

    /**
     * @var BaseArticle
     */
    private $articleModel;

    private $allColunmList = [];

    // 保存一些常常用到的属性
    private $companyType           = 0;
    private $companyNature         = 0;
    private $abroadType            = BaseActiveRecord::BASE_TYPE_NO;
    private $isPOSTDOC             = false;
    private $provinceList          = [];
    private $cityList              = [];
    private $educationList         = [];
    private $majorList             = [];
    private $categoryJobLevel2List = [];
    private $categoryJobLevel1List = [];
    private $attribute             = [];

    private $jobList = [];

    private $columnLevle1List = [];
    private $columnLevle2List = [];

    private $remark = '';

    // 其实这些都是为了文案着想
    const MATH_TYPE_MAJOR              = 1;
    const MATH_TYPE_COMPANY_TYPE       = 2;
    const MATH_TYPE_CITY               = 3;
    const MATH_TYPE_PROVINCE           = 4;
    const MATH_TYPE_EDUCATION          = 5;
    const MATH_TYPE_ABROAD             = 6;
    const MATH_TYPE_COMPANY_NATURE     = 7;
    const MATH_TYPE_CATEGORY_JOB       = 8;
    const MATH_TYPE_ABROAD_AREA        = 9;
    const MATH_TYPE_ANNUAL_RECRUITMENT = 10;
    const MATH_TYPE_ABROAD_QIUXIAN     = 11;
    const MATH_TYPE_ABROAD_YINCAI      = 12;

    const MATH_TYPE_LIST = [
        self::MATH_TYPE_MAJOR              => '专业',
        self::MATH_TYPE_COMPANY_TYPE       => '企业类型',
        self::MATH_TYPE_CITY               => '城市',
        self::MATH_TYPE_PROVINCE           => '省份',
        self::MATH_TYPE_EDUCATION          => '学历',
        self::MATH_TYPE_ABROAD             => '海外经历',
        self::MATH_TYPE_COMPANY_NATURE     => '企业性质',
        self::MATH_TYPE_CATEGORY_JOB       => '职位类型',
        self::MATH_TYPE_ABROAD_AREA        => '海外地区',
        self::MATH_TYPE_ANNUAL_RECRUITMENT => '年度招聘',
        self::MATH_TYPE_ABROAD_QIUXIAN     => '求贤公告',
        self::MATH_TYPE_ABROAD_YINCAI      => '引才活动',
    ];

    public function __construct($announcementId)
    {
        $this->announcementId = $announcementId;

        $this->announcementModel = BaseAnnouncement::findOne($announcementId);
        if (!$this->announcementModel) {
            throw new Exception('公告不存在' . $announcementId);
        }

        $this->articleModel = BaseArticle::findOne($this->announcementModel->article_id);
        if (!$this->articleModel) {
            throw new Exception('公告不存在2' . $announcementId);
        }

        $this->companyModel = BaseCompany::findOne($this->announcementModel->company_id);
        if (!$this->companyModel) {
            throw new Exception('企业不存在');
        }

        $this->companyType   = $this->companyModel->type;
        $this->companyNature = $this->companyModel->nature;

        // 找到公告的全部属性（2.0版本后需要）
        $this->attribute = BaseArticleAttribute::find()
            ->select([
                'type',
            ])
            ->where([
                'article_id' => $this->articleModel->id,
            ])
            ->asArray()
            ->column();

        $cacheColumnList = BaseHomeColumn::getCache();
        foreach ($cacheColumnList as $column) {
            $this->allColunmList[$column['id']] = $column;
        }
    }

    /**
     * 运行
     */
    public function run()
    {
        $this->getJobList();
        $this->setMajor();
        $this->setCity();
        $this->setAloneCity();
        $this->setProvince();
        $this->setTalentSpecial1();
        $this->setTalentSpecial2();
        $this->setTalentBroad();
        $this->setPostdoc();
        $this->setCompanyGeneral();
        $this->setMedical();
        $this->setPublicInstitution();
        $this->setScientificResearchTalent();
        $this->setPrimaryAndSecondarySchool();
        $this->setCollege();
        $this->setAbroadArea();
        $this->setAnnualRecruitment();
        $this->setAbroadQinxian();
        $this->setAbroadYouqing();
        $this->setAbroadYincai();
        // ---v2---v2---v2
        //        $this->updateAnnouncementMiniAppType();
        //        $this->updateJobAnnouncementAmount();
        //        $this->updateJobAnnouncementRelation();
        //更新公告PI---v2
        //        $this->updateAnnouncementPiFlag();
        //        $this->updateStatAnnouncementCount();
        //更新博士后公告表---v2
        //        $this->updateAnnouncementBoShiHouTable();
        // 处理一下各端的详情缓存
        // frontendPc\models\Announcement::getDetailInfo
        // h5\models\Announcement::getDetailInfo
        // miniApp\models\Announcement::getDetailService
        $this->update();
        FrontendPcAnnouncement::getDetailInfo($this->announcementId, true);
        H5Announcement::getDetailInfo($this->announcementId, true);
        MiniAppAnnouncement::getDetailService($this->announcementId, BaseCommon::PLATFORM_MINI, 0, true);

        // 入队meilisearch
        Producer::meilisearch($this->announcementId, MeilisearchJob::TYPE_ANNOUNCEMENT);
    }

    private function getJobList()
    {
        // 找到所有合法的职位?
        $list = BaseJob::find()
            ->select([
                'id',
                'status',
                'education_type',
                'job_category_id',
                'major_id',
                'abroad_type',
                'city_id',
                'province_id',
                'amount',
                'title_type',
            ])
            ->where([
                'announcement_id' => $this->announcementId,
                'is_show'         => BaseJob::IS_SHOW_YES,
            ])
            ->andWhere([
                'status' => [
                    BaseJob::STATUS_ONLINE,
                    BaseJob::STATUS_OFFLINE,
                ],
            ])
            ->asArray()
            ->all();

        $tmpJobCategoryList = [];
        foreach ($list as &$item) {
            //判断一下
            if (empty($item['amount']) || $item['amount'] == '若干') {
                $item['amountNumber'] = 0;
            } else {
                $item['amountNumber'] = intval($item['amount']);
            }
            // 学历要求
            $education = $item['education_type'];
            // 海外经历要求
            $abroadType = $item['abroad_type'];
            // 城市id
            $cityId = $item['city_id'];
            // 省区id
            $provinceId = $item['province_id'];
            // 职位类型
            $jobCategoryId = $item['job_category_id'];
            // 专业要求(多个)
            $majorId = explode(',', $item['major_id']);

            if (!in_array($education, $this->educationList)) {
                $this->educationList[] = $education;
            }

            if (!in_array($jobCategoryId, $tmpJobCategoryList)) {
                $tmpJobCategoryList[] = $jobCategoryId;
            }

            if (!in_array($cityId, $this->cityList)) {
                $this->cityList[] = $cityId;
            }

            if (!in_array($provinceId, $this->provinceList)) {
                $this->provinceList[] = $provinceId;
            }

            if ($abroadType == BaseActiveRecord::BASE_TYPE_YES) {
                $this->abroadType = self::JOB_ABROAD_TYPE_YES;
            }

            $this->majorList = array_merge($majorId, $this->majorList);
            $this->majorList = array_unique($this->majorList);
            // 去0
            $this->majorList = array_filter($this->majorList);
        }

        $rs = BaseCategoryJob::find()
            ->select([
                'parent_id',
                'id',
                'name',
            ])
            ->where([
                'id' => $tmpJobCategoryList,

            ])
            ->asArray()
            ->all();

        foreach ($rs as $r) {
            if ($r['parent_id']) {
                // 有父级,就是等级二
                $this->categoryJobLevel2List[] = $r['id'];
                if (!in_array($r['parent_id'], $this->categoryJobLevel1List)) {
                    $this->categoryJobLevel1List[] = $r['parent_id'];
                }
            } else {
                // 没有父级,就是等级一
                if (!in_array($r['parent_id'], $this->categoryJobLevel1List)) {
                    $this->categoryJobLevel1List[] = $r['id'];
                }
            }
        }

        $this->jobList = $list;
    }

    private function setMajor()
    {
        $rule      = self::MAJOR_RULE;
        $majorList = $this->majorList;
        foreach ($majorList as $item) {
            if ($rule[$item]) {
                $this->setMath([
                    [
                        'type'  => self::MATH_TYPE_MAJOR,
                        'value' => $item,
                    ],
                ], $rule[$item]['columnId']);
            }
        }
    }

    private function setCity()
    {
        $rule = self::CITY_RULE;
        foreach ($this->cityList as $item) {
            if ($rule[$item]) {
                // 每一个城市都会涉及到很多很多的栏目需要分配
                foreach ($rule[$item]['list'] as $v) {
                    if ($v['companyType'] || $v['categoryJobLevel1']) {
                        if (in_array($this->companyType, $v['companyType'])) {
                            $this->setMath([
                                [
                                    'type'  => self::MATH_TYPE_COMPANY_TYPE,
                                    'value' => $this->companyType,
                                ],
                                [
                                    'type'  => self::MATH_TYPE_CITY,
                                    'value' => $item,
                                ],
                            ], $v['columnId']);
                        }

                        foreach ($this->categoryJobLevel1List as $categoryJobLevel1Id) {
                            if (in_array($categoryJobLevel1Id, $v['categoryJobLevel1'])) {
                                $this->setMath([
                                    [
                                        'type'  => self::MATH_TYPE_CATEGORY_JOB,
                                        'value' => $categoryJobLevel1Id,
                                    ],
                                    [
                                        'type'  => self::MATH_TYPE_CITY,
                                        'value' => $item,
                                    ],
                                ], $v['columnId']);
                            }
                        }
                    } else {
                        $this->setMath([
                            [
                                'type'  => self::MATH_TYPE_CITY,
                                'value' => $item,
                            ],
                        ], $v['columnId']);
                    }
                }
            }
        }
    }

    private function setAloneCity()
    {
        $rule = self::ALONE_CITY_RULE;

        foreach ($this->cityList as $item) {
            if ($rule[$item]) {
                $this->setMath([
                    [
                        'type'  => self::MATH_TYPE_CITY,
                        'value' => $item,
                    ],
                ], $rule[$item]['columnId']);
            }
        }
    }

    private function setProvince()
    {
        $rule = self::PROVINCE_RULE;
        foreach ($this->provinceList as $item) {
            if ($rule[$item]) {
                // 每一个城市都会涉及到很多很多的栏目需要分配
                foreach ($rule[$item]['list'] as $k => $v) {
                    // 如果有单位的性质要求或者职位类型的要啊
                    if ($v['companyType'] || $v['categoryJobLevel1']) {
                        if (in_array($this->companyType, $v['companyType'])) {
                            $this->setMath([
                                [
                                    'type'  => self::MATH_TYPE_COMPANY_TYPE,
                                    'value' => $this->companyType,
                                ],
                                [
                                    'type'  => self::MATH_TYPE_PROVINCE,
                                    'value' => $item,
                                ],
                            ], $v['columnId']);
                        }

                        foreach ($this->categoryJobLevel1List as $categoryJobLevel1Id) {
                            if (in_array($categoryJobLevel1Id, $v['categoryJobLevel1'])) {
                                $this->setMath([
                                    [
                                        'type'  => self::MATH_TYPE_CATEGORY_JOB,
                                        'value' => $categoryJobLevel1Id,
                                    ],
                                    [
                                        'type'  => self::MATH_TYPE_PROVINCE,
                                        'value' => $item,
                                    ],
                                ], $v['columnId']);
                            }
                        }
                    } else {
                        // 这种情况只需要符合企业类型就可以
                        $this->setMath([
                            [
                                'type'  => self::MATH_TYPE_PROVINCE,
                                'value' => $item,
                            ],
                        ], $v['columnId']);
                    }
                }
            }
        }
    }

    private function setTalentSpecial1()
    {
        $rule = self::TALENT_SPECIAL_RULE_1;
        foreach ($rule as $k => $item) {
            if ($this->companyType == $k) {
                $this->setMath([
                    [
                        'type'  => self::MATH_TYPE_COMPANY_TYPE,
                        'value' => $this->companyType,
                    ],
                ], $item['columnId']);
            }
        }
    }

    private function setTalentSpecial2()
    {
        $rule = self::TALENT_SPECIAL_RULE_2;
        foreach ($rule as $k => $item) {
            if (in_array($k, $this->educationList)) {
                $this->setMath([
                    [
                        'type'  => self::MATH_TYPE_EDUCATION,
                        'value' => $k,
                    ],
                ], $item['columnId']);
            }
        }
    }

    /**
     * 必须是有海外经历或者博士后
     */
    private function setTalentBroad()
    {
        $mathBase = [];
        if ($this->abroadType == self::JOB_ABROAD_TYPE_YES) {
            $mathBase[] = [
                'type'  => self::MATH_TYPE_ABROAD,
                'value' => self::JOB_ABROAD_TYPE_YES,
            ];
        }

        if (in_array(self::EDUCATION_DOCTOR_CODE, $this->educationList)) {
            $mathBase[] = [
                'type'  => self::MATH_TYPE_EDUCATION,
                'value' => self::EDUCATION_DOCTOR_CODE,
            ];
        }

        if ($mathBase[0]) {
            // 必须是有上述条件的其中一个,才允许
            $rule = self::TALENT_BROAD_RULE;
            // 规则需要转换一下
            foreach ($rule as $columnId => $provinceList) {
                foreach ($provinceList as $provinceId => $provinceName) {
                    if (in_array($provinceId, $this->provinceList)) {
                        $math = array_merge($mathBase, [
                            [
                                'type'  => self::MATH_TYPE_PROVINCE,
                                'value' => $provinceId,
                            ],
                        ]);
                        $this->setMath($math, $columnId);
                    }
                }
            }
        }
    }

    private function setPostdoc()
    {
        $mathBase = [];
        foreach (self::POSTDOC_LIST as $categoryJobItem) {
            if (in_array($categoryJobItem, $this->categoryJobLevel2List)) {
                $mathBase[] = [
                    'type'  => self::MATH_TYPE_CATEGORY_JOB,
                    'value' => $categoryJobItem,
                ];
            }
        }
        if ($mathBase[0]) {
            $rule = self::POSTDOC_RULE;
            foreach ($rule as $columnId => $companyTypeList) {
                if (in_array($this->companyType, $companyTypeList)) {
                    $math = array_merge($mathBase, [
                        [
                            'type'  => self::MATH_TYPE_COMPANY_TYPE,
                            'value' => $this->companyType,
                        ],
                    ]);
                    $this->setMath($math, $columnId);
                }
            }
        }
    }

    // 企业招聘
    private function setCompanyGeneral()
    {
        $rule = self::COMPANY_GENERAL_RULE;

        foreach ($rule as $columnId => $item) {
            $companyTypeList   = $item['companyType'];
            $companyNatureList = $item['companyNature'];
            $mathBase          = [];

            // 只有类型需要判断
            if ($companyTypeList && !$companyNatureList) {
                if (in_array($this->companyType, $companyTypeList)) {
                    $mathBase[] = [
                        'type'  => self::MATH_TYPE_COMPANY_TYPE,
                        'value' => $this->companyType,
                    ];
                }
            }

            // 只有性质需要判断
            if ($companyNatureList && !$companyTypeList) {
                if (in_array($this->companyNature, $companyNatureList)) {
                    $mathBase[] = [
                        'type'  => self::MATH_TYPE_COMPANY_NATURE,
                        'value' => $this->companyNature,
                    ];
                }
            }

            if ($companyNatureList && $companyTypeList) {
                if (in_array($this->companyType, $companyTypeList) && in_array($this->companyNature,
                        $companyNatureList)) {
                    $mathBase[] = [
                        'type'  => self::MATH_TYPE_COMPANY_TYPE,
                        'value' => $this->companyType,
                    ];
                }
            }

            if ($mathBase[0]) {
                $this->setMath($mathBase, $columnId);
            }
        }
    }

    private function setMedical()
    {
        $rule = self::MEDICAL_RULE;
        foreach ($rule as $columnId => $item) {
            $companyType      = $item['companyType'];
            $categoryJob      = $item['categoryJob'];
            $aloneCategoryJob = $item['aloneCategoryJob'];

            if ($aloneCategoryJob) {
                // 循环所有的职位类型
                // 只要符合职位类型就可以了
                foreach ($this->categoryJobLevel2List as $categoryJobLevel2Id) {
                    if (in_array($categoryJobLevel2Id, $aloneCategoryJob)) {
                        $this->setMath([
                            [
                                'type'  => self::MATH_TYPE_CATEGORY_JOB,
                                'value' => $categoryJobLevel2Id,
                            ],
                        ], $columnId);
                    }
                }
            } else {
                // 必须符合企业类型和职位类型
                if (in_array($this->companyType, $companyType)) {
                    $baseMath = [
                        [
                            'type'  => self::MATH_TYPE_COMPANY_TYPE,
                            'value' => $this->companyType,
                        ],
                    ];
                    if ($categoryJob) {
                        foreach ($this->categoryJobLevel2List as $categoryJobLevel2Id) {
                            if (in_array($categoryJobLevel2Id, $categoryJob)) {
                                $this->setMath(array_merge($baseMath, [
                                    [
                                        'type'  => self::MATH_TYPE_CATEGORY_JOB,
                                        'value' => $categoryJobLevel2Id,
                                    ],
                                ]), $columnId);
                            }
                        }
                    } else {
                        $this->setMath($baseMath, $columnId);
                    }
                }
            }
        }
    }

    private function setPublicInstitution()
    {
        $rule = self::PUBLIC_INSTITUTION_RULE;
        // 这里其实只有比较简单的,不过还是遵循规则来写,方便以后扩展
        foreach ($rule as $columnId => $item) {
            $companyType = $item['companyType'];
            if (in_array($this->companyType, $companyType)) {
                $this->setMath([
                    [
                        'type'  => self::MATH_TYPE_COMPANY_TYPE,
                        'value' => $this->companyType,
                    ],
                ], $columnId);
            }
        }
    }

    private function setScientificResearchTalent()
    {
        $rule = self::SCIENTIFIC_RESEARCH_TALENT_RULE;
        foreach ($rule as $columnId => $item) {
            $companyType = $item['companyType'];
            if (in_array($this->companyType, $companyType)) {
                $this->setMath([
                    [
                        'type'  => self::MATH_TYPE_COMPANY_TYPE,
                        'value' => $this->companyType,
                    ],
                ], $columnId);
            }
        }
    }

    private function setPrimaryAndSecondarySchool()
    {
        $rule = self::PRIMARY_AND_SECONDARY_SCHOOL_RULE;
        foreach ($rule as $columnId => $item) {
            $companyType = $item['companyType'];
            $categoryJob = $item['categoryJob'];
            // 这里是在 http://zentao.jugaocai.com/index.php?m=story&f=view&storyID=156&tid=thmqa4zq 里面新调整的逻辑
            $aloneCategoryJob = $item['aloneCategoryJob'];
            $aloneCompanyType = $item['aloneCompanyType'];
            if ($aloneCompanyType) {
                // 循环所有的职位类型
                // 只要符合职位类型就可以了
                if (in_array($this->companyType, $aloneCompanyType)) {
                    $this->setMath([
                        [
                            'type'  => self::MATH_TYPE_COMPANY_TYPE,
                            'value' => $this->companyType,
                        ],
                    ], $columnId);
                }
            }

            if ($aloneCategoryJob) {
                // 循环所有的职位类型
                // 只要符合职位类型就可以了
                foreach ($this->categoryJobLevel2List as $categoryJobLevel2Id) {
                    if (in_array($categoryJobLevel2Id, $aloneCategoryJob)) {
                        $this->setMath([
                            [
                                'type'  => self::MATH_TYPE_CATEGORY_JOB,
                                'value' => $categoryJobLevel2Id,
                            ],
                        ], $columnId);
                    }
                }
            }

            // 必须符合企业类型和职位类型
            if (in_array($this->companyType, $companyType)) {
                $baseMath = [
                    [
                        'type'  => self::MATH_TYPE_COMPANY_TYPE,
                        'value' => $this->companyType,
                    ],
                ];
                if ($categoryJob) {
                    foreach ($this->categoryJobLevel2List as $categoryJobLevel2Id) {
                        if (in_array($categoryJobLevel2Id, $categoryJob)) {
                            $this->setMath(array_merge($baseMath, [
                                [
                                    'type'  => self::MATH_TYPE_CATEGORY_JOB,
                                    'value' => $categoryJobLevel2Id,
                                ],
                            ]), $columnId);
                        }
                    }
                } else {
                    $this->setMath($baseMath, $columnId);
                }
            }
        }
    }

    private function setCollege()
    {
        $rule = self::COLLEGE_RULE;
        foreach ($rule as $columnId => $item) {
            $companyType       = $item['companyType'];
            $categoryJob       = $item['categoryJob'];
            $categoryJobLevel1 = $item['categoryJobLevel1'];
            if ($categoryJobLevel1) {
                // 循环所有的职位类型
                // 只要符合职位类型就可以了
                if (in_array($this->companyType, $companyType)) {
                    $baseMath = [
                        [
                            'type'  => self::MATH_TYPE_COMPANY_TYPE,
                            'value' => $this->companyType,
                        ],
                    ];
                    foreach ($this->categoryJobLevel1List as $categoryJobLevel1Id) {
                        if (in_array($categoryJobLevel1Id, $categoryJobLevel1)) {
                            $this->setMath(array_merge($baseMath, [
                                [
                                    'type'  => self::MATH_TYPE_CATEGORY_JOB,
                                    'value' => $categoryJobLevel1Id,
                                ],
                            ]), $columnId);
                        }
                    }
                }
            }
            // 必须符合企业类型和职位类型
            if (in_array($this->companyType, $companyType)) {
                $baseMath = [
                    [
                        'type'  => self::MATH_TYPE_COMPANY_TYPE,
                        'value' => $this->companyType,
                    ],
                ];
                foreach ($this->categoryJobLevel2List as $categoryJobLevel2Id) {
                    if (in_array($categoryJobLevel2Id, $categoryJob)) {
                        $this->setMath(array_merge($baseMath, [
                            [
                                'type'  => self::MATH_TYPE_CATEGORY_JOB,
                                'value' => $categoryJobLevel2Id,
                            ],
                        ]), $columnId);
                    }
                }
            }
        }
    }

    /**
     * 海外地区的规则
     */
    private function setAbroadArea()
    {
        $rule = self::ABROAD_AREA_RULE;
        foreach ($rule as $columnId => $item) {
            // 海外
            foreach ($this->provinceList as $provinceId) {
                if (in_array($provinceId, $item['areaId'])) {
                    $this->setMath([
                        'type'  => self::MATH_TYPE_ABROAD_AREA,
                        'value' => $provinceId,
                    ], $columnId);
                    break;
                }
            }
        }
    }

    /**
     * 年度招聘
     */
    private function setAnnualRecruitment()
    {
        $rule = self::ANNUAL_RECRUITMENT_RULE;
        // ①公告所在单位，单位类型为 双一流院校；或 普通本科院校；或 高职高专院校；或 党校与行政学院；
        // ②公告下在线状态 在招人数≥13 或 职位数量≥13；
        //职位的总数与招聘人数的总数
        // $jobCount    = 0;
        // $amountCount = 0;
        // foreach ($this->jobList as $job) {
        //     $jobCount++;
        //     $amountCount += $job['amountNumber'];
        // }
        foreach ($rule as $columnId => $item) {
            // if (in_array($this->companyType, $item['companyType']) && ($jobCount >= 13 || $amountCount >= 13)) {
            //     $this->setMath([
            //         [
            //             'type'  => self::MATH_TYPE_ANNUAL_RECRUITMENT,
            //             'value' => '',
            //         ],
            //     ], $columnId);
            // }
            // 24.4.10 改为直接按照单位类型 双一流或者普通本科院校
            if (in_array($this->companyType, $item['companyType'])) {
                $this->setMath([
                    [
                        'type'  => self::MATH_TYPE_COMPANY_TYPE,
                        'value' => $this->companyType,
                    ],
                ], $columnId);
            }
        }
    }

    // 求贤公告（海外栏目）
    private function setAbroadQinxian()
    {
        // 这里为了职位可以在对应栏目出现，所以特定需要加一个海外的缓存，来帮助做对应符合条件的公告，塞符合条件的职位
        $key = Cache::ALL_ABROAD_QIUXIAN_ANNOUNCEMENT_MATCH_JOB_KEY . ':' . $this->announcementId;
        // 首先删除
        Cache::delete($key);

        $rule = self::ABROAD_QIUXIAN_RULE;

        $matchStr = '';
        // 首先必须是合作单位，如果不是，直接return
        $cooperativeUnitRule = $rule['COOPERATIVE_UNIT'];

        if (!in_array($this->companyModel->is_cooperation, $cooperativeUnitRule)) {
            return;
        }

        $matchStr .= '符合合作单位,';

        // 单位性质
        $companyNatureRule = $rule['COMPANY_NATURE'];
        if (!in_array($this->companyNature, $companyNatureRule)) {
            return;
        }

        $matchStr .= '单位性质符合,';

        // 属性和单位类型是要同时匹配的
        $attRule = $rule['ATTRIBUTE'];
        $hasAtt  = false;

        foreach ($this->attribute as $item) {
            if (in_array($item, $attRule)) {
                $companyTypeRule = $rule['COMPANY_TYPE'][$item];
                if (!in_array($this->companyType, $companyTypeRule)) {
                    continue;
                }
                $hasAtt = true;
            }
        }
        if (!$hasAtt) {
            return;
        }

        $matchStr .= '公告包含属性符合,';
        $matchStr .= '单位类型符合,';
        // 公告里面必须包含此属性
        // 职位类型和职称，这就需要看职位的了，而且必须是某一个职位同时符合，而不能跨职位
        $categoryJobAndTitleRule = $rule['CATEGORY_JOB_AND_TITLE'];

        $matchCategoryJobAndTitleRule = false;
        $matchJobList                 = [];
        foreach ($this->jobList as $item) {
            if ($item['status'] != BaseJob::STATUS_ONLINE) {
                continue;
            }
            $education     = $item['education_type'];
            $title         = $item['title_type'];
            $jobCategoryId = $item['job_category_id'];

            foreach ($categoryJobAndTitleRule as $ruleItem) {
                if ($ruleItem['CATEGORY_JOB'] && $ruleItem['TITLE_TYPE']) {
                    // 职位必须符合两者，要不就标记为不合适
                    if (in_array($jobCategoryId, $ruleItem['CATEGORY_JOB']) && (in_array($title,
                                $ruleItem['TITLE_TYPE']) || in_array($education, $ruleItem['EDUCATION']))) {
                        // 符合要求，跳出整个循环
                        $matchCategoryJobAndTitleRule = true;
                        $matchJobList[]               = $item['id'];
                        $matchStr                     .= '职位' . $item['id'] . '符合职位类型+(职称 or 学历),';
                    }
                }

                if ($ruleItem['CATEGORY_JOB'] && !$ruleItem['TITLE_TYPE']) {
                    if (in_array($jobCategoryId, $ruleItem['CATEGORY_JOB']) && (in_array($education,
                            $ruleItem['EDUCATION']))) {
                        $matchCategoryJobAndTitleRule = true;
                        $matchJobList[]               = $item['id'];
                        $matchStr                     .= '职位' . $item['id'] . '符合职位类型+学历,';
                        $this->setRemark('职位' . $item['id'] . '符合职位类型');
                    }
                }
            }
        }

        if (!$matchCategoryJobAndTitleRule) {
            return;
        }

        Cache::set($key, json_encode($matchJobList));

        // 到这里就是符合标准的了
        $matchArray = [
            [
                'type'  => self::MATH_TYPE_ABROAD_QIUXIAN,
                'value' => $matchStr,
            ],
        ];

        $this->setMath($matchArray, self::ABROAD_QIUXIAN_COLUMN_ID);
    }

    private function setAbroadYouqing()
    {
        $rule = self::ABROAD_YOUQING_RULE;

        foreach ($rule as $columId => $item) {
            // 暂时就只有职位类型这么一个规则
            $isMatch     = false;
            $mathBase    = [];
            $ruleCateIds = $item['CATEGORY_JOB'];
            foreach ($ruleCateIds as $ruleCateId) {
                if (in_array($ruleCateId, $this->categoryJobLevel2List)) {
                    $isMatch    = true;
                    $mathBase[] = [
                        'type'  => self::MATH_TYPE_CATEGORY_JOB,
                        'value' => $ruleCateId,
                    ];
                }
            }

            if ($isMatch) {
                $this->setMath($mathBase, $columId);

                return;
            }
        }
    }

    private function setAbroadYincai()
    {
        $rule = self::ABROAD_YINCAI_RULE;

        // 只看公告属性
        $attRule = $rule['ATTRIBUTE'];
        $hasAtt  = false;
        foreach ($this->attribute as $item) {
            if (in_array($item, $attRule)) {
                $hasAtt = true;
            }
        }
        if (!$hasAtt) {
            return;
        }

        $matchStr = '';
        $matchStr .= '公告包含属性符合,';
        // 到这里就是符合标准的了
        $matchArray = [
            [
                'type'  => self::MATH_TYPE_ABROAD_YINCAI,
                'value' => $matchStr,
            ],
        ];

        $this->setMath($matchArray, self::ABROAD_YINCAI_COLUMN_ID);
    }

    // 根据不同的类型获取不同的符合条件的栏目的文案
    private function setMath($mathArray, $columnId)
    {
        $str = '';
        foreach ($mathArray as $item) {
            $type  = $item['type'];
            $value = $item['value'];
            switch ($type) {
                case self::MATH_TYPE_MAJOR:
                    $str .= '符合专业:' . Major::findOneVal(['id' => $value], 'name') . ',';
                    break;
                case self::MATH_TYPE_COMPANY_TYPE:
                    $name = BaseDictionary::getCompanyTypeName($value);
                    $str  .= '符合企业类型:' . $name . ',';
                    break;
                case self::MATH_TYPE_CITY:
                    $name = Area::findOneVal(['id' => $value], 'name');
                    $str  .= '符合城市:' . $name . ',';
                    break;
                case self::MATH_TYPE_PROVINCE:
                    $name = Area::findOneVal(['id' => $value], 'name');
                    $str  .= '符合省份:' . $name . ',';
                    break;
                case self::MATH_TYPE_EDUCATION:
                    $name = BaseDictionary::getEducationName($value);
                    $str  .= '符合学历要求:' . $name . ',';
                    break;
                case self::MATH_TYPE_COMPANY_NATURE:
                    $name = BaseDictionary::getCompanyNatureName($value);
                    $str  .= '符合企业性质:' . $name . ',';
                    break;
                case self::MATH_TYPE_CATEGORY_JOB:
                    $name = CategoryJob::findOneVal(['id' => $value], 'name');
                    $str  .= '符合职位类型:' . $name . ',';
                    break;
                case self::MATH_TYPE_ABROAD_AREA:
                    $str .= '符合海外地区规则';
                    break;
                case self::MATH_TYPE_ANNUAL_RECRUITMENT:
                    $str .= '符合年度招聘规则';
                    break;
                case self::MATH_TYPE_ABROAD_QIUXIAN:
                    $str .= $value . '符合求贤公告规则';
                    break;
            }
        }
        $columnName = $this->allColunmList[$columnId]['name'];
        $str        .= '分配到栏目id:' . $columnId . ',名字:' . $columnName;
        $this->setRemark($str);
        $this->setColumn($columnId);
    }

    private function setRemark($str)
    {
        $this->remark .= $str . PHP_EOL;
    }

    /**
     * 缓存一下?然后区分一级二级?
     */
    private function setColumn($columnId)
    {
        $detail = $this->allColunmList[$columnId];
        if ($detail['parent_id']) {
            // 有父级,说明是二级栏目
            $this->columnLevle2List[] = $columnId;
            // 如果父级不在一级栏目中,说明是一级栏目
            $this->columnLevle1List[] = $detail['parent_id'];

            $this->setRemark("新增{$detail['name']}的父栏目id:{$detail['parent_id']},名字:{$this->allColunmList[$detail['parent_id']]['name']}");
        } else {
            // 如果父级不在一级栏目中,说明是一级栏目
            $this->columnLevle1List[] = $columnId;
            // 没有父级,说明是一级栏目
        }
    }

    /**
     * 更新公告是否是小程序公告
     * @throws \Exception
     */
    public function UpdateAnnouncementMiniAppType()
    {
        try {
            if ($this->announcementModel->is_manual_tag = BaseAnnouncement::IS_MANUAL_TAG_NONE) {
                $model      = new RuleAnnouncement();
                $res        = $model->exec($this->announcementId);
                $is_miniapp = $res ? BaseAnnouncement::IS_MINIAPP_YES : BaseAnnouncement::IS_MINIAPP_NO;
            } elseif ($this->announcementModel->is_manual_tag = BaseAnnouncement::IS_MANUAL_TAG_YES) {
                $is_miniapp = BaseAnnouncement::IS_MINIAPP_YES;
            }
            if ($this->announcementModel->is_miniapp != $is_miniapp) {
                $this->announcementModel->is_miniapp = $is_miniapp;
                $this->announcementModel->save();
            }
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    public function updateJobAnnouncementAmount()
    {
        //有公告才更新
        if ($this->announcementId > 0) {
            try {
                $announcementModel                    = BaseAnnouncement::findOne($this->announcementId);
                $announcementModel->online_job_amount = BaseJob::find()
                    ->where([
                        'announcement_id' => $this->announcementId,
                        'status'          => BaseJob::STATUS_ONLINE,
                        'is_show'         => BaseJob::IS_SHOW_YES,
                    ])
                    ->count();
                $announcementModel->all_job_amount    = BaseJob::find()
                    ->where([
                        'announcement_id' => $this->announcementId,
                        'status'          => [
                            BaseJob::STATUS_ONLINE,
                            BaseJob::STATUS_OFFLINE,
                        ],
                        'is_show'         => BaseJob::IS_SHOW_YES,
                    ])
                    ->count();
                $announcementModel->save();

                //缓存公告招聘人数
                $key    = Cache::ALL_ANNOUNCEMENT_JOB_AMOUNT_KEY . ':' . $this->announcementId;
                $amount = BaseJob::find()
                    ->where([
                        'announcement_id' => $this->announcementId,
                        'status'          => BaseJob::STATUS_ONLINE,
                        'is_show'         => BaseJob::IS_SHOW_YES,
                        'amount'          => '若干',
                    ])
                    ->exists() ? '若干' : BaseJob::find()
                    ->where([
                        'announcement_id' => $this->announcementId,
                        'status'          => BaseJob::STATUS_ONLINE,
                        'is_show'         => BaseJob::IS_SHOW_YES,
                    ])
                    ->sum('amount');
                Cache::set($key, $amount);
            } catch (\Exception $e) {
                return $e->getMessage();
            }
        }
    }

    /**
     * 更新公告附属表的PI标识
     */
    private function updateAnnouncementPiFlag()
    {
        try {
            //先看下是否有附属记录
            $announcementExtraInfo = BaseAnnouncementExtra::findOne(['announcement_id' => $this->announcementId]);
            if (!$announcementExtraInfo) {
                //没有就补一下
                BaseAnnouncementExtra::insertData([
                    'announcement_id' => $this->announcementId,
                    'company_id'      => $this->announcementModel->company_id,
                ]);
                $announcementExtraInfo = BaseAnnouncementExtra::findOne(['announcement_id' => $this->announcementId]);
            }
            //看下自身是否有pi属性
            if (BaseArticleAttribute::find()
                    ->where([
                        'article_id' => $this->announcementModel->article_id,
                        'type'       => BaseArticleAttribute::ATTRIBUTE_PI,
                    ])
                    ->exists() || BaseCompanyFeatureTagRelation::find()
                    ->where([
                        'company_id'     => $this->announcementModel->company_id,
                        'feature_tag_id' => BaseCompanyFeatureTag::PI_TAG_ID,
                    ])
                    ->exists()) {
                $announcementExtraInfo->is_pi = BaseAnnouncementExtra::IS_PI_YES;
            } else {
                $announcementExtraInfo->is_pi = BaseAnnouncementExtra::IS_PI_NO;
            }
            if ($announcementExtraInfo->is_pay == BaseAnnouncementExtra::IS_PAY_NO || $announcementExtraInfo->is_boshihou_pay == BaseAnnouncementExtra::IS_PAY_NO) {
                BaseShowcase::updateCompanyStatIsPay([$this->announcementModel->company_id]);
            }

            $announcementExtraInfo->save();
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * 更新博士后公告表
     * @throws Exception
     */
    private function updateAnnouncementBoShiHouTable()
    {
        if (BaseAnnouncementBoshihou::find()
                ->where(['announcement_id' => $this->announcementId])
                ->exists() && !BaseJob::find()
                ->where([
                    'job_category_id' => [
                        29,
                        263,
                    ],
                    'status'          => [
                        BaseJob::STATUS_ONLINE,
                        BaseJob::STATUS_OFFLINE,
                    ],
                    'is_show'         => BaseJob::IS_SHOW_YES,
                    'announcement_id' => $this->announcementId,
                ])
                ->exists()) {
            //删除
            BaseAnnouncementBoshihou::deleteAll(['announcement_id' => $this->announcementId]);
        }
        if (!BaseAnnouncementBoshihou::find()
                ->where(['announcement_id' => $this->announcementId])
                ->exists() && BaseJob::find()
                ->where([
                    'job_category_id' => [
                        29,
                        263,
                    ],
                    'status'          => [
                        BaseJob::STATUS_ONLINE,
                        BaseJob::STATUS_OFFLINE,
                    ],
                    'is_show'         => BaseJob::IS_SHOW_YES,
                    'announcement_id' => $this->announcementId,
                ])
                ->exists()) {
            // 添加
            $model                  = new BaseAnnouncementBoshihou();
            $model->announcement_id = $this->announcementId;
            $model->save();
        }

        return true;
    }

    /**
     * 更新职位公告地区、学历中间表
     * @return string|void
     */
    public function updateJobAnnouncementRelation()
    {
        //有公告才更新
        if ($this->announcementId > 0) {
            try {
                //删除职位公告地区中间表数据
                BaseAnnouncementAreaRelation::deleteAll(['announcement_id' => $this->announcementId]);
                //删除职位公告学历中间表数据
                BaseAnnouncementEducationRelation::deleteAll(['announcement_id' => $this->announcementId]);
                //获取公告下所有职位(在线、下线)
                $jobList        = BaseJob::find()
                    ->select([
                        'province_id',
                        'city_id',
                        'education_type',
                    ])
                    ->where([
                        'announcement_id' => $this->announcementId,
                        'status'          => [
                            BaseJob::STATUS_ONLINE,
                            BaseJob::STATUS_OFFLINE,
                        ],
                        'is_show'         => BaseJob::IS_SHOW_YES,
                    ])
                    ->asArray()
                    ->all();
                $provinceIds    = array_unique(array_column($jobList, 'province_id'));
                $cityIds        = array_unique(array_column($jobList, 'city_id'));
                $educationCodes = array_unique(array_column($jobList, 'education_type'));

                //批量写入表中
                $areaInsert = [];
                foreach ($provinceIds as $provinceId) {
                    $item         = [
                        'announcement_id' => $this->announcementId,
                        'area_id'         => $provinceId,
                        'level'           => 1,
                    ];
                    $areaInsert[] = $item;
                }
                foreach ($cityIds as $cityId) {
                    $item         = [
                        'announcement_id' => $this->announcementId,
                        'area_id'         => $cityId,
                        'level'           => 2,
                    ];
                    $areaInsert[] = $item;
                }
                if ($areaInsert) {
                    BaseAnnouncementAreaRelation::getDb()
                        ->createCommand()
                        ->batchInsert(BaseAnnouncementAreaRelation::tableName(), [
                            'announcement_id',
                            'area_id',
                            'level',
                        ], $areaInsert)
                        ->execute();
                }

                //学历要求
                $educationInsert = [];
                foreach ($educationCodes as $educationCode) {
                    $item              = [
                        'announcement_id' => $this->announcementId,
                        'education_code'  => $educationCode,
                    ];
                    $educationInsert[] = $item;
                }
                if ($educationInsert) {
                    BaseAnnouncementEducationRelation::getDb()
                        ->createCommand()
                        ->batchInsert(BaseAnnouncementEducationRelation::tableName(), [
                            'announcement_id',
                            'education_code',
                        ], $educationInsert)
                        ->execute();
                }
            } catch (\Exception $e) {
                return $e->getMessage();
            }
        }
    }

    /**
     * 更新单位扩展表里公告数量
     * @return void
     */
    public function updateStatAnnouncementCount()
    {
        $announcementInfo = BaseAnnouncement::find()
            ->select(['company_id'])
            ->where([
                'id' => $this->announcementId,
            ])
            ->asArray()
            ->one();

        // 在线公告条件
        $onLineCount = BaseAnnouncement::find()
            ->alias('a')
            ->leftJoin(['art' => BaseArticle::tableName()], 'a.article_id = art.id')
            ->where([
                'a.company_id' => $announcementInfo['company_id'],
                'art.is_show'  => BaseArticle::IS_SHOW_YES,
                'art.status'   => BaseArticle::STATUS_ONLINE,
            ])
            ->count();

        $allCount = BaseAnnouncement::find()
            ->alias('a')
            ->leftJoin(['art' => BaseArticle::tableName()], 'a.article_id = art.id')
            ->where([
                'a.company_id' => $announcementInfo['company_id'],
                'art.is_show'  => BaseArticle::IS_SHOW_YES,
                'art.status'   => [
                    BaseArticle::STATUS_OFFLINE,
                    BaseArticle::STATUS_ONLINE,
                ],
            ])
            ->count();

        $statModel = BaseCompanyStatData::find()
            ->where(['company_id' => $announcementInfo['company_id']])
            ->one();

        $statModel->all_announcement_count    = $allCount;
        $statModel->online_announcement_count = $onLineCount;
        $statModel->save();
    }

    private function update()
    {
        $homeColumnId = $this->articleModel->home_column_id;
        $this->setRemark("主栏目id:$homeColumnId,名字:{$this->allColunmList[$homeColumnId]['name']}");
        $this->setColumn($homeColumnId);
        $homeSubColumnIds = explode(',', $this->articleModel->home_sub_column_ids);
        foreach ($homeSubColumnIds as $homeSubColumnId) {
            $this->setRemark("副栏目id:$homeSubColumnId,名字:{$this->allColunmList[$homeSubColumnId]['name']}");
            $this->setColumn($homeSubColumnId);
        }
        $oldColumnId  = BaseArticleColumn::find()
            ->where(['article_id' => $this->articleModel->id])
            ->select('column_id')
            ->column();
        $oldColumnArr = array_unique(array_merge([$homeColumnId], $homeSubColumnIds, $oldColumnId));
        $newColumnArr = array_unique(array_merge([$homeColumnId], $homeSubColumnIds, $this->columnLevle1List,
            $this->columnLevle2List));

        BaseArticleColumn::rebuild($this->articleModel->id, $newColumnArr);

        // 最后写日志
        $model                         = new AnnouncementAutoClassifyLog();
        $model->before_home_column_ids = implode(',', $oldColumnArr);
        $model->after_home_column_ids  = implode(',', $newColumnArr);
        $model->announcement_id        = $this->announcementId;
        $model->remark                 = $this->remark;
        $model->save();
        // 这个时候还需要触发一次职位相关的匹配
        foreach ($this->jobList as $item) {
            $jobAuto = new JobAutoClassify($item['id']);
            $jobAuto->run();
            // Producer::meilisearch($item['id'], MeilisearchJob::TYPE_JOB);
        }
    }
}