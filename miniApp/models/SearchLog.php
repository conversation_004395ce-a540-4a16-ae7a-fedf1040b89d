<?php

namespace miniApp\models;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseJob;
use common\base\models\BaseCompany;
use common\base\models\BaseResume;
use common\base\models\BaseSearchLog;
use common\helpers\PingYinHelper;
use common\libs\Cache;
use yii\base\Exception;

class SearchLog extends BaseSearchLog
{
    /**
     * 新增搜索
     * @throws Exception
     */
    public static function addSearchLog($keyWords): int
    {
        $keyWords['title'] = trim($keyWords['title']);
        $resumeId          = BaseResume::findOneVal(['member_id' => $keyWords['member_id']], 'id');
        $findNewOne        = BaseSearchLog::findOne([
            'resume_id' => $resumeId,
            'title'     => $keyWords['title'],
            'type'      => $keyWords['type'],
            'status'    => BaseSearchLog::STATUS_ACTIVE,
        ]);
        $addTime           = $keyWords['addTime'] ? date('Y-m-d H:i:s', $keyWords['addTime']) : CUR_DATETIME;

        if ($findNewOne) {
            $findNewOne->add_time = $addTime;
            if (!$findNewOne->save()) {
                throw new Exception($findNewOne->getFirstErrorsMessage());
            }

            return $findNewOne->id;
        }

        $data = [
            'add_time'      => $addTime,
            'status'        => BaseSearchLog::STATUS_ACTIVE,
            'title'         => $keyWords['title'],
            'resume_id'     => $resumeId,
            'platform_type' => BaseSearchLog::PLATFORM_TYPE_MINI_APP,
            'type'          => $keyWords['type'],
        ];

        return BaseSearchLog::createSearchLog($data);
    }

    /**
     * 批量新增搜索记录
     * @throws Exception
     */
    public static function addSearchListLog($keyWords): int
    {
        if (!$keyWords['list']) {
            return true;
        }
        $list = json_decode($keyWords['list'], true);
        if ($list) {
            foreach ($list as $item) {
                $temp = array_merge($item, [
                    'member_id' => $keyWords['member_id'],
                ]);
                SearchLog::addSearchLog($temp);
            }
        }

        return true;
    }

    /**
     * 删除搜索记录
     */
    public static function deleteSearchLog($memberId, $id): bool
    {
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');
        $model    = BaseSearchLog::findOne([
            'resume_id' => $resumeId,
            'id'        => $id,
        ]);
        if (!$model) {
            return true;
        }
        $model->status = BaseSearchLog::STATUS_DELETE;

        return $model->save();
    }


    /**
     * 删除所有搜索记录
     */
    public static function deleteAllSearchLog($memberId, $type)
    {
        $resumeId = BaseResume::findOneVal(['member_id' => $memberId], 'id');

        BaseSearchLog::updateAll(['status' => BaseSearchLog::STATUS_DELETE], [
            'resume_id' => $resumeId,
            'type'      => $type,
        ]);
    }

    /**
     * 获取用户搜索记录
     * @param $memberId
     * @return array
     */
    public static function getList($memberId): array
    {
        $resumeId    = BaseResume::findOneVal(['member_id' => $memberId], 'id');
        $miniAppList = BaseSearchLog::TYPE_LIST;
        $list        = [];
        foreach ($miniAppList as $key => $item) {
            $temp            = [];
            $temp['typeTxt'] = $item;
            $temp['type']    = $key;
            $temp['list']    = BaseSearchLog::getResumeSearchMess($resumeId, $key);
            $pinyin          = PingYinHelper::get($item);
            $list[$pinyin]   = $temp;
        }

        return $list;
    }

    /**
     * 检索结果页职位检索
     * @throws \Exception
     */
    public static function searchForJobList($keyWords): array
    {
        return BaseJob::searchForMiniAppList($keyWords);
    }

    /**
     * 检索结果页公告检索
     * @throws \Exception
     */
    public static function searchForAnnouncementList($keyWords): array
    {
        return BaseAnnouncement::searchForMiniAppList($keyWords);
    }

    /**
     * 检索结果页单位检索
     * @throws \Exception
     */
    public static function searchForCompanyList($keyWords): array
    {
        return BaseCompany::searchForMiniAppList($keyWords);
    }
}