<?php

namespace miniApp\controllers;

use common\base\models\BaseJob;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeWxBind;
use common\base\models\BaseShowcase;
use common\base\models\BaseShowcaseBrowseLog;
use common\libs\Cache;
use common\libs\WxPublic;
use common\service\CommonService;
use common\service\match\PersonToJobNewService;
use common\service\match\PersonToJobService;
use common\service\resumeRemind\ResumeRemindApplication;
use miniApp\models\Job;
use miniApp\models\Member;
use miniApp\models\Showcase;
use Yii;

class HomeController extends BaseMiniAppController
{
    /**
     * 首页广告
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionShowcase()
    {
        return $this->success([
            'banner'    => Showcase::getHomeBanner(),
            'quickLink' => Showcase::getHomeQuickLink(),
            'recommend' => Showcase::getHomeRecommend(),
        ]);
    }

    public function actionGetIconList()
    {
        return $this->success();
    }

    /**
     * 首页推荐职位
     * @throws \Exception
     */
    public function actionGetRecommendJobList()
    {
        // 内存开到1个g
        ini_set('memory_limit', '1024M');

        $keyWords             = Yii::$app->request->get();
        $keyWords['resumeId'] = $this->getResumeId();
        $memberId             = $this->memberId;

        $data = Job::getRecommendJobList($keyWords);
        //获取职位列表广告插入位置
        $showcaseInfo = [];
        if ($keyWords['p'] == 1 || !$keyWords['p']) {
            //            $showcaseInfo = BaseJob::getListShowcaseInfo(count($data), $memberId,
            //                BaseJob::VIP_SHOWCASE_POSITION_TYPE_MINI_JOB_LIST);
            $dataCount = count($data);
            if ($dataCount == 0) {
                $showcaseInfo = [
                    'position' => 0,
                    'list'     => [],
                ];
            } else {
                $showcaseInfo = [
                    'position' => min($dataCount, 3),
                    'list'     => BaseShowcase::getByKey('mini_shouye_A5'),
                ];
            }
        }

        return $this->success([
            'jobList'      => $data,
            'showcaseInfo' => $showcaseInfo,
        ]);
    }

    /**
     * 获取我的页面，默认现实文本
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetPersonPageDefaultTip()
    {
        $memberId = $this->memberId;
        if (!empty($memberId)) {
            $isLogin = true;
        } else {
            $isLogin = false;
        }

        $unLoginText             = '未登录';
        $unLoginTip              = '点击登录体验更多功能';
        $followTip               = '求职快人一步';
        $servicePhone            = '020-85611139'; //等待配置
        $vipTip                  = '高效求职 尊享11大权益';
        $subscriptionServiceIcon = '/subscription-service.png';

        return $this->success([
            'unLoginText'             => $unLoginText,
            'unLoginTip'              => $unLoginTip,
            'followTip'               => $followTip,
            'servicePhone'            => $servicePhone,
            'isLogin'                 => $isLogin,
            'vipTip'                  => $vipTip,
            'subscriptionServiceIcon' => $subscriptionServiceIcon,
        ]);
    }

    /**
     * 获取个人中心，未读提示红点/有反馈
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetUnReadTip()
    {
        //设置初始值
        $data     = [
            'hasFeedback' => false,
            'hasUnRead'   => false,
        ];
        $resumeId = $this->getResumeId();

        if ($resumeId) {
            //获取强提醒列表
            $list = ResumeRemindApplication::getInstance()
                ->getAll($resumeId);

            foreach ($list as $k => $item) {
                //任意一个未读，则红点提示
                if ($item > 0) {
                    $data['hasUnRead'] = true;
                }
                //如果存在投递反馈，提示有反馈
                if ($k == 'job_apply_all_count' && $item > 0) {
                    $data['hasFeedback'] = true;
                }
            }
        }

        return $this->success($data);
    }

    /**
     * 检查是否显示绑定弹窗
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCheckIsShowFollowQrCode()
    {
        //该弹窗7个自然内限弹出一次
        $cacheKey  = Cache::MINI_BIND_QRCODE_POP_KEY . ':' . $this->memberId;
        $cacheInfo = Cache::get($cacheKey);
        //判断是否已经关注
        $resumeId = $this->getResumeId();
        if (!$resumeId) {
            return $this->success(['isShow' => 2]);
        }

        $isSubscribeInfo = BaseResumeWxBind::find()
            ->where([
                'resume_id'    => $resumeId,
                'is_subscribe' => BaseResumeWxBind::IS_SUBSCRIBE_YES,
            ])
            ->one();

        $isShow = 1;
        if ($isSubscribeInfo || $cacheInfo) {
            $isShow = 2;
        }

        return $this->success(['isShow' => $isShow]);
    }

    /**
     * 获取关注弹框信息
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetFollowQrCodeInfo()
    {
        $isAutoClick = Yii::$app->request->get('isAutoClick') ?: false;
        try {
            $resumeId = $this->getResumeId();

            $qrCodeInfo         = WxPublic::getInstance(BaseMember::TYPE_PERSON)
                ->createBindQrCode($resumeId);
            $data['subtitle']   = '关注【高校人才网服务号】';
            $data['title']      = '求职效率翻倍';
            $data['text']       = [
                '投递进展及时提醒',
                '新职位发布抢先查看',
            ];
            $data['qrcode']     = $qrCodeInfo['url'];
            $data['qrcodeText'] = '长按识别二维码并关注';
            if ($isAutoClick) {
                $cacheKey = Cache::MINI_BIND_QRCODE_POP_KEY . ':' . $this->memberId;
                Cache::set($cacheKey, $this->memberId, 7 * 24 * 3600);
            }

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 我的页面获取信息
     * @return \yii\console\Response|\yii\web\Response
     * @throws \Exception
     */
    public function actionGetPersonInfo()
    {
        $memberId = $this->memberId;
        //获取用户信息
        $userInfo = Member::getMiniAppMemberInfo($memberId);

        $statInfo = Member::getMemberCount($memberId);

        return $this->success([
            'userInfo' => $userInfo,
            'statInfo' => $statInfo,
        ]);
    }

    public function actionGetJobToolInfo()
    {
        $memberId = $this->memberId;

        return $this->success(Member::getJobToolInfo($memberId));
    }

    /**
     * 检查用户是否扫码绑定微信了
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCheckIsBind()
    {
        $resumeId = $this->getResumeId();
        $isBind   = BaseResumeWxBind::checkWxBind($resumeId);
        if (!$isBind) {
            $isBind = 2;
        }

        return $this->success(['isBind' => $isBind]);
    }

    /**
     * 查看用户是否关注微信了
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCheckIsSubscribe()
    {
        $resumeId    = $this->getResumeId();
        $isSubscribe = BaseResumeWxBind::checkSubscribe($resumeId);

        return $this->success(['isSubscribe' => $isSubscribe]);
    }

    /**
     * 获取推荐职位列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetIntentionJobList()
    {
        try {
            // 这里开始临时被修改了 需要的时候就更换注释部分  ---------------------
            // $service = new PersonToJobService();
            // $params  = [
            //     'intentionId' => Yii::$app->request->get('intentionId'),
            //     'page'        => Yii::$app->request->get('page'),
            //     'type'        => Yii::$app->request->get('type'),
            //     'pageSize'    => Yii::$app->request->get('pageSize'),
            // ];
            // $data    = $service->setPlatform(CommonService::PLATFORM_MINI)
            //     ->init($params)
            //     ->run();

            $service = new PersonToJobNewService();
            $params  = [
                'intentionId' => Yii::$app->request->get('intentionId'),
                'page'        => Yii::$app->request->get('page'),
                'type'        => Yii::$app->request->get('type'),
                'pageSize'    => Yii::$app->request->get('pageSize'),
                'platform'    => CommonService::PLATFORM_MINI,
            ];

            if ($this->memberId) {
                $params['memberId'] = $this->memberId;
            }

            $data = $service->getList($params);

            //获取职位列表广告插入位置
            $data['showcaseInfo'] = [];
            if ($params['page'] == 1 || !$params['page']) {
                //                $memberId             = $this->memberId;
                //                $data['showcaseInfo'] = BaseJob::getListShowcaseInfo(count($data['list']), $memberId,
                //                    BaseJob::VIP_SHOWCASE_POSITION_TYPE_MINI_JOB_LIST);
                $dataCount = count($data['list']);
                if ($dataCount == 0) {
                    $data['showcaseInfo'] = [
                        'position' => 0,
                        'list'     => [],
                    ];
                } else {
                    $data['showcaseInfo'] = [
                        'position' => min($dataCount, 3),
                        'list'     => BaseShowcase::getByKey('mini_shouye_A5'),
                    ];
                }
            }

            // 这里开始临时被修改了 ---------------------

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionShowcaseClick()
    {
        // 点击广告位
        $memberId = $this->memberId;
        $id       = Yii::$app->request->get('id');
        $data     = [
            'memberId' => $memberId,
            'id'       => $id,
        ];
        BaseShowcaseBrowseLog::addMiniShowcaseBrowseLog($data);

        return $this->success();
    }

    /**
     * 获取页面tips的文案
     */
    public function actionGetServiceTips()
    {
        $result = [
            'title'             => '提示',
            'content'           => '使用“编制查询”可一键筛选有编制的职位/公告，捕捉每一次入编机会',
            'remark'            => '服务说明：可查询包含 行政编制/事业编制/备案制 等编制类型 的优质职位',
            'cancelButtonText'  => '取消',
            'confirmButtonText' => '了解更多',
        ];

        return $this->success($result);
    }

    /**
     * 获取扫码购买vip二维码
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetBuyGuideQrcode()
    {
        try {
            return $this->success(WxPublic::getInstance()
                ->createBuyGuideQrcode());
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionGetResumeCompletePopInfo()
    {
        try {
            $memberId = $this->memberId;
            $info     = BaseResume::getResumeCompletePopInfo($memberId);

            return $this->success($info);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionSetResumeCompletePopInfo()
    {
        try {
            $memberId = $this->memberId;
            BaseResume::setResumeCompletePopInfo($memberId);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}