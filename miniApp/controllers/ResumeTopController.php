<?php

namespace miniApp\controllers;

use common\service\CommonService;
use common\service\resume\ResumeTopService;
use Yii;
use yii\base\Exception;

class ResumeTopController extends BaseMiniAppController
{
    /**
     * 检查简历置顶
     */
    public function actionCheck()
    {
        try {
            $service = new ResumeTopService();
            $result  = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeTopService::OPERATION_TYPE_CHECK)
                ->init()
                ->run();

            return $this->success($result);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 简历置顶数据的合法性验证
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionValidate()
    {
        try {
            $params = Yii::$app->request->post();

            // 在这里会有一个小程序接收数组的问题，需要转换一下
            if ($params['set_date']) {
                $params['set_date'] = explode(',', $params['set_date']);
            } else {
                unset($params['set_date']);
            }

            $service = new ResumeTopService();
            $result  = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeTopService::OPERATION_TYPE_VALIDATE)
                ->init($params)
                ->run();

            return $this->success($result);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 添加简历置顶
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionAdd()
    {
        $params = Yii::$app->request->post();

        // 在这里会有一个小程序接收数组的问题，需要转换一下
        if ($params['set_date']) {
            $params['set_date'] = explode(',', $params['set_date']);
        } else {
            unset($params['set_date']);
        }

        //开启事务
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $service = new ResumeTopService();
            $result  = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(ResumeTopService::OPERATION_TYPE_ADD)
                ->init($params)
                ->run();
            $transaction->commit();

            return $this->success($result);
        } catch (Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }
}