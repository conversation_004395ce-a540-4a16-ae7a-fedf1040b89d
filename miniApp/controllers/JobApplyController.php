<?php

namespace miniApp\controllers;

use common\base\models\BaseJobApply;
use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeComplete;
use common\service\CommonService;
use common\service\resume\DeliveryService;
use yii\base\Exception;
use Yii;

class JobApplyController extends BaseMiniAppController
{
    /**
     * 公告下投递检查
     */
    public function actionCheckAnnouncementApply()
    {
        try {
            $service = new DeliveryService();
            $result  = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(DeliveryService::OPERATION_TYPE_CHECK_ANNOUNCEMENT_APPLY)
                ->init()
                ->run();

            return $this->success($result);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 检查用户状态
     */
    public function actionCheckUserApplyStatus()
    {
        try {
            $service = new DeliveryService();
            $result  = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(DeliveryService::OPERATION_TYPE_CHECK_APPLY)
                ->init()
                ->run();

            return $this->success($result);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 投递职位
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSubmit()
    {
        $transaction = \Yii::$app->db->beginTransaction();
        try {
            //调用服务
            $service = new DeliveryService();
            $result  = $service->setPlatform(CommonService::PLATFORM_MINI)
                ->setOparetion(DeliveryService::OPERATION_TYPE_APPLY)
                ->init()
                ->run();

            $transaction->commit();
            $data = [
                'toastType'            => $result['toast_type'] ?? '',
                'link'                 => $result['link'] ?? '',
                'successContentUp'     => $result['apply_success_tips'] ?? '',
                'successContentDown'   => $result['apply_success_qrcode_tips'] ?? '',
                'qrcodeLink'           => $result['qrcode_link'] ?? '',
                'applyStatus'          => $result['apply_status'],
                'wxBindQrCodeImageUrl' => $result['wxBindQrCodeImageUrl'] ?? '',
                'applySuccessMsg'      => $result['applySuccessMsg'] ?? '',
                'applyId'              => $result['applyId'] ?? '',
            ];

            return $this->success($data);
        } catch (\Exception $e) {
            $transaction->rollBack();

            return $this->fail($e->getMessage());
        }
    }

    /**
     * 检查用户简历完成度，用于投递前的检查
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCheckMemberCompleteStatus()
    {
        $memberId = $this->memberId;

        if (!$memberId) {
            return $this->fail('用户登陆状态错误');
        }
        $resumeId = $this->getResumeId();

        $completePercent = Yii::$app->params['completeResumePercent'];

        //获取用户简历完成度
        $percent = BaseResume::getComplete($memberId) ?: 0;

        if ($percent < $completePercent) {
            //获取用户简历完成步数
            $data['resumeStepNum'] = BaseResumeComplete::getResumeStep($resumeId);
            $data['title']         = '提示';
            $data['content']       = '您的在线简历完善度' . $percent . '%，简历完善度达' . $completePercent . '%方可投递。请先完善简历';

            return $this->success($data);
        } else {
            return $this->success();
        }
    }

    /**
     * 获取站内投递的列表
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetList()
    {
        try {
            $searchData             = Yii::$app->request->get();
            $searchData['memberId'] = $this->memberId;

            return $this->success(BaseJobApply::getApplyList($searchData, BaseJobApply::NEED_PAGE_INFO_YES));
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }
}
