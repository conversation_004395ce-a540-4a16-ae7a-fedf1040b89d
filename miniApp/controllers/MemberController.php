<?php

namespace miniApp\controllers;

use common\base\models\BaseCompany;
use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseResume;
use common\helpers\DebugHelper;
use common\libs\Captcha;
use common\libs\JwtAuth;
use common\libs\SmsQueue;
use common\libs\WxMiniApp;
use common\libs\WxPublic;
use common\service\chat\ChatApplication;
use frontendPc\models\MemberLoginForm;
use Yii;
use yii\base\Exception;
use yii\web\Response;

class MemberController extends BaseMiniAppController
{

    /**
     * 获取登录的验证码
     * @return \yii\console\Response|Response
     */
    public function actionSendMobileLoginCode()
    {

        $mobile     = Yii::$app->request->post('mobile');
        $mobileCode = Yii::$app->request->post('mobileCode');
        $ticket  = Yii::$app->request->post('ticket');
        $randStr = Yii::$app->request->post('randstr');
        // 先检验验证码
        if (!$randStr || !$ticket) {
            return $this->fail('图形验证码验证失败');
        }

        $captcha = new Captcha();
        if (!$captcha->check($ticket, $randStr)) {
            return $this->fail('图形验证码验证失败');
        }

        $type       = BaseMember::TYPE_PERSON;

        $loginForm = new BaseMemberLoginForm();

        $loginForm->mobileCode = $mobileCode;
        $loginForm->mobile     = $mobile;
        $loginForm->loginType  = BaseMemberLoginForm::LOGIN_TYPE_MOBILE;
        $loginForm->type       = $type;
        $loginForm->smsType    = SmsQueue::TYPE_LOGIN;

        try {
            $loginForm->sendMobileCode();

            return $this->success('验证码已发送，请注意查收');
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 验证登录验证码
     * @return \yii\console\Response|Response
     */
    public function actionValidateMobileLoginCode()
    {
        $mobile     = Yii::$app->request->post('mobile');
        $mobileCode = Yii::$app->request->post('mobileCode') ?: BaseMemberLoginForm::DEFAULT_MOBILE_CODE;
        $code       = Yii::$app->request->post('code');
        $type       = BaseMember::TYPE_PERSON;

        $loginForm = new BaseMemberLoginForm();

        $loginForm->mobileCode = $mobileCode;
        $loginForm->mobile     = $mobile;
        $loginForm->code       = $code;
        $loginForm->loginType  = BaseMemberLoginForm::LOGIN_TYPE_MOBILE;
        $loginForm->type       = $type;

        try {
            $data = $loginForm->validateMobileCode();

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 验证登录验证码(1.12新版使用的)
     * @return \yii\console\Response|Response
     */
    public function actionValidateMobileLoginMiniCode()
    {
        $mobile     = Yii::$app->request->post('mobile');
        $mobileCode = Yii::$app->request->post('mobileCode') ?: BaseMemberLoginForm::DEFAULT_MOBILE_CODE;
        $code       = Yii::$app->request->post('code');

        // 1.12新增
        $unionCode = Yii::$app->request->post('unionCode');
        $scene     = Yii::$app->request->post('scene') ?: '';
        $type      = BaseMember::TYPE_PERSON;

        // 判断参数必须
        if (!$mobile || !$code || !$unionCode) {
            return $this->fail('参数错误');
        }

        $loginForm = new BaseMemberLoginForm();

        $loginForm->mobileCode    = $mobileCode;
        $loginForm->mobile        = $mobile;
        $loginForm->code          = $code;
        $loginForm->loginType     = BaseMemberLoginForm::LOGIN_TYPE_MOBILE;
        $loginForm->miniUnionCode = $unionCode;
        $loginForm->miniScene     = $scene;
        $loginForm->type          = $type;

        try {
            $data = $loginForm->loginByMiniMobile();

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 账号密码登录
     */
    public function actionAccountLogin()
    {
        $account  = Yii::$app->request->post('account');
        $password = Yii::$app->request->post('password');

        $loginForm = new BaseMemberLoginForm();

        $loginForm->account  = $account;
        $loginForm->password = $password;
        $loginForm->type     = BaseMember::TYPE_PERSON;

        try {
            $data = $loginForm->accountLogin();

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    // 写一个给小程序获取code换取手机号登录的
    public function actionLoginByMiniCode()
    {
        $code = Yii::$app->request->post('code');
        if (!$code) {
            return $this->fail();
        }

        $loginForm = new BaseMemberLoginForm();

        $loginForm->code = $code;
        $loginForm->type = BaseMember::TYPE_PERSON;

        try {
            $data = $loginForm->loginByMiniCode();

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    // 微信小程用户code环境后端登录信息(这种是静默登录的code)
    //    public function actionLoginByMiniLoginCode()
    //    {
    //        $code = Yii::$app->request->post('code');
    //        if (!$code) {
    //            return $this->fail();
    //        }
    //
    //        $loginForm = new BaseMemberLoginForm();
    //
    //        $loginForm->code = $code;
    //        $loginForm->type = BaseMember::TYPE_PERSON;
    //
    //        try {
    //            $data = $loginForm->loginByMiniLoginCode();
    //
    //            return $this->success($data);
    //        } catch (\Exception $e) {
    //            return $this->fail($e->getMessage());
    //        }
    //    }

    public function actionRefreshToken()
    {
        // 前面都校验了,这里就直接刷新吧
        $jwt = new JwtAuth();
        $jwt->createToken($this->memberId);

        return $this->success([
            'token'      => $jwt->token,
            'expireTime' => $jwt->expireTime,
        ]);
    }

    /**
     *  微信登录
     */
    // public function actionGetWxUserInfo()
    // {
    //     $data = Yii::$app->request->post();
    //
    //     try {
    //         $client = new WxMini();
    //         $res    = $client->getJscode2Session($data);
    //
    //         return $this->success($res);
    //     } catch (\Exception $e) {
    //         return $this->fail($e->getMessage());
    //     }
    // }

    /**
     * 获取关注二维码
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCreateBindQrcode()
    {
        // 这里有两种可能,一种是求职者,一种是单位端
        $type = Yii::$app->user->identity->type;
        try {
            if (!$type) {
                throw new \Exception('用户类型不能为空');
            }
            if ($type == BaseMember::TYPE_PERSON) {
                $mainId = BaseResume::findOneVal(['member_id' => $this->memberId], 'id');
            } else {
                $mainId = BaseCompany::findOneVal(['member_id' => $this->memberId], 'id');
            }

            $qrCodeInfo = WxPublic::getInstance($type)
                ->createBindQrCode($mainId);
            $tipData    = [
                'title'      => '关注【高校人才网服务号】',
                'content'    => '即时获取求职进度',
                'buttonText' => '长按或扫码识别二维码',
            ];

            $data = array_merge($qrCodeInfo, $tipData);

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    public function actionChatDownload()
    {
        $memberId = $this->memberId;

        $messageId = Yii::$app->request->get('messageId');

        // 找这条消息是否超过了30天

        try {
            $app = ChatApplication::getInstance();

            // 这里直接是文件流了
            return $app->download($memberId, $messageId);
        } catch (\Exception $e) {
            // 这里是直接访问链接的,所以报错是需要调用js的window的alter来提示,点击确定关闭当前页面
            // 返回一个 404

            Yii::$app->response->setStatusCode(404)
                ->send();
            exit;
        }
    }

    /**
     * vip筛选部分获取用户信息
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetVipFilterInfo()
    {
        $data['isLogin'] = false;
        $data['isVip']   = false;
        $memberId        = $this->memberId;
        if (!empty($memberId)) {
            $data['isLogin'] = true;
            //判断是否是VIP
            $data['isVip'] = BaseResume::checkVip($memberId);
        }
        $data['url'] = '/vip.html';

        return $this->success($data);
    }

    /**
     * 通过code获取unionid，判断是否可以直接登录
     * @return \yii\console\Response|Response
     */
    public function actionCheckMiniCodeLogin()
    {
        $code  = Yii::$app->request->post('code');
        $scene = Yii::$app->request->post('scene') ?: '';

        $loginForm = new BaseMemberLoginForm();

        try {
            $data = $loginForm->loginByMiniLoginCode($code, $scene);

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 小程序code获取手机号
     * @return \yii\console\Response|Response
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function actionGetMobileInfoByMiniCode()
    {
        $code = Yii::$app->request->post('code');
        try {
            $miniApp = WxMiniApp::getInstance();
            //获取手机信息
            $mobileInfo = $miniApp->codeToMobileInfo($code);
            $mobile     = $mobileInfo['purePhoneNumber'] ?: '';
            $mobileCode = $mobileInfo['countryCode'] ?: '';

            return $this->success([
                'mobile'     => $mobile,
                'mobileCode' => $mobileCode,
            ]);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 小程序手机好快捷登录
     * 通过手机号code和unionCode两个参数来实现登录
     * @return \yii\console\Response|Response
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function actionLoginByMiniMobile()
    {
        $unionCode  = Yii::$app->request->post('unionCode');
        $mobileCode = Yii::$app->request->post('mobileCode');
        $scene      = Yii::$app->request->post('scene') ?: '';

        try {
            $loginForm       = new MemberLoginForm();
            $loginForm->type = BaseMember::TYPE_PERSON;

            if (!$unionCode || !$mobileCode) {
                throw new Exception('缺少必要参数');
            }

            $loginForm->miniScene     = $scene;
            $loginForm->miniUnionCode = $unionCode;
            $loginForm->minMobileCode = $mobileCode;
            $loginForm->loginType     = BaseMemberLoginForm::LOGIN_TYPE_MINI_APP;

            $data = $loginForm->loginByMiniMobile();

            return $this->success($data);
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 扫码小程序码，更新二维码状态
     * @return \yii\console\Response|Response
     */
    public function actionScanMiniLoginCode()
    {
        $scene = Yii::$app->request->post('scene');

        try {
            $app = WxMiniApp::getInstance();
            $app->updateLoginQrCodeStatus($scene, $app::LOGIN_QRCODE_STATUS_SCAN);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    /**
     * 小程序已经登录，通知pc登录
     * @return \yii\console\Response|Response
     */
    public function actionLoginCodeNotice()
    {
        $scene    = Yii::$app->request->post('scene');
        $memberId = $this->memberId;

        if (!$memberId) {
            Yii::error('非法扫码');
            // 403
            return $this->success();
        }

        try {
            $app = WxMiniApp::getInstance();
            $app->updateLoginQrCodeStatus($scene, $app::LOGIN_QRCODE_STATUS_LOGIN, $memberId);

            return $this->success();
        } catch (\Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

}