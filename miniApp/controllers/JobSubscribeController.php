<?php

namespace miniApp\controllers;

use common\service\jobSubscribe\JobSubscribeApplication;
use Yii;

class JobSubscribeController extends BaseMiniAppController
{
    /**
     * 保存订阅
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionSave()
    {
        $data = \Yii::$app->request->post();

        $data['resumeId'] = $this->getResumeId();

        $transaction = Yii::$app->db->beginTransaction();
        try {
            $service = new JobSubscribeApplication();

            $service->save($data);
            $transaction->commit();

            $email        = $data['sendEmail'];
            $isSendEmail  = $data['isSendEmail'];
            $isSendWechat = $data['isSendWechat'];
            $content      = '<p>您订阅的最新职位消息将推送至：<br>';
            if ($isSendEmail == 1 && $email) {
                // 箱：<EMAIL>；
                $content .= '邮箱：' . $email . '；';
            }

            if ($isSendWechat == 1) {
                // 微信：已绑定微信号；
                $content .= '微信；';
            }

            $content .= '<br>请注意查收</p>';

            return $this->success([
                'title'   => '职位订阅成功',
                'content' => $content,

            ]);
        } catch (\Exception $e) {
            $transaction->rollBack();
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

    /**
     * 获取职位订阅资料
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionGetInfo()
    {
        $resumeId = $this->getResumeId();

        if (!$resumeId) {
            return $this->success([]);
        }

        $service = new JobSubscribeApplication();

        $info = $service->getInfo($resumeId);

        return $this->success($info);
    }

    /**
     * 取消订阅
     * @return \yii\console\Response|\yii\web\Response
     */
    public function actionCancel()
    {
        $resumeId    = $this->getResumeId();
        $transaction = Yii::$app->db->beginTransaction();
        try {
            $service = new JobSubscribeApplication();

            $service->cancel($resumeId);

            // 提交事务
            $transaction->commit();

            return $this->success();
        } catch (\Exception $e) {
            $transaction->rollBack();
            $message = $e->getMessage();

            return $this->fail($message);
        }
    }

}