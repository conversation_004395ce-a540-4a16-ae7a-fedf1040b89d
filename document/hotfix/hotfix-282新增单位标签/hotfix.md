# ReadMe

### 禅道bug(罗列bug地址或者id)
- [需求282 学科专业分类优化 - 新官网 - 禅道 (ideaboat.cn)](http://zentao.jugaocai.com/index.php?m=story&f=view&id=282&tid=ujci9k4a)

***

### 参与人员

- 单文超

***

### 分支

|仓库|bug分支|备注|
|:----:|:----:|:----:|
|new_gaoxiao_yii|hotfix/#282_更新单位标签|-|



***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤| 是否执行 |执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|  -   |-|
|执行sql语句|  是   |见下方"执行sql语句"|
|更新后端代码|  是   |-|
|composer安装|  -   |见下方"composer安装"|
|更新配置|  -   |见下方"更新配置"|
|创建队列|  -   |见下方"创建队列"|
|执行脚本|  -   |见下方"执行脚本"|
|删除redis缓存|  -   |见下方"删除redis缓存"|
|重启队列|  -   |上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|  是   |-|
|添加定时任务|  -   |见下方"定时任务"|
|群内通知部署完毕|  -   |-|


#### 执行sql语句(按顺序执行)
 
- 参见alter_data.sql

