INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610001, '广告位ID', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610002, '打包ID', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610003, '是否打包', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610004, '广告位类型', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610005, '广告标题', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610006, '广告副标题', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610007, '广告跳转地址', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610008, '所属平台', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610009, '广告位置', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610010, '显示状态', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610011, '创建人', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610012, '创建时间', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610013, '广告状态', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610014, '关联单位ID', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610015, '关联单位名称', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610016, '生效时间', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610017, '失效时间', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610018, '次标题', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610019, '图片地址', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610020, '图片链接地址', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610021, '跳转类型', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610022, '页面类型', 1, 6, 1, '');
INSERT INTO `admin_statement_builder_title_config` (`id`, `code`, `title`, `status`, `tabs`, `type`, `remark`)
VALUES (null, 610023, '其他图片地址', 1, 6, 1, '');