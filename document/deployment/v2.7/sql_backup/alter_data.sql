ALTER TABLE `job_apply_record_extra`
    ADD COLUMN `announcement_id` int(11) NOT NULL DEFAULT 0 COMMENT '公告ID' AFTER `education_doctor`;

ALTER TABLE `job_apply_record_extra`
    ADD INDEX `idx_total` (`total`) USING BTREE;

ALTER TABLE `job`
    ADD INDEX `idx_real_refresh_time` (`real_refresh_time`);
CREATE TABLE `job_log`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT,
    `update_time`     datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `add_time`        datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_type`     tinyint(2)       NOT NULL DEFAULT '1' COMMENT '类型1=运营；2=单位',
    `create_id`       int(11)          NOT NULL DEFAULT '0' COMMENT '创建人ID',
    `create_name`     varchar(100)     NOT NULL DEFAULT '' COMMENT '创建人名称：运营记录运营名称；单位记录单位名称',
    `remark`          varchar(255)     NOT NULL DEFAULT '' COMMENT '备注',
    `route`           varchar(255)     NOT NULL DEFAULT '' COMMENT '控制器路由',
    `type`            int(11)          NOT NULL COMMENT '操作类型(4位数00前面两位代表大操作类型00后面两位代表小操作类型；例如：刷新1001；批量刷新1002)：1001=添加；1002=批量添加；1101=编辑；',
    `job_id`          int(11)          NOT NULL COMMENT '职位ID',
    `announcement_id` int(11)          NOT NULL COMMENT '公告ID',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_create_id` (`create_id`) USING BTREE,
    KEY `idx_create_type` (`create_type`) USING BTREE,
    KEY `idx_job_id` (`job_id`),
    KEY `idx_announcement_id` (`announcement_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;


CREATE TABLE `announcement_log`
(
    `id`              int(11) unsigned NOT NULL AUTO_INCREMENT,
    `update_time`     datetime         NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '修改时间',
    `add_time`        datetime         NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `create_type`     tinyint(2)       NOT NULL DEFAULT '1' COMMENT '类型1=运营；2=单位',
    `create_id`       int(11)          NOT NULL DEFAULT '0' COMMENT '创建人ID',
    `create_name`     varchar(100)     NOT NULL DEFAULT '' COMMENT '创建人名称：运营记录运营名称；单位记录单位名称',
    `route`           varchar(255)     NOT NULL DEFAULT '' COMMENT '控制器路由',
    `type`            int(11)          NOT NULL COMMENT '操作类型(4位数00前面两位代表大操作类型00后面两位代表小操作类型)：1001=添加；1002=批量添加；1101=编辑；',
    `remark`          varchar(255)     NOT NULL DEFAULT '' COMMENT '备注',
    `announcement_id` int(11)          NOT NULL COMMENT '公告ID',
    PRIMARY KEY (`id`) USING BTREE,
    KEY `idx_create_id` (`create_id`) USING BTREE,
    KEY `idx_create_type` (`create_type`) USING BTREE,
    KEY `idx_announcement_id` (`announcement_id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4;

ALTER TABLE `job`
    ADD COLUMN `audit_admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '审核人ID';

ALTER TABLE `job`
    ADD COLUMN `audit_admin_name` varchar(60) NOT NULL DEFAULT '' COMMENT '审核人名称';
ALTER TABLE `job`
    ADD INDEX `idx_audit_admin_id` (`audit_admin_id`) USING BTREE;
ALTER TABLE `job`
    ADD COLUMN `apply_admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '申请人ID';
ALTER TABLE `job`
    ADD COLUMN `apply_admin_name` varchar(60) NOT NULL DEFAULT '' COMMENT '申请人名称';
ALTER TABLE `job`
    ADD INDEX `idx_apply_admin_id` (`apply_admin_id`) USING BTREE;
ALTER TABLE `job`
    MODIFY COLUMN `duty` varchar(2300) NOT NULL DEFAULT '' COMMENT '岗位职责';
ALTER TABLE `job`
    MODIFY COLUMN `requirement` varchar(2300) NOT NULL DEFAULT '' COMMENT '任职要求';
ALTER TABLE `job`
    MODIFY COLUMN `remark` varchar(2300) NOT NULL DEFAULT '' COMMENT '其他说明';


ALTER TABLE `announcement`
    ADD COLUMN `audit_admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '审核人ID';
ALTER TABLE `announcement`
    ADD COLUMN `audit_admin_name` varchar(60) NOT NULL DEFAULT '' COMMENT '审核人名称';
ALTER TABLE `announcement`
    ADD INDEX `idx_audit_admin_id` (`audit_admin_id`) USING BTREE;
ALTER TABLE `announcement`
    ADD COLUMN `apply_admin_id` int(11) NOT NULL DEFAULT 0 COMMENT '申请人ID';
ALTER TABLE `announcement`
    ADD COLUMN `apply_admin_name` varchar(60) NOT NULL DEFAULT '' COMMENT '申请人名称';
ALTER TABLE `announcement`
    ADD INDEX `idx_apply_admin_id` (`apply_admin_id`) USING BTREE;
