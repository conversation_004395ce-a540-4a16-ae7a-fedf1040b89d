# ReadMe

### 关联产品原型版本
- [1.1.7_搜索引擎职位中心]([蓝湖 (lanhuapp.com)](https://lanhuapp.com/web/#/item/project/product?tid=8d951de4-aefb-40f7-954e-366683d68331&pid=3297b313-3594-4bab-88ba-39f451f8721c&versionId=f05356b4-aa04-4542-ba41-e7296fbb0a85&docId=a5910698-12dc-4578-afe5-24407db65426&docType=axure&pageId=91b21a6e505246fabb1db0d235778ca6&image_id=a5910698-12dc-4578-afe5-24407db65426&parentId=fec2f6e9-b5a7-428d-8e9b-95776afae8d4))

***

### 参与人员

- 单文超
- 杜孙鹤

***

### 分支

|仓库|开发分支|提測分支|备注|
|:----:|:----:|:----:|:----:|
|new_gaoxiao_yii|feature/Vv_1.1.7_seo职位中心|release/v_1.1.7_seo职位中心|-|



***

### 上线部署步骤

`内容为空,填写"-"即可`

|步骤|是否执行|执行内容|
|:----|:----:|:----|
|提醒前端提前合并代码到master和添加权限节点|-|-|
|执行sql语句|是|见下方"执行sql语句"|
|更新后端代码|是|-|
|composer安装|是|见下方"composer安装"|
|更新配置|-|见下方"更新配置"|
|创建队列|-|见下方"创建队列"|
|执行脚本|是|见下方"执行脚本"|
|删除redis缓存|-|见下方"删除redis缓存"|
|重启队列|-|上线前, 暂停所有队列, 上线后再重启|
|更新前端代码|-|-|
|添加定时任务|是|见下方"定时任务"|
|群内通知部署完毕|是|-|

#### 执行sql语句(按顺序执行)

* alter_data.sql

#### 添加广告位
* 广告位名称：职位中心B页面右侧
* 位置编号：zhiweizhongxin_B_youce
* 广告数量：2