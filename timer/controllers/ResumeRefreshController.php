<?php

namespace timer\controllers;

use common\base\models\BaseMember;
use common\base\models\BaseResume;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseResumeEquitySetting;
use common\base\models\BaseResumeRefresh;

/**
 * 处理简历自动刷新业务类
 */
class ResumeRefreshController extends BaseTimerController
{
    /**
     * 简历自动刷新
     * php timer_yii resume-refresh/run
     */
    public function actionRun()
    {
        //先获取有刷新权益的简历ID
        $resumeIds = BaseResumeEquityPackage::find()
            ->andWhere([
                'equity_id'     => BaseResumeEquitySetting::ID_RESUME_REFRESH,
                'expire_status' => BaseResumeEquityPackage::STATUS_EXPIRE,
            ])
            ->andWhere([
                '>',
                'expire_time',
                date('Y-m-d H:i:s'),
            ])
            ->select('resume_id')
            ->distinct()
            ->column();

        $count = count($resumeIds);
        self::log('今天有' . $count . '个简历需要自动刷新！');
        if ($count > 0) {
            foreach ($resumeIds as $resumeId) {
                //刷新简历
                $resume_info                   = BaseResume::findOne($resumeId);
                $resume_info->refresh_time     = date('Y-m-d H:i:s');
                $resume_info->last_update_time = date('Y-m-d H:i:s');
                $resume_info->save();
                //更新活跃时间
                $member_info                   = BaseMember::findOne($resume_info->member_id);
                $member_info->last_active_time = date('Y-m-d H:i:s');
                $member_info->save();
                $refresh_model              = new BaseResumeRefresh();
                $refresh_model->resume_id   = $resumeId;
                $refresh_model->return_type = BaseResumeRefresh::TYPE_SYSTEM;
                $refresh_model->return_data = '';
                $refresh_model->save();
                self::log('简历ID：' . $resumeId . '完成简历刷新');
            }
        }

        self::log('完成今天自动刷新操作，结束执行！');
    }
}