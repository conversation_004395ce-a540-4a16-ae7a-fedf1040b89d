<?php

namespace timer\controllers;

use common\base\models\BaseJob;
use common\libs\Cache;
use common\service\meilisearch\job\AddService;

class InsertJobToMeilisearchController extends BaseTimerController
{
    public function actionAdd()
    {
        $cacheKey  = 'meilisearch_insert_job_id';
        $currentId = Cache::get($cacheKey) ?: 0;
        $jobList   = BaseJob::find()
            ->where([
                '>',
                'id',
                $currentId,
            ])
            ->select('id')
            ->orderBy('id asc')
            ->limit(1000)
            ->asArray()
            ->column();
        $maxId     = max($jobList);
        if (!empty($jobList)) {
            $model = new AddService();
            foreach ($jobList as $item) {
                self::log('处理职位：'.$item);
                $model->saveById($item);
                self::log('插入职位：'.$item.'，成功');
            }
            Cache::set($cacheKey, $maxId);
        }
    }

}