<?php

namespace timer\controllers;

use common\base\models\BaseAnnouncement;
use common\base\models\BaseCompany;
use common\base\models\BaseHwActivity;
use common\base\models\BaseHwActivityPromotion;
use common\base\models\BaseHwActivitySession;
use common\base\models\BaseHwSpecialActivity;
use common\base\models\BaseJob;
use common\service\abroadColumn\ComeHomeService;
use common\service\abroadColumn\CompanyService;
use common\service\abroadColumn\ExcellentYouthService;
use common\service\abroadColumn\HomeService;
use common\service\abroadColumn\OverseasTalentService;
use common\service\abroadColumn\QiuxianService;
use common\service\hwActivity\HwService;
use common\service\zhaoPinHuiColumn\SpecialActivityService;
use queue\Producer;
use yii\db\conditions\AndCondition;

/**
 *
 */
class HwController extends BaseTimerController
{

    // 更新单位排序积分(建议一个小时更新一次)
    // php timer_yii hw/update-overseas-point
    public function actionUpdateOverseasPoint()
    {
        $list = BaseCompany::find()
            ->where([
                'is_abroad' => BaseCompany::IS_ABROAD_YES,
                'status'    => BaseCompany::STATUS_ACTIVE,
                'is_hide'   => BaseCompany::IS_HIDE_NO,
            ])
            ->select([
                'id',
                'type',
                'member_id',
                'is_cooperation',
            ])
            ->asArray()
            ->all();
        self::log('共有' . count($list) . '个单位');
        //定义单位类型的排名积分

        //单位类型对应的积分
        $typePointList = [
            //双一流院校
            '1'  => 3,
            //普通本科院校
            '2'  => 2,
            //中国科学院系统
            '16' => 2,
        ];
        foreach ($list as &$item) {
            self::log('开始更新单位id：' . $item['id']);
            //获取在线公告数量
            $onlineAnnouncementAmount = BaseAnnouncement::getCompanyOnLineAnnouncementAmount($item['id']);
            //获取职位数量
            $onlineJobAmount = BaseJob::getCompanyJobAmount($item['id']);

            if ($onlineAnnouncementAmount || $onlineJobAmount) {
                $point = 100;
            } else {
                $point = 0;
            }

            if ($item['is_cooperation'] == BaseCompany::COOPERATIVE_UNIT_YES) {
                $point += 1000;
            }

            switch ($item['type']) {
                case 1:
                    $point += $typePointList['1'];
                    break;
                case 2:
                    $point += $typePointList['2'];
                    break;
                case 16:
                    $point += $typePointList['16'];
                    break;
            }
            $model                      = BaseCompany::findOne($item['id']);
            $model->overseas_sort_point = $point;
            $model->save();
            self::log('更新单位id：' . $item['id'] . ',成功');
        }
    }

    // 更新海外活动主状态和场次状态(建议每分钟执行一次)
    // php timer_yii hw/update-activity-status
    public function actionUpdateActivityStatus($isReloadData = 2)
    {
        $list = BaseHwActivity::find()
            ->where([
                'status' => BaseHwActivity::STATUS_ACTIVE,
            ])
            ->andWhere([
                '!=',
                'activity_status',
                BaseHwActivity::ACTIVITY_STATUS_END,
            ])
            ->select('id')
            ->asArray()
            ->all();

        // 当前时间，如果三十分就同步一次活动关联单位
        $minute = date('i');

        foreach ($list as $item) {
            $id = $item['id'];
            BaseHwActivity::updateActivityBySession($id);
            self::log('更新活动ID：' . $id . ',成功');

            if ($minute == '30' || $isReloadData == 1) {
                // 更新对应场次排序逻辑
                Producer::hwActivityCompanyQueue($id);
            }

        }
    }

    // 更新推广位状态
    // php timer_yii hw/update-ad-position-status
    public function actionUpdateAdPositionStatus()
    {
        $today = CUR_DATE;

        $list = BaseHwActivityPromotion::find()
            ->where(['status' => BaseHwActivityPromotion::STATUS_OFFLINE])
            ->andWhere(['start_date' => $today])
            ->select('id')
            ->asArray()
            ->all();

        // 找到符合上线条件的推广位
        foreach ($list as $item) {
            $model         = BaseHwActivityPromotion::findOne($item['id']);
            $model->status = BaseHwActivityPromotion::STATUS_ONLINE;
            $model->save();
            self::log('更新推广位id：' . $item['id'] . ',上线成功)');
        }

        $list = BaseHwActivityPromotion::find()
            ->where(['status' => BaseHwActivityPromotion::STATUS_ONLINE])
            ->andWhere(['end_date' => $today])
            ->select('id')
            ->asArray()
            ->all();

        // 找到符合下线条件的推广位
        foreach ($list as $item) {
            $model         = BaseHwActivityPromotion::findOne($item['id']);
            $model->status = BaseHwActivityPromotion::STATUS_OFFLINE;
            $model->save();
            self::log('更新推广位id：' . $item['id'] . ',下线成功)');
        }
    }

    // 更新缓存
    // php timer_yii hw/update-cache
    public function actionUpdateCache()
    {
        (new ComeHomeService())->getAll(true);
        (new CompanyService())->getAll(true);
        (new ExcellentYouthService())->getAll(true);
        (new HomeService())->getAll(true);
        (new OverseasTalentService())->getAll(true);
        (new QiuxianService())->getAll(true);
    }

    /**
     * 将未结束且没有自定义时间的专场更新状态和关联活动单位数量
     * php timer_yii hw/update-special-activity
     * @return void
     */
    public function actionUpdateSpecialActivity()
    {
        $where        = [
            [
                '<>',
                'status',
                BaseHwSpecialActivity::STATUS_COMPLETED,
            ],
            [
                '=',
                'custom_time',
                '',
            ],
        ];
        $activityList = BaseHwSpecialActivity::find()
            ->select(['id'])
            ->andWhere(new AndCondition($where))
            ->asArray()
            ->all();

        foreach ($activityList as $item) {
            SpecialActivityService::syncSpecialActivity($item['id']);
        }
    }

}
