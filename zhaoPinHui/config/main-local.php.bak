<?php

$config = [
    'as cors'    => [
        'class' => \yii\filters\Cors::className(),
        'cors'  => [
            'Origin'                           => [
                'http://127.0.0.1:5501',
                'http://127.0.0.1:5500',
                'http://frp.gc.ideaboat.cn',
                'http://127.0.0.1:53757',
                'http://frp2.gc.jugaocai.com',
            ],
            'Access-Control-Request-Method'    => [
                'GET',
                'POST',
                'PUT',
                'PATCH',
                'DELETE',
                'HEAD',
                'OPTIONS',
            ],
            'Access-Control-Request-Headers'   => ['*'],
            'Access-Control-Allow-Credentials' => true,
            'Access-Control-Max-Age'           => 86400,
            'Access-Control-Expose-Headers'    => [],

        ],
    ],
    'components' => [
        'request' => [
            'cookieValidationKey' => '4tCyb80HqgFCczncTIzrxNfT0ecglI2m',
        ],
        'user'    => [
            'class'           => 'yii\web\User',
            'identityClass'   => 'common\base\models\BaseMember',
            'enableAutoLogin' => true,
            'identityCookie'  => [
                'name'     => '_identity',
                'httpOnly' => true,
                'domain'   => '.gaoxiaojob.com',
                // 设置为共享的主域名
            ],
            'idParam'         => '__member',
        ],
        'session' => [
            'name'         => 'advanced',
            'cookieParams' => [
                'domain' => '.gaoxiaojob.com',
                // 设置为共享的主域名
            ],
        ],
    ],
];

if (!YII_ENV_TEST) {
    $config['bootstrap'][]      = 'debug';
    $config['modules']['debug'] = [
        'class' => 'yii\debug\Module',
    ];

    $config['bootstrap'][]    = 'gii';
    $config['modules']['gii'] = [
        'class' => 'yii\gii\Module',
    ];
}

return $config;
